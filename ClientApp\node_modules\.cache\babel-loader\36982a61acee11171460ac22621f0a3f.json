{"ast": null, "code": "import api from\"./Api\";import{BASE_URL}from\"./Context/config\";export const EditContact=async newContact=>{const data={Id:newContact.Id,Category:newContact.Category,ContactInfo:newContact.ContactInfo,Title:newContact.Title,isPublic:newContact.isPublic};if(newContact.isPublic===true)data.isPublic=true;else data.isPublic=false;try{const response=await api.put(`${BASE_URL}/Contact/EditContact`,data,{headers:{\"Content-Type\":\"application/json\"}});return response;}catch(error){console.log(\"Error:\",error.message);}};export const DeleteContact=async Id=>{try{const response=await api.delete(`${BASE_URL}/Contact/DeleteContact/${Id}`,{headers:{\"Content-Type\":\"application/json\"}});return response;}catch(error){throw error;}};export const CreateContact=async newContact=>{var _data$ContactInfo;const data={UserId:newContact.UserId,Category:newContact.Category||\"CvFile\",ContactInfo:newContact.ContactInfo,Title:newContact.Title,isPublic:newContact.isPublic};if(newContact.isPublic===true)data.isPublic=true;else data.isPublic=false;console.log(\"CreateContact - Sending data:\",data);console.log(\"CreateContact - ContactInfo length:\",(_data$ContactInfo=data.ContactInfo)===null||_data$ContactInfo===void 0?void 0:_data$ContactInfo.length);console.log(\"CreateContact - ContactInfo type:\",typeof data.ContactInfo);try{const response=await api.post(`${BASE_URL}/Contact/CreateContact`,data,{headers:{\"Content-Type\":\"application/json\"}});return response;}catch(error){var _error$response,_error$response2;console.error(\"Error creating contact:\",error);console.error(\"Error response:\",(_error$response=error.response)===null||_error$response===void 0?void 0:_error$response.data);console.error(\"Error status:\",(_error$response2=error.response)===null||_error$response2===void 0?void 0:_error$response2.status);throw error;}};", "map": {"version": 3, "names": ["api", "BASE_URL", "EditContact", "newContact", "data", "Id", "Category", "ContactInfo", "Title", "isPublic", "response", "put", "headers", "error", "console", "log", "message", "DeleteContact", "delete", "CreateContact", "_data$ContactInfo", "UserId", "length", "post", "_error$response", "_error$response2", "status"], "sources": ["C:/Users/<USER>/Desktop/iDigitalX/ClientApp/src/ContactData.ts"], "sourcesContent": ["import api from \"./Api\";\r\nimport { BASE_URL } from \"./Context/config\";\r\n\r\nexport interface PutContact {\r\n  Id: number;\r\n  Category: string;\r\n  ContactInfo: string;\r\n  Title?: string;\r\n  isPublic: boolean;\r\n}\r\n\r\nexport interface PostContact {\r\n  UserId: number;\r\n  ContactInfo: string;\r\n  Category: string;\r\n  Title?: string;\r\n  isPublic: boolean;\r\n}\r\n\r\nexport const EditContact = async (newContact: PutContact) => {\r\n  const data = {\r\n    Id: newContact.Id,\r\n    Category: newContact.Category,\r\n    ContactInfo: newContact.ContactInfo,\r\n    Title: newContact.Title,\r\n    isPublic: newContact.isPublic,\r\n  };\r\n\r\n  if (newContact.isPublic === true) data.isPublic = true;\r\n  else data.isPublic = false;\r\n\r\n  try {\r\n    const response = await api.put(`${BASE_URL}/Contact/EditContact`, data, {\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.log(\"Error:\", error.message);\r\n  }\r\n};\r\n\r\nexport const DeleteContact = async (Id: number) => {\r\n  try {\r\n    const response = await api.delete(\r\n      `${BASE_URL}/Contact/DeleteContact/${Id}`,\r\n      {\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n      }\r\n    );\r\n\r\n    return response;\r\n  } catch (error) {\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const CreateContact = async (newContact: PostContact) => {\r\n  const data = {\r\n    UserId: newContact.UserId,\r\n    Category: newContact.Category || \"CvFile\",\r\n    ContactInfo: newContact.ContactInfo,\r\n    Title: newContact.Title,\r\n    isPublic: newContact.isPublic,\r\n  };\r\n\r\n  if (newContact.isPublic === true) data.isPublic = true;\r\n  else data.isPublic = false;\r\n\r\n  console.log(\"CreateContact - Sending data:\", data);\r\n  console.log(\"CreateContact - ContactInfo length:\", data.ContactInfo?.length);\r\n  console.log(\"CreateContact - ContactInfo type:\", typeof data.ContactInfo);\r\n\r\n  try {\r\n    const response = await api.post(`${BASE_URL}/Contact/CreateContact`, data, {\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Error creating contact:\", error);\r\n    console.error(\"Error response:\", error.response?.data);\r\n    console.error(\"Error status:\", error.response?.status);\r\n    throw error;\r\n  }\r\n};\r\n"], "mappings": "AAAA,MAAO,CAAAA,GAAG,KAAM,OAAO,CACvB,OAASC,QAAQ,KAAQ,kBAAkB,CAkB3C,MAAO,MAAM,CAAAC,WAAW,CAAG,KAAO,CAAAC,UAAsB,EAAK,CAC3D,KAAM,CAAAC,IAAI,CAAG,CACXC,EAAE,CAAEF,UAAU,CAACE,EAAE,CACjBC,QAAQ,CAAEH,UAAU,CAACG,QAAQ,CAC7BC,WAAW,CAAEJ,UAAU,CAACI,WAAW,CACnCC,KAAK,CAAEL,UAAU,CAACK,KAAK,CACvBC,QAAQ,CAAEN,UAAU,CAACM,QACvB,CAAC,CAED,GAAIN,UAAU,CAACM,QAAQ,GAAK,IAAI,CAAEL,IAAI,CAACK,QAAQ,CAAG,IAAI,CAAC,IAClD,CAAAL,IAAI,CAACK,QAAQ,CAAG,KAAK,CAE1B,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAV,GAAG,CAACW,GAAG,CAAC,GAAGV,QAAQ,sBAAsB,CAAEG,IAAI,CAAE,CACtEQ,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CACF,CAAC,CAAC,CAEF,MAAO,CAAAF,QAAQ,CACjB,CAAE,MAAOG,KAAK,CAAE,CACdC,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAEF,KAAK,CAACG,OAAO,CAAC,CACtC,CACF,CAAC,CAED,MAAO,MAAM,CAAAC,aAAa,CAAG,KAAO,CAAAZ,EAAU,EAAK,CACjD,GAAI,CACF,KAAM,CAAAK,QAAQ,CAAG,KAAM,CAAAV,GAAG,CAACkB,MAAM,CAC/B,GAAGjB,QAAQ,0BAA0BI,EAAE,EAAE,CACzC,CACEO,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CACF,CACF,CAAC,CAED,MAAO,CAAAF,QAAQ,CACjB,CAAE,MAAOG,KAAK,CAAE,CACd,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED,MAAO,MAAM,CAAAM,aAAa,CAAG,KAAO,CAAAhB,UAAuB,EAAK,KAAAiB,iBAAA,CAC9D,KAAM,CAAAhB,IAAI,CAAG,CACXiB,MAAM,CAAElB,UAAU,CAACkB,MAAM,CACzBf,QAAQ,CAAEH,UAAU,CAACG,QAAQ,EAAI,QAAQ,CACzCC,WAAW,CAAEJ,UAAU,CAACI,WAAW,CACnCC,KAAK,CAAEL,UAAU,CAACK,KAAK,CACvBC,QAAQ,CAAEN,UAAU,CAACM,QACvB,CAAC,CAED,GAAIN,UAAU,CAACM,QAAQ,GAAK,IAAI,CAAEL,IAAI,CAACK,QAAQ,CAAG,IAAI,CAAC,IAClD,CAAAL,IAAI,CAACK,QAAQ,CAAG,KAAK,CAE1BK,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAEX,IAAI,CAAC,CAClDU,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAAK,iBAAA,CAAEhB,IAAI,CAACG,WAAW,UAAAa,iBAAA,iBAAhBA,iBAAA,CAAkBE,MAAM,CAAC,CAC5ER,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAE,MAAO,CAAAX,IAAI,CAACG,WAAW,CAAC,CAEzE,GAAI,CACF,KAAM,CAAAG,QAAQ,CAAG,KAAM,CAAAV,GAAG,CAACuB,IAAI,CAAC,GAAGtB,QAAQ,wBAAwB,CAAEG,IAAI,CAAE,CACzEQ,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CACF,CAAC,CAAC,CACF,MAAO,CAAAF,QAAQ,CACjB,CAAE,MAAOG,KAAK,CAAE,KAAAW,eAAA,CAAAC,gBAAA,CACdX,OAAO,CAACD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/CC,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAAW,eAAA,CAAEX,KAAK,CAACH,QAAQ,UAAAc,eAAA,iBAAdA,eAAA,CAAgBpB,IAAI,CAAC,CACtDU,OAAO,CAACD,KAAK,CAAC,eAAe,EAAAY,gBAAA,CAAEZ,KAAK,CAACH,QAAQ,UAAAe,gBAAA,iBAAdA,gBAAA,CAAgBC,MAAM,CAAC,CACtD,KAAM,CAAAb,KAAK,CACb,CACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}