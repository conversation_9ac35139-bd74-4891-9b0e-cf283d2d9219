{"ast": null, "code": "import{useState}from\"react\";import{TextField,Grid,Avatar,Button,Dialog,DialogTitle,DialogContent,DialogActions,Tab,Tabs,Card,CardContent,IconButton,Box,CardHeader,Container,CircularProgress,List,ListItem,ListItemIcon,ListItemText,Divider}from\"@mui/material\";import Tooltip,{tooltipClasses}from\"@mui/material/Tooltip\";import{useNavigate}from\"react-router-dom\";import{Helmet}from\"react-helmet-async\";import{styled}from\"@mui/material/styles\";import CreateIcon from\"@mui/icons-material/Create\";import CloseIcon from\"@mui/icons-material/Close\";import SaveIcon from\"@mui/icons-material/Save\";import InstagramIcon from\"@mui/icons-material/Instagram\";import FacebookIcon from\"@mui/icons-material/Facebook\";import GitHubIcon from\"@mui/icons-material/GitHub\";import CheckCircleOutlineIcon from\"@mui/icons-material/CheckCircleOutline\";import LinkedInIcon from\"@mui/icons-material/LinkedIn\";import PhoneIcon from\"@mui/icons-material/Phone\";import PhoneIphoneIcon from\"@mui/icons-material/PhoneIphone\";import XIcon from\"@mui/icons-material/X\";import EmailIcon from\"@mui/icons-material/Email\";import WhatsAppIcon from\"@mui/icons-material/WhatsApp\";import YouTubeIcon from\"@mui/icons-material/YouTube\";import PinterestIcon from\"@mui/icons-material/Pinterest\";import RedditIcon from\"@mui/icons-material/Reddit\";import TelegramIcon from\"@mui/icons-material/Telegram\";import SpotifyIcon from\"@mui/icons-material/MusicNote\";import LanguageIcon from\"@mui/icons-material/Language\";import Typography from\"@mui/material/Typography\";import AppLinksByProfile from\"../sections/@dashboard/app/AppLinksByProfile\";import{AppProfileCard}from\"../sections/@dashboard/app\";import{EditProfile}from\"../ProfileData.ts\";import{DeleteContact}from\"../ContactData.ts\";import{GetSocialLinks,EditCustomLink,EditSocialLink,CreateSocialLink,CreateCustomLink,DeleteSocialLink,GetCustomLinks,DeleteCustomLink}from\"../LinkData.ts\";import{useEffect}from\"react\";import{toast}from\"react-toastify\";import\"react-toastify/dist/ReactToastify.css\";import PhotoSelector from\"../sections/auth/signup/PhotoSelector\";import{useProfile}from\"../Context/ProfileContext\";import PhoneLinkDialog from\"../sections/@dashboard/Link/PhoneLinkDialog\";import EmailLinkDialog from\"../sections/@dashboard/Link/EmailLinkDialog\";import WhatsAppLinkDialog from\"../sections/@dashboard/Link/WhatsAppLinkDialog\";import{motion}from\"framer-motion\";import EmojiPeopleIcon from\"@mui/icons-material/EmojiPeople\";import BusinessCenterIcon from\"@mui/icons-material/BusinessCenter\";import EngineeringIcon from\"@mui/icons-material/Engineering\";import ApartmentIcon from\"@mui/icons-material/Apartment\";import Iconify from\"../components/iconify\";import Appearance from\"./Appearance\";import{SvgIcon}from\"@mui/material\";import{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";import{Fragment as _Fragment}from\"react/jsx-runtime\";function TikTokIcon(props){return/*#__PURE__*/_jsx(SvgIcon,{...props,children:/*#__PURE__*/_jsx(\"path\",{d:\"M12.95 2c.52 1.97 2.08 3.38 4.05 3.59v3.27a7.49 7.49 0 0 1-3.64-.98v5.77c0 3.58-3.2 6.54-7.15 5.38A5.55 5.55 0 0 1 4 13.3c0-2.74 2.05-5.06 4.83-5.35v3.28c-.89.18-1.55.97-1.55 1.93 0 1.08.9 1.96 2 1.96s2-.88 2-1.96V2h1.67z\"})});}// import About from \"./About\";\nconst BootstrapTooltip=styled(_ref=>{let{className,...props}=_ref;return/*#__PURE__*/_jsx(Tooltip,{...props,arrow:true,classes:{popper:className}});})(_ref2=>{let{theme}=_ref2;return{[`& .${tooltipClasses.arrow}`]:{color:\"#ee705e\"},[`& .${tooltipClasses.tooltip}`]:{backgroundColor:\"#ee705e\"}};});const ProfileUser=()=>{var _profile$contacts,_profile$contacts2,_profile$contacts3;const{profile,fetchProfile}=useProfile();const[activeTab,setActiveTab]=useState(0);const[isMobile,setIsMobile]=useState(false);const[isProfileCardVisible,setIsProfileCardVisible]=useState(false);const[isVisible,setIsVisible]=useState(true);const[isSaveButtonActive,setIsSaveButtonActive]=useState(false);const[isProfileSaving,setIsProfileSaving]=useState(false);const[isCustomLinkSaving,setIsCustomLinkSaving]=useState(false);const[isSocialLinkSaving,setIsSocialLinkSaving]=useState(false);const navigate=useNavigate();const handleTabChange=(event,newValue)=>{setActiveTab(newValue);};// Function to get bundle icon based on category\nconst getBundleIcon=category=>{switch(category){case\"Free\":return/*#__PURE__*/_jsx(EmojiPeopleIcon,{fontSize:\"large\",sx:{color:\"#ff715b\"}});case\"Student\":return/*#__PURE__*/_jsx(BusinessCenterIcon,{fontSize:\"large\",sx:{color:\"#ff715b\"}});case\"Freelance\":return/*#__PURE__*/_jsx(EngineeringIcon,{fontSize:\"large\",sx:{color:\"#ff715b\"}});case\"Enterprise\":return/*#__PURE__*/_jsx(ApartmentIcon,{fontSize:\"large\",sx:{color:\"#ff715b\"}});default:return/*#__PURE__*/_jsx(EmojiPeopleIcon,{fontSize:\"large\",sx:{color:\"#ff715b\"}});}};const[User,setUser]=useState({id:0,email:\"\",firstName:\"\",lastName:\"\",category:\"\",budget:0.0});const[Profile,setProfile]=useState({id:0,userId:0,userName:\"\",birthDate:\"\",gender:\"\",profilePicture:\"\",profileCoverPicture:\"\",profilePictureFrame:0,occupation:\"\",isPremium:false,user:null,socialLinks:null,customLinks:null,premium:null,isSearch:null,country:null});const[isLoading,setIsLoading]=useState(true);const[SocialLinks,setSocialLinks]=useState([]);const[CustomLinks,setCustomLinks]=useState([]);const[newSocialLink,setNewSocialLink]=useState({ProfileId:0,LinkUrl:\"\",Category:\"\",Title:\"\",Color:\"\"});const[newCustomLink,setNewCustomLink]=useState({ProfileId:0,LinkUrl:\"\",Title:\"\",Color:\"Gray\",Icon:null});const[editCustomSectionVisible,setEditCustomSectionVisible]=useState(false);const[editedCustomLink,setEditedCustomLink]=useState({Id:0,ProfileId:0,LinkUrl:\"\",Title:\"\",Icon:null});const[editSocialSectionVisible,setEditSocialSectionVisible]=useState(false);const[editedSocialLink,setEditedSocialLink]=useState({Id:0,ProfileId:0,Title:\"\",LinkUrl:\"\",Category:\"\",Color:\"\"});const[open,setOpen]=useState(false);const[CategoryChosen,setCategoryChosen]=useState(false);const handleClickOpen=(title,text,color)=>{setOpen(true);setNewSocialLink(prevNewSocialLink=>({...prevNewSocialLink,Category:text,Color:color}));};const socialLinks=[{platform:\"Twitter\",icon:\"X\",label:\"Can you provide your Twitter account link?\",dialogTitle:\"Twitter link\",color:\"linear-gradient(to bottom,#0d90e0, #1DA1F2)\"},{platform:\"GitHub\",icon:\"GitHub\",label:\"Can you provide your GitHub account link?\",dialogTitle:\"GitHub link\",color:\"linear-gradient(112.1deg, rgb(63, 76, 119) 11.4%, rgb(32, 38, 57) 70.2%)\"},{platform:\"Instagram\",icon:\"Instagram\",label:\"Can you provide your Insta account link?\",dialogTitle:\"insta link\",color:\"linear-gradient(90deg, #f46f30, #c32aa3)\"},{platform:\"Facebook\",icon:\"Facebook\",label:\"Can you provide your Facebook account link?\",dialogTitle:\"facebook link\",color:\"linear-gradient(180deg, #1877f2, #3b5998)\"},{platform:\"TikTok\",icon:\"TikTok\",label:\"Can you provide your TikTok account link?\",dialogTitle:\"TikTok link\",color:\"linear-gradient(180deg, #000000, #ff0050)\"},{platform:\"LinkedIn\",icon:\"LinkedIn\",label:\"Can you provide your LinkedIn account link?\",dialogTitle:\"LinkedIn link\",color:\"linear-gradient(135deg, #0077B5, #00A0DC)\"}];const PhoneLinks=[{platform:\"Phone\",icon:\"Phone\",label:\"Can you provide your Phone number?\",dialogTitle:\"Phone link\",color:\"linear-gradient(to bottom,#0d90e0, #1DA1F2)\"},{platform:\"Email\",icon:\"Email\",label:\"Can you provide your Email address?\",dialogTitle:\"Email link\",color:\"linear-gradient(to bottom,#EA4335, #FBBC05)\"},{platform:\"WhatsApp\",icon:\"WhatsApp\",label:\"Can you provide your WhatsApp number?\",dialogTitle:\"WhatsApp link\",color:\"linear-gradient(to bottom,#25D366, #128C7E)\"}];const handleUserChange=event=>{const{name,value}=event.target;const hasNonSpaceCharacter=value.trim()!==\"\";const isValidInput=/^[a-zA-Z\\s]+$/.test(value);if(name===\"firstName\"||name===\"lastName\"){if(isValidInput&&hasNonSpaceCharacter){setUser(prevUser=>({...prevUser,[name]:value}));setIsSaveButtonActive(true);}}};const handleProfileChange=event=>{const{name,value}=event.target;setProfile(prevProfile=>({...prevProfile,[name]:value}));setIsSaveButtonActive(true);};const handleSearchChange=value=>{setProfile(prevProfile=>({...prevProfile,isSearch:value}));setIsSaveButtonActive(true);};const handleNewSocialLinkChange=event=>{const{name,value}=event.target;setNewSocialLink(prevNewSocialLink=>({...prevNewSocialLink,[name]:value}));};const handleNewCustomLinkChange=event=>{const{name,value}=event.target;setNewCustomLink(prevNewCustomLink=>({...prevNewCustomLink,[name]:value}));setValidationStatus({...validationStatus,[name]:value!==\"\"});};const handlePhotoSelect=photoDataUrl=>{setProfile(prevData=>({...prevData,profilePicture:photoDataUrl}));setIsSaveButtonActive(true);};const handleCoverPhotoSelect=photoDataUrl=>{setProfile(prevData=>({...prevData,profileCoverPicture:photoDataUrl}));setIsSaveButtonActive(true);};const handleSave=async()=>{setIsProfileSaving(true);try{const response=await EditProfile(User,Profile);if(response&&response.status===200){await fetchProfile();toast.success(\"Profile saved successfully\",{position:\"top-center\",autoClose:1000});setIsSaveButtonActive(false);}else{throw new Error(\"Failed to save profile\");}}catch(error){console.error(\"Error saving profile:\",error);toast.error(`Error saving profile: ${error.message||\"Unknown error\"}`,{position:\"top-center\",autoClose:3000});}finally{setIsProfileSaving(false);}};// edit Custom links\nconst handleBringEditedCustomLink=async link=>{setEditedCustomLink(link);setEditCustomSectionVisible(true);// Switch to links tab when editing from mobile\nif(isMobile){setActiveTab(1);}};const handleDeleteContact=async contactId=>{if(!window.confirm(\"Are you sure you want to delete this contact?\")){return;}console.log(\"Attempting to delete contact with ID:\",contactId);try{const response=await DeleteContact(contactId);console.log(\"Delete response:\",response);if(response){toast.success(\"Contact deleted successfully\",{position:\"top-center\",autoClose:1000});fetchProfile();}else{toast.error(\"No response from server\",{position:\"top-center\",autoClose:1000});}}catch(error){console.error(\"Error deleting contact:\",error);toast.error(`Error deleting contact: ${error.message||error}`,{position:\"top-center\",autoClose:3000});}};const handleEditContact=contact=>{// Set the contact being edited\nsetEditingContact(contact);// Open the appropriate dialog based on contact category\nif(contact.Category===\"Phone\"||contact.Category===\"PhoneNumber\"){setOpenPhoneDialog(true);}else if(contact.Category===\"Gmail\"||contact.Category===\"Email\"){setOpenEmailDialog(true);}else if(contact.Category===\"WhatsApp\"){setOpenWhatsAppDialog(true);}// Switch to links tab when editing from mobile\nif(isMobile){setActiveTab(1);}};const handleEditedCustomLinkChange=async event=>{const{name,value}=event.target;setEditedCustomLink(prevLink=>({...prevLink,[name]:value}));};const handleCustomLinkPhotoSelectEdit=photoDataUrl=>{setEditedCustomLink(prevLink=>({...prevLink,Icon:photoDataUrl}));setValidationStatus({...validationStatus,photo:photoDataUrl!==null});};const handleCustomLinkEdit=async()=>{setIsCustomLinkSaving(true);try{if(!isValidCustomURL(editedCustomLink.LinkUrl)){toast.error(\"Invalid URL format\",{position:\"top-center\",autoClose:2000});return;}if(editedCustomLink.Title===\"\"){toast.error(\"Title can't be empty\",{position:\"top-center\",autoClose:2000});return;}await EditCustomLink(editedCustomLink);setEditedCustomLink({Id:0,ProfileId:0,LinkUrl:\"\",Title:\"\"});setEditCustomSectionVisible(false);toast.success(\"Link edited\",{position:\"top-center\",autoClose:1000});fetchCustomLinksData();}catch(error){toast.error(error,{position:\"top-center\",autoClose:1000});console.error(\"Error saving Custom link data:\",error.message);}finally{setIsCustomLinkSaving(false);}};// edit Social link\nconst handleBringEditedSocialLink=async link=>{setEditedSocialLink(link);setEditSocialSectionVisible(true);// Switch to links tab when editing from mobile\nif(isMobile){setActiveTab(1);}};const handleEditedSocialLinkChange=async event=>{const{name,value}=event.target;setEditedSocialLink(prevLink=>({...prevLink,[name]:value}));};const handleSocialLinkEdit=async()=>{setIsSocialLinkSaving(true);try{if(!isValidURL(editedSocialLink.LinkUrl)){toast.error(\"Invalid URL format\",{position:\"top-center\",autoClose:2000});return;}if(editedSocialLink.Title===\"\"){toast.error(\"Title can't be empty\",{position:\"top-center\",autoClose:2000});return;}const regex=/^(?:https?:\\/\\/)?(?:www\\.)?([^\\/.]+)/i;const matches=editedSocialLink.LinkUrl.match(regex);const domain=matches?matches[1]:null;if(domain!==editedSocialLink.Category.toLowerCase()){toast.error(\"You cant change the category of the link\",{position:\"top-center\",autoClose:2000});return;}await EditSocialLink(editedSocialLink);setEditedSocialLink({Id:0,ProfileId:0,Title:\"\",LinkUrl:\"\",Category:\"\",Color:\"\"});setEditSocialSectionVisible(false);setCategoryChosen(false);toast.success(\"Link edited\",{position:\"top-center\",autoClose:1000});fetchSocialLinksData();}catch(error){toast.error(error,{position:\"top-center\",autoClose:1000});console.error(\"Error saving Social link data:\",error.message);}finally{setIsSocialLinkSaving(false);}};const handleDeleteCustomLink=async Id=>{// Add confirmation dialog\nif(!window.confirm(\"Are you sure you want to delete this custom link?\")){return;}try{const response=await DeleteCustomLink(Id);if(response!=null){toast.success(\"Custom link deleted successfully\",{position:\"top-center\",autoClose:1000});fetchCustomLinksData();}}catch(error){toast.error(\"Failed to delete custom link\",{position:\"top-center\",autoClose:1000});console.error(\"Error deleting custom link:\",error);}};const handleDeleteSocialLink=async Id=>{// Add confirmation dialog\nif(!window.confirm(\"Are you sure you want to delete this social link?\")){return;}try{const response=await DeleteSocialLink(Id);if(response!=null){toast.success(\"Social link deleted successfully\",{position:\"top-center\",autoClose:1000});fetchSocialLinksData();}}catch(error){toast.error(\"Failed to delete social link\",{position:\"top-center\",autoClose:1000});console.error(\"Error deleting social link:\",error);}};const fetchUserData=async()=>{try{setUser({id:profile.id,email:profile.email,firstName:profile.firstName,lastName:profile.lastName,category:profile.category});setProfile(profile.profile);setNewSocialLink(prevNewSocialLink=>({...prevNewSocialLink,ProfileId:profile.profile.id}));setNewCustomLink(prevNewCustomLink=>({...prevNewCustomLink,ProfileId:profile.profile.id}));}catch(error){if(error.redirectToLogin){navigate(\"/Login\");}}};// Update local state when profile context changes\nuseEffect(()=>{if(profile&&profile.profile){fetchUserData();}},[profile]);useEffect(()=>{const handleResize=()=>{setIsMobile(window.innerWidth<=768);};window.addEventListener(\"resize\",handleResize);// Call handleResize immediately to set the initial state\nhandleResize();// Fetch data when component mounts\nconst fetchData=async()=>{if(profile&&profile.profile){setIsLoading(true);try{await Promise.all([fetchUserData(),fetchSocialLinksData(),fetchCustomLinksData()]);}catch(error){console.error(\"Error fetching profile data:\",error);}finally{setIsLoading(false);}}};fetchData();return()=>{window.removeEventListener(\"resize\",handleResize);};},[]);const fetchSocialLinksData=async()=>{try{const response=await GetSocialLinks();setSocialLinks(response.data);}catch(error){console.error(\"Error fetching social links data:\",error);}};const fetchCustomLinksData=async()=>{try{const response=await GetCustomLinks();setCustomLinks(response.data);}catch(error){console.error(\"Error fetching social links data:\",error);}};const handleClose=()=>{setOpen(false);setNewSocialLink(prevNewSocialLink=>({...prevNewSocialLink,LinkUrl:\"\",Category:\"\",Title:\"\",Color:\"\"}));};const handleDone=async()=>{if(!isValidURL(newSocialLink.LinkUrl)){toast.error(\"Invalid URL format\",{position:\"top-center\",autoClose:2000});return;}const response=await CreateSocialLink(newSocialLink);localStorage.setItem(\"isLinksCardVisible\",\"true\");handleClose();if(response){fetchSocialLinksData();toast.success(\"Social link created\",{position:\"top-center\",autoClose:1000});}else{toast.error(\"Error while creating social link\",{position:\"top-center\",autoClose:1000});}};const handleCustomLinkDone=async()=>{if(!isValidCustomURL(newCustomLink.LinkUrl)){toast.error(\"Invalid URL format\",{position:\"top-center\",autoClose:2000});return;}const response=await CreateCustomLink(newCustomLink);if(response){fetchCustomLinksData();toast.success(\"Custom link created\",{position:\"top-center\",autoClose:1000});handleCustomLinkClose();}else{toast.error(\"Error while creating custom link\",{position:\"top-center\",autoClose:1000});}};const handleCustomLinkPhotoSelect=photoDataUrl=>{setNewCustomLink(prevData=>({...prevData,Icon:photoDataUrl}));setValidationStatus({...validationStatus,photo:photoDataUrl!==null});};// Second Dialog\nconst[openSecondDialog,setOpenSecondDialog]=useState(false);const[openPhoneDialog,setOpenPhoneDialog]=useState(false);const[openEmailDialog,setOpenEmailDialog]=useState(false);const[openWhatsAppDialog,setOpenWhatsAppDialog]=useState(false);const[editingContact,setEditingContact]=useState(null);const[openCategoryChooseDialog,setOpenCategoryChooseDialog]=useState(false);// Second Dialog Handlers\nconst handleClickOpenSecond=()=>{setOpenSecondDialog(true);};const handleCustomLinkClose=()=>{setNewCustomLink(prevData=>({...prevData,Title:\"\",LinkUrl:\"\",Icon:\"\"}));setOpenSecondDialog(false);};//lazem create\nconst[validationStatus,setValidationStatus]=useState({title:false,linkUrl:false,photo:false});const isFormValid=newCustomLink.Title.trim()!==\"\"&&newCustomLink.LinkUrl.trim()!==\"\";const isValidURL=input=>{// Regular expression pattern to match URLs that start with http or https\nconst urlPattern=/^(https?:\\/\\/)([\\da-z.-]+)\\.([a-z.]{2,6})(\\/[^?#]*)?(\\?[^#]*)?(#.*)?$/i;// Regular expression pattern to match phone numbers with 8 digits\nconst phonePattern=/^\\d{8}$/;// Check if the input matches URL pattern\nconst isURL=urlPattern.test(input);// Check if the input matches phone number pattern\nconst isPhoneNumber=phonePattern.test(input);// List of social media domains\nconst socialMediaDomains=[\"facebook.com\",\"twitter.com\",\"instagram.com\",\"linkedin.com\",\"instagram.com\",\"github.com\",\"tiktok.com\"];// Check if the input matches any social media domain and starts with http or https\nconst isSocialMedia=socialMediaDomains.some(domain=>new RegExp(`^https?:\\/\\/(?:www\\.)?${domain}`,\"i\").test(input));const isCategoryLike=new RegExp(`^https?:\\/\\/(?:www\\.)?${newSocialLink.Category}`,\"i\").test(input);// Return true if it's a valid URL with http/https, a valid social media URL with http/https, OR a valid phone number\nreturn isURL&&isSocialMedia&&isCategoryLike||isPhoneNumber;};const isValidCustomURL=input=>{// Regular expression pattern to match URLs that start with http or https\nconst urlPattern=/^(https?:\\/\\/)([\\da-z.-]+)\\.([a-z.]{2,6})(\\/[^?#]*)?(\\?[^#]*)?(#.*)?$/i;// Check if the input matches URL pattern\nconst isURL=urlPattern.test(input);return isURL;};const IconFromPlatform=platform=>{switch(platform){case\"Twitter\":return/*#__PURE__*/_jsx(XIcon,{sx:{fontSize:\"36px\",cursor:\"pointer\",transition:\"color 0.3s\",\"&:hover\":{color:\"#212121\"}}});case\"GitHub\":return/*#__PURE__*/_jsx(GitHubIcon,{sx:{fontSize:\"36px\",cursor:\"pointer\",transition:\"color 0.3s\",\"&:hover\":{color:\"#212121\"}}});case\"Instagram\":return/*#__PURE__*/_jsx(InstagramIcon,{sx:{fontSize:\"35px\",cursor:\"pointer\",transition:\"color 0.3s\",\"&:hover\":{color:\"#D81B60\"}}});case\"Facebook\":return/*#__PURE__*/_jsx(FacebookIcon,{sx:{fontSize:\"35px\",cursor:\"pointer\",transition:\"color 0.3s\",\"&:hover\":{color:\"#5892d0\"}}});case\"LinkedIn\":return/*#__PURE__*/_jsx(LinkedInIcon,{sx:{fontSize:\"35px\",cursor:\"pointer\",transition:\"color 0.3s\",\"&:hover\":{color:\"#00b9f1\"}}});case\"TikTok\":return/*#__PURE__*/_jsx(TikTokIcon,{icon:\"fab:tiktok\",sx:{fontSize:\"35px\",cursor:\"pointer\",transition:\"color 0.3s\",\"&:hover\":{color:\"#000\"}}});case\"Phone\":return/*#__PURE__*/_jsx(PhoneIcon,{sx:{fontSize:\"35px\",cursor:\"pointer\",transition:\"color 0.3s\",\"&:hover\":{color:\"#4CAF50\"}}});case\"Gmail\":case\"Email\":return/*#__PURE__*/_jsx(EmailIcon,{sx:{fontSize:\"35px\",cursor:\"pointer\",transition:\"color 0.3s\",\"&:hover\":{color:\"#EA4335\"}}});case\"WhatsApp\":return/*#__PURE__*/_jsx(WhatsAppIcon,{sx:{fontSize:\"35px\",cursor:\"pointer\",transition:\"color 0.3s\",\"&:hover\":{color:\"#25D366\"}}});default:return null;}};return/*#__PURE__*/_jsxs(Container,{sx:{\"@media (min-width: 900px)\":{paddingRight:\"0\"// Remove default padding to prevent overlap with sidebar\n}},children:[/*#__PURE__*/_jsxs(Helmet,{children:[/*#__PURE__*/_jsx(\"title\",{children:\"IDigics | Profile\"}),/*#__PURE__*/_jsx(\"meta\",{name:\"description\",content:`Manage your IDigics profile${User.firstName&&User.lastName?` - ${User.firstName} ${User.lastName}`:\"\"}${Profile.occupation?`, ${Profile.occupation}`:\"\"}. Update your social links, contact information, and professional details.`}),/*#__PURE__*/_jsx(\"meta\",{property:\"og:title\",content:`${User.firstName&&User.lastName?`${User.firstName} ${User.lastName} | `:\"\"}IDigics Profile`}),/*#__PURE__*/_jsx(\"meta\",{property:\"og:description\",content:`Manage your IDigics profile${User.firstName&&User.lastName?` - ${User.firstName} ${User.lastName}`:\"\"}${Profile.occupation?`, ${Profile.occupation}`:\"\"}. Update your social links, contact information, and professional details.`}),/*#__PURE__*/_jsx(\"meta\",{property:\"og:type\",content:\"profile\"}),/*#__PURE__*/_jsx(\"meta\",{property:\"og:url\",content:window.location.href}),Profile.profilePicture&&/*#__PURE__*/_jsx(\"meta\",{property:\"og:image\",content:Profile.profilePicture}),/*#__PURE__*/_jsx(\"meta\",{property:\"og:site_name\",content:\"IDigics\"}),/*#__PURE__*/_jsx(\"meta\",{name:\"twitter:card\",content:\"summary_large_image\"}),/*#__PURE__*/_jsx(\"meta\",{name:\"twitter:title\",content:`${User.firstName&&User.lastName?`${User.firstName} ${User.lastName} | `:\"\"}IDigics Profile`}),/*#__PURE__*/_jsx(\"meta\",{name:\"twitter:description\",content:`Manage your IDigics profile${User.firstName&&User.lastName?` - ${User.firstName} ${User.lastName}`:\"\"}${Profile.occupation?`, ${Profile.occupation}`:\"\"}. Update your social links, contact information, and professional details.`}),Profile.profilePicture&&/*#__PURE__*/_jsx(\"meta\",{name:\"twitter:image\",content:Profile.profilePicture}),User.firstName&&/*#__PURE__*/_jsx(\"meta\",{property:\"profile:first_name\",content:User.firstName}),User.lastName&&/*#__PURE__*/_jsx(\"meta\",{property:\"profile:last_name\",content:User.lastName}),Profile.userName&&/*#__PURE__*/_jsx(\"meta\",{property:\"profile:username\",content:Profile.userName})]}),isLoading?/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:0.5,ease:\"easeOut\"},children:/*#__PURE__*/_jsxs(Box,{sx:{display:\"flex\",justifyContent:\"center\",alignItems:\"center\",minHeight:\"400px\",flexDirection:\"column\",gap:2},children:[/*#__PURE__*/_jsx(CircularProgress,{size:60}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"textSecondary\",children:\"Loading profile data...\"})]})}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Box,{sx:{display:\"flex\",justifyContent:\"center\",marginBottom:\"45px\"},children:/*#__PURE__*/_jsxs(Tabs,{value:activeTab,onChange:handleTabChange,\"aria-label\":\"Account tabs\",children:[/*#__PURE__*/_jsx(Tab,{label:\"Appearance\"}),/*#__PURE__*/_jsx(Tab,{label:\"Links\"})]})}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:2,sx:{marginTop:\"-50px\"},children:[/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,md:9,sx:{\"@media (min-width: 900px) and (max-width: 1200px)\":{maxWidth:\"calc(100vw - 320px)\",// Account for sidebar + margins\npaddingRight:\"20px\"},\"@media (min-width: 1201px)\":{maxWidth:\"calc(75vw - 40px)\",paddingRight:\"20px\"},\"@media (max-width: 899px)\":{maxWidth:\"100%\",paddingRight:\"0\"}},children:[User.category&&/*#__PURE__*/_jsxs(Card,{sx:{p:3,marginBottom:\"30px\",position:\"relative\"},children:[/*#__PURE__*/_jsx(Typography,{variant:\"overline\",sx:{mb:3,display:\"block\",color:\"text.secondary\"},children:\"Your Plan\"}),/*#__PURE__*/_jsxs(Box,{sx:{display:\"flex\",alignItems:\"center\",gap:2,mb:2},children:[/*#__PURE__*/_jsx(Box,{sx:{display:\"flex\",alignItems:\"center\",justifyContent:\"center\",width:60,height:60,borderRadius:\"12px\",background:\"linear-gradient(135deg, #ff715b20 0%, #e65d4710 100%)\",border:\"2px solid #ff715b30\",boxShadow:\"0 8px 32px #ff715b20\"},children:getBundleIcon(User.category)}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",children:User.category})]}),User.category!==\"Freelance\"&&User.category!==\"Enterprise\"&&/*#__PURE__*/_jsx(Box,{sx:{mt:{xs:2,sm:0},position:{sm:\"absolute\"},top:{sm:24},right:{sm:24}},children:/*#__PURE__*/_jsx(Button,{size:\"small\",variant:\"outlined\",onClick:()=>{navigate(\"/admin/bundles\");},children:\"Upgrade plan\"})})]}),activeTab===0&&/*#__PURE__*/_jsx(Appearance,{Profile:Profile,User:User,isSaveButtonActive:isSaveButtonActive,setIsSaveButtonActive:setIsSaveButtonActive,isProfileSaving:isProfileSaving,handlePhotoSelect:handlePhotoSelect,handleProfileChange:handleProfileChange,handleUserChange:handleUserChange,handleSave:handleSave,handleIsSearchChange:handleSearchChange,handleCoverPhotoSelect:handleCoverPhotoSelect}),activeTab===1&&/*#__PURE__*/_jsxs(Grid,{container:true,spacing:2,children:[/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,md:12,children:[editSocialSectionVisible&&/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:12,sx:{marginBottom:\"10px\"},children:/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(CardHeader,{title:\"Edit your links\",subheader:\"This is where you can edit your entire profile! Here, you can manage and edit your 'About' section.\"}),/*#__PURE__*/_jsx(CardContent,{children:/*#__PURE__*/_jsxs(Grid,{container:true,spacing:2,children:[/*#__PURE__*/_jsx(IconButton,{sx:{position:\"absolute\",right:8,top:8},\"aria-label\":\"close\",onClick:()=>{setEditSocialSectionVisible(false);setCategoryChosen(false);},children:/*#__PURE__*/_jsx(CloseIcon,{})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:9,md:7,children:/*#__PURE__*/_jsx(TextField,{name:\"Title\",label:\"Type your link title\",focused:true,value:editedSocialLink.Title,sx:{width:\"100%\"},onChange:handleEditedSocialLinkChange})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:12,children:/*#__PURE__*/_jsx(TextField,{name:\"LinkUrl\",label:\"Type your link url\",value:editedSocialLink.LinkUrl,focused:true,sx:{width:\"100%\"},onChange:handleEditedSocialLinkChange})})]})}),/*#__PURE__*/_jsxs(Button,{onClick:handleSocialLinkEdit,color:\"primary\",variant:\"outlined\",disabled:isSocialLinkSaving,sx:{margin:\"25px\",backgroundColor:\"#ee705e\",color:\"white\",\"&:hover\":{color:\"#ee705e\"}},children:[/*#__PURE__*/_jsx(\"span\",{style:{marginRight:\"10px\"},children:isSocialLinkSaving?\"Saving...\":\"Save your link\"}),isSocialLinkSaving?/*#__PURE__*/_jsx(CircularProgress,{size:20,color:\"inherit\"}):/*#__PURE__*/_jsx(SaveIcon,{})]}),/*#__PURE__*/_jsxs(Dialog,{open:openCategoryChooseDialog,onClose:()=>{setOpenCategoryChooseDialog(false);},children:[/*#__PURE__*/_jsx(DialogTitle,{color:\"primary\",children:\"Choose a website\"}),/*#__PURE__*/_jsx(DialogContent,{children:/*#__PURE__*/_jsx(Grid,{sx:{display:\"flex\"},container:true,spacing:2,children:socialLinks.map(_ref3=>{let{platform}=_ref3;let icon=IconFromPlatform(platform);return/*#__PURE__*/_jsx(Grid,{item:true,xs:3,sm:6,md:2,lg:2,sx:{display:\"flex\",justifyContent:\"center\",marginTop:\"5px\"},children:/*#__PURE__*/_jsx(BootstrapTooltip,{title:platform,sx:{\"& .MuiTooltip-tooltip\":{fontSize:\"13px\"}},children:/*#__PURE__*/_jsx(Button,{variant:\"outlined\",sx:{color:\"rgba(20, 43, 58, 0.5)\",borderColor:\"rgba(20, 43, 58, 0.3)\",height:\"100%\",padding:\"15px 20px\"},onClick:()=>{setEditedSocialLink(prevLink=>({...prevLink,Category:platform}));setOpenCategoryChooseDialog(false);setCategoryChosen(true);},children:icon})})},platform);})})}),/*#__PURE__*/_jsx(DialogActions,{})]})]})}),editCustomSectionVisible&&/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:12,sx:{marginTop:\"10px\",marginBottom:\"10px\"},children:/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(CardHeader,{title:\"Edit your links\",subheader:\"Analyze the daily views to understand the trends and patterns in the number of views your content receives.Gain valuable insights into the most active days and make informed decisions based on this data.\"}),/*#__PURE__*/_jsx(CardContent,{children:/*#__PURE__*/_jsxs(Grid,{container:true,spacing:2,children:[/*#__PURE__*/_jsx(IconButton,{sx:{position:\"absolute\",right:8,top:8},\"aria-label\":\"close\",onClick:()=>{setEditCustomSectionVisible(false);},children:/*#__PURE__*/_jsx(CloseIcon,{})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:10,md:11,children:/*#__PURE__*/_jsx(TextField,{name:\"Title\",label:\"Type your link title\",value:editedCustomLink.Title,focused:true,sx:{width:\"100%\"},onChange:handleEditedCustomLinkChange})}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:1,md:1,children:[/*#__PURE__*/_jsx(Avatar,{style:{width:\"3rem\",height:\"3rem\"},focused:true,src:editedCustomLink.Icon}),/*#__PURE__*/_jsx(PhotoSelector,{onSelect:handleCustomLinkPhotoSelectEdit})]}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:12,children:/*#__PURE__*/_jsx(TextField,{name:\"LinkUrl\",label:\"Type your link url\",value:editedCustomLink.LinkUrl,focused:true,sx:{width:\"100%\"},onChange:handleEditedCustomLinkChange})})]})}),/*#__PURE__*/_jsxs(Button,{onClick:handleCustomLinkEdit,color:\"primary\",variant:\"outlined\",disabled:isCustomLinkSaving,sx:{margin:\"25px\",backgroundColor:\"#ee705e\",color:\"white\",\"&:hover\":{color:\"#ee705e\"}},children:[/*#__PURE__*/_jsx(\"span\",{style:{marginRight:\"10px\"},children:isCustomLinkSaving?\"Saving...\":\"Save your link\"}),isCustomLinkSaving?/*#__PURE__*/_jsx(CircularProgress,{size:20,color:\"inherit\"}):/*#__PURE__*/_jsx(SaveIcon,{})]})]})}),/*#__PURE__*/_jsxs(Card,{sx:{background:\"linear-gradient(135deg, #ff715b08, #ff715b03)\",border:\"1px solid #ff715b20\",boxShadow:\"0 8px 32px rgba(255, 113, 91, 0.12)\",borderRadius:\"16px\",overflow:\"hidden\",position:\"relative\",\"&::before\":{content:'\"\"',position:\"absolute\",top:0,left:0,right:0,height:\"4px\",background:\"linear-gradient(90deg, #ff715b, #e65d47)\"}},children:[/*#__PURE__*/_jsx(CardHeader,{title:/*#__PURE__*/_jsxs(Box,{sx:{display:\"flex\",alignItems:\"center\",gap:1},children:[/*#__PURE__*/_jsx(Box,{sx:{width:40,height:40,borderRadius:\"12px\",background:\"linear-gradient(135deg, #ff715b, #e65d47)\",display:\"flex\",alignItems:\"center\",justifyContent:\"center\",boxShadow:\"0 4px 16px rgba(255, 113, 91, 0.3)\"},children:/*#__PURE__*/_jsx(CreateIcon,{sx:{color:\"white\",fontSize:20}})}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{fontWeight:700,color:\"#212B36\"},children:\"Create Custom Links\"})]}),subheader:/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{color:\"text.secondary\",mt:1,lineHeight:1.6,fontSize:\"14px\",marginBottom:\"14px\"},children:\"Design personalized links with custom icons and titles. Perfect for showcasing your portfolio, business, or any important links you want to share.\"}),sx:{pb:1}}),/*#__PURE__*/_jsx(CardContent,{sx:{pt:0},children:/*#__PURE__*/_jsx(Grid,{container:true,spacing:1,children:/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:12,children:/*#__PURE__*/_jsx(Button,{size:\"large\",fullWidth:true,variant:\"outlined\",sx:{minHeight:\"80px\",background:\"linear-gradient(135deg, #ff715b08, #ff715b03)\",border:\"2px dashed #ff715b40\",borderRadius:\"20px\",color:\"#ff715b\",fontSize:\"16px\",fontWeight:\"600\",textTransform:\"none\",position:\"relative\",overflow:\"hidden\",transition:\"all 0.4s cubic-bezier(0.4, 0, 0.2, 1)\",\"&::before\":{content:'\"\"',position:\"absolute\",top:0,left:\"-100%\",width:\"100%\",height:\"100%\",background:\"linear-gradient(90deg, transparent, rgba(255, 113, 91, 0.1), transparent)\",transition:\"left 0.6s ease\"},\"&:hover\":{background:\"linear-gradient(135deg, #ff715b15, #ff715b08)\",border:\"2px solid #ff715b\",transform:\"translateY(-3px) scale(1.02)\",boxShadow:\"0 12px 35px rgba(255, 113, 91, 0.3)\",\"&::before\":{left:\"100%\"}},\"&:active\":{transform:\"translateY(-1px) scale(1.01)\"}},onClick:()=>handleClickOpenSecond(),children:/*#__PURE__*/_jsxs(Box,{sx:{display:\"flex\",alignItems:\"center\",gap:2.5,position:\"relative\",zIndex:1},children:[/*#__PURE__*/_jsx(Box,{sx:{width:48,height:48,borderRadius:\"12px\",background:\"linear-gradient(135deg, #ff715b, #e65d47)\",display:\"flex\",alignItems:\"center\",justifyContent:\"center\",boxShadow:\"0 4px 16px rgba(255, 113, 91, 0.4)\",transition:\"all 0.3s ease\"},children:/*#__PURE__*/_jsx(CreateIcon,{sx:{fontSize:24,color:\"white\"}})}),/*#__PURE__*/_jsxs(Box,{sx:{textAlign:\"left\",flex:1},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h7\",sx:{fontWeight:700,color:\"#ff715b\",fontSize:\"18px\",mb:0.5},children:\"Create Custom Link\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{color:\"text.secondary\",fontSize:\"14px\",lineHeight:1.4},children:\"Design personalized links\"})]})]})})})})})]}),/*#__PURE__*/_jsxs(Card,{sx:{marginTop:\"20px\",background:\"linear-gradient(135deg, #667eea08, #764ba203)\",border:\"1px solid #667eea20\",boxShadow:\"0 8px 32px rgba(102, 126, 234, 0.12)\",borderRadius:\"16px\",overflow:\"hidden\",position:\"relative\",\"&::before\":{content:'\"\"',position:\"absolute\",top:0,left:0,right:0,height:\"4px\",background:\"linear-gradient(90deg, #667eea, #764ba2)\"}},children:[/*#__PURE__*/_jsx(CardHeader,{title:/*#__PURE__*/_jsxs(Box,{sx:{display:\"flex\",alignItems:\"center\",gap:1},children:[/*#__PURE__*/_jsx(Box,{sx:{width:40,height:40,borderRadius:\"12px\",background:\"linear-gradient(135deg, #667eea, #764ba2)\",display:\"flex\",alignItems:\"center\",justifyContent:\"center\",boxShadow:\"0 4px 16px rgba(102, 126, 234, 0.3)\"},children:/*#__PURE__*/_jsx(LanguageIcon,{sx:{color:\"white\",fontSize:20}})}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{fontWeight:700,color:\"#212B36\"},children:\"Social Platforms\"})]}),subheader:/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{color:\"text.secondary\",mt:1,lineHeight:1.6,fontSize:\"14px\",marginBottom:\"14px\"},children:\"Connect your social media accounts and professional profiles. Choose from popular platforms and add your custom titles and URLs to build your online presence.\"}),sx:{pb:1}}),/*#__PURE__*/_jsxs(CardContent,{sx:{pt:0},children:[/*#__PURE__*/_jsx(Grid,{container:true,spacing:2,children:socialLinks.map(_ref4=>{let{platform,color}=_ref4;let icon=IconFromPlatform(platform);return/*#__PURE__*/_jsx(Grid,{item:true,xs:6,sm:4,md:2.4,lg:2.4,sx:{display:\"flex\",justifyContent:\"center\"},children:/*#__PURE__*/_jsx(BootstrapTooltip,{title:platform,sx:{\"& .MuiTooltip-tooltip\":{fontSize:\"13px\"}},children:/*#__PURE__*/_jsxs(Button,{variant:\"outlined\",sx:{color:\"rgba(20, 43, 58, 0.5)\",borderColor:\"rgba(20, 43, 58, 0.3)\",height:\"100%\",padding:\"15px 28px\",minHeight:\"100px\",minWidth:\"100px\",boxShadow:`0 6px 24px ${color}18`,transition:\"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",display:\"flex\",flexDirection:\"column\",gap:1,position:\"relative\",overflow:\"hidden\",\"&::before\":{content:'\"\"',position:\"absolute\",top:0,left:0,right:0,height:\"3px\",background:`linear-gradient(90deg, ${color}, ${color}80)`,opacity:0,transition:\"opacity 0.3s ease\"},\"&:hover\":{background:`linear-gradient(135deg, ${color}20, ${color}08)`,border:`2px solid ${color}50`,transform:\"translateY(-6px) scale(1.02)\",boxShadow:`0 12px 40px ${color}30`,\"&::before\":{opacity:1}}},onClick:()=>handleClickOpen(\"Customize your link\",platform,color),children:[/*#__PURE__*/_jsx(Box,{sx:{fontSize:32,mb:0.5},children:icon}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",sx:{fontWeight:600,fontSize:\"11px\",textTransform:\"none\",opacity:0.8},children:platform})]})})},platform);})}),/*#__PURE__*/_jsxs(Box,{sx:{my:4},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:\"flex\",alignItems:\"center\",gap:2,mb:2},children:[/*#__PURE__*/_jsx(Box,{sx:{width:32,height:32,borderRadius:\"8px\",background:\"linear-gradient(135deg, #25D366, #128C7E)\",display:\"flex\",alignItems:\"center\",justifyContent:\"center\",boxShadow:\"0 4px 12px rgba(37, 211, 102, 0.3)\"},children:/*#__PURE__*/_jsx(PhoneIcon,{sx:{color:\"white\",fontSize:16}})}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{fontWeight:600,color:\"#212B36\"},children:\"Contact Information\"})]}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{color:\"text.secondary\",mb:3,lineHeight:1.6,fontSize:\"13px\"},children:\"Add your contact details to make it easy for people to reach you directly.\"}),/*#__PURE__*/_jsx(Divider,{sx:{borderColor:\"rgba(102, 126, 234, 0.2)\",borderWidth:\"1px\",borderStyle:\"dashed\"}})]}),/*#__PURE__*/_jsx(Grid,{container:true,spacing:2,children:PhoneLinks.map(_ref5=>{let{platform,color}=_ref5;let icon=IconFromPlatform(platform);return/*#__PURE__*/_jsx(Grid,{item:true,xs:6,sm:4,md:2.4,lg:2.4,sx:{display:\"flex\",justifyContent:\"center\"},children:/*#__PURE__*/_jsx(BootstrapTooltip,{title:platform,sx:{\"& .MuiTooltip-tooltip\":{fontSize:\"13px\"}},children:/*#__PURE__*/_jsxs(Button,{variant:\"outlined\",sx:{color:\"rgba(20, 43, 58, 0.5)\",borderColor:\"rgba(20, 43, 58, 0.3)\",height:\"100%\",padding:\"15px 20px\",minHeight:\"100px\",minWidth:\"100px\",boxShadow:`0 6px 24px ${color}18`,transition:\"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",display:\"flex\",flexDirection:\"column\",gap:1,position:\"relative\",overflow:\"hidden\",\"&::before\":{content:'\"\"',position:\"absolute\",top:0,left:0,right:0,height:\"3px\",background:`linear-gradient(90deg, ${color}, ${color}80)`,opacity:0,transition:\"opacity 0.3s ease\"},\"&:hover\":{background:`linear-gradient(135deg, ${color}20, ${color}08)`,border:`2px solid ${color}50`,transform:\"translateY(-6px) scale(1.02)\",boxShadow:`0 12px 40px ${color}30`,\"&::before\":{opacity:1}}},onClick:()=>{setEditingContact(null);if(platform===\"Phone\"){setOpenPhoneDialog(true);}else if(platform===\"Email\"){setOpenEmailDialog(true);}else if(platform===\"WhatsApp\"){setOpenWhatsAppDialog(true);}},children:[/*#__PURE__*/_jsx(Box,{sx:{fontSize:32,mb:0.5},children:icon}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",sx:{fontWeight:600,fontSize:\"11px\",textTransform:\"none\",opacity:0.8},children:platform})]})})},platform);})})]})]})]}),/*#__PURE__*/_jsxs(Dialog,{open:openSecondDialog,onClose:handleCustomLinkClose,children:[/*#__PURE__*/_jsx(DialogTitle,{color:\"primary\",children:\"Create your custom link\"}),/*#__PURE__*/_jsx(\"div\",{style:{display:\"flex\",alignItems:\"center\",justifyContent:\"center\"},children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Avatar,{style:{width:\"5rem\",height:\"5rem\"},src:newCustomLink.Icon,alt:\"User Profile Photo\"}),/*#__PURE__*/_jsx(PhotoSelector,{onSelect:handleCustomLinkPhotoSelect,error:newCustomLink.Icon===null&&validationStatus.photo,helperText:newCustomLink.Icon===null?\"Photo is required\":\"\"})]})}),/*#__PURE__*/_jsxs(DialogContent,{children:[/*#__PURE__*/_jsx(TextField,{autoFocus:true,margin:\"dense\",name:\"Title\",label:\"Title\",type:\"text\",fullWidth:true,required:true,color:\"primary\",value:newCustomLink.Title,onChange:handleNewCustomLinkChange,error:newCustomLink.Title.trim()===\"\"&&validationStatus.title,helperText:newCustomLink.Title.trim()===\"\"?\"Title is required\":\"\"}),/*#__PURE__*/_jsx(TextField,{name:\"LinkUrl\",margin:\"dense\",label:\"Your link\",type:\"url\",fullWidth:true,required:true,value:newCustomLink.LinkUrl,onChange:handleNewCustomLinkChange,error:newCustomLink.LinkUrl.trim()===\"\"&&validationStatus.linkUrl,helperText:newCustomLink.LinkUrl.trim()===\"\"?\"URL is required\":\"\"}),/*#__PURE__*/_jsxs(Box,{mt:2,p:2,sx:{backgroundColor:\"#f0f0f0\",borderRadius:\"5px\"},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",color:\"textPrimary\",children:\"Tips for Creating Your Custom Link\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"textSecondary\",children:\"- Ensure your link is correctly formatted, e.g., \\\"https://www.facebook.com/yourprofile\\\"\"})]})]}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{onClick:handleCustomLinkClose,children:\"Cancel\"}),/*#__PURE__*/_jsx(Button,{onClick:handleCustomLinkDone,disabled:!isFormValid,children:\"Done\"})]})]}),/*#__PURE__*/_jsxs(Dialog,{open:open,onClose:handleClose,children:[/*#__PURE__*/_jsx(DialogTitle,{children:\"Create your social link\"}),/*#__PURE__*/_jsxs(DialogContent,{children:[/*#__PURE__*/_jsx(TextField,{name:\"Title\",autoFocus:true,margin:\"dense\",label:\"Title\",type:\"url\",fullWidth:true,required:true,value:newSocialLink.Title,onChange:handleNewSocialLinkChange,helperText:newSocialLink.Title===\"\"?\"Title is required\":\"\"}),/*#__PURE__*/_jsx(TextField,{name:\"LinkUrl\",margin:\"dense\",id:\"linkUrl\",label:\"Url\",type:\"url\",fullWidth:true,required:true,value:newSocialLink.LinkUrl,onChange:handleNewSocialLinkChange,helperText:newSocialLink.LinkUrl===\"\"?\"URL is required\":\"\"}),/*#__PURE__*/_jsxs(Box,{mt:2,p:2,sx:{backgroundColor:\"#f0f0f0\",borderRadius:\"5px\"},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",color:\"textPrimary\",children:\"Tips for Creating Predefined Link\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"textSecondary\",children:\"- Ensure your link is correctly formatted, e.g., https://www.facebook.com/yourprofile\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"textSecondary\",children:\"- Only links from social media platforms like Facebook, Twitter, Instagram, LinkedIn, GitHub, and TikTok are accepted.\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"textSecondary\",children:\"- For phone numbers, simply enter an 8-digit number without spaces or symbols.\"})]})]}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{onClick:handleClose,children:\"Cancel\"}),/*#__PURE__*/_jsx(Button,{onClick:handleDone,disabled:newSocialLink.Title===\"\"||newSocialLink.LinkUrl===\"\",children:\"Done\"})]})]}),/*#__PURE__*/_jsx(PhoneLinkDialog,{setOpenPhoneDialog:setOpenPhoneDialog,openPhoneDialog:openPhoneDialog,Id:profile.id,editingContact:editingContact,fetchProfile:fetchProfile,clearEditingContact:()=>setEditingContact(null)}),/*#__PURE__*/_jsx(EmailLinkDialog,{setOpenEmailDialog:setOpenEmailDialog,openEmailDialog:openEmailDialog,Id:profile.id,editingContact:editingContact,fetchProfile:fetchProfile,clearEditingContact:()=>setEditingContact(null)}),/*#__PURE__*/_jsx(WhatsAppLinkDialog,{setOpenWhatsAppDialog:setOpenWhatsAppDialog,openWhatsAppDialog:openWhatsAppDialog,Id:profile.id,editingContact:editingContact,fetchProfile:fetchProfile,clearEditingContact:()=>setEditingContact(null)}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:12,children:/*#__PURE__*/_jsxs(Card,{sx:{display:isVisible?\"flex\":\"none\",marginTop:\"20px\"},children:[/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(IconButton,{sx:{position:\"absolute\",right:8,top:8},\"aria-label\":\"close\",onClick:()=>{setIsVisible(false);localStorage.setItem(\"isLinksCardVisible\",\"false\");},children:/*#__PURE__*/_jsx(CloseIcon,{})}),/*#__PURE__*/_jsx(Typography,{gutterBottom:true,variant:\"h6\",component:\"div\",children:\"Create Your Custom Link!\"}),/*#__PURE__*/_jsxs(List,{children:[/*#__PURE__*/_jsxs(ListItem,{children:[/*#__PURE__*/_jsx(ListItemIcon,{children:/*#__PURE__*/_jsx(CheckCircleOutlineIcon,{sx:{fontSize:\"20px\"}})}),/*#__PURE__*/_jsx(ListItemText,{children:/*#__PURE__*/_jsx(Typography,{sx:{fontSize:\"16px\"},children:\"Automate content with dynamic feeds and images.\"})})]}),/*#__PURE__*/_jsxs(ListItem,{children:[/*#__PURE__*/_jsx(ListItemIcon,{children:/*#__PURE__*/_jsx(CheckCircleOutlineIcon,{sx:{fontSize:\"20px\"}})}),/*#__PURE__*/_jsx(ListItemText,{children:/*#__PURE__*/_jsx(Typography,{sx:{fontSize:\"16px\"},children:\"Use your own domain to boost branding.\"})})]}),/*#__PURE__*/_jsxs(ListItem,{children:[/*#__PURE__*/_jsx(ListItemIcon,{children:/*#__PURE__*/_jsx(CheckCircleOutlineIcon,{sx:{fontSize:\"20px\"}})}),/*#__PURE__*/_jsx(ListItemText,{children:/*#__PURE__*/_jsx(Typography,{sx:{fontSize:\"16px\"},children:\"Access analytics to improve your strategy.\"})})]}),/*#__PURE__*/_jsxs(ListItem,{children:[/*#__PURE__*/_jsx(ListItemIcon,{children:/*#__PURE__*/_jsx(CheckCircleOutlineIcon,{sx:{fontSize:\"20px\"}})}),/*#__PURE__*/_jsx(ListItemText,{children:/*#__PURE__*/_jsx(Typography,{sx:{fontSize:\"16px\"},children:\"Unlock premium features for more engagement.\"})})]})]}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",onClick:()=>handleClickOpenSecond(),children:\"Get Started\"})]}),/*#__PURE__*/_jsx(Box,{sx:{objectFit:\"cover\",width:\"50%\",display:{xs:\"none\",sm:\"block\"}},children:/*#__PURE__*/_jsx(\"img\",{src:\"https://linktr.ee/_gatsby/image/1f7e31106ab8fd6cf4d62970cef6fec5/5dd82ff0dad9ac8464af794a9dc6bbeb/lsp_16x9-linktree-edm-release-may-2020-hero-socials-1_260520-024625.png?u=https%3A%2F%2Fapi.blog.production.linktr.ee%2Fwp-content%2Fuploads%2F2020%2F05%2Flsp_16x9-linktree-edm-release-may-2020-hero-socials-1_260520-024625.png&a=w%3D364%26h%3D449%26fit%3Dcrop%26crop%3Dcenter%26fm%3Dpng%26q%3D75&cd=4f02ff65d6cf6e346076e548f1a232da\"})})]})})]})]}),!isMobile&&/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,md:3,children:[activeTab===0&&/*#__PURE__*/_jsx(Box,{sx:{\"@media (min-width: 900px) and (max-width: 1200px)\":{maxHeight:\"100vh\",width:\"280px\",overflowY:\"auto\",position:\"fixed\",right:\"10px\",top:\"100px\",zIndex:1000},\"@media (max-width: 899px)\":{display:\"none\"}},children:/*#__PURE__*/_jsx(AppProfileCard,{title:\"Profile\",subheader:\"Here is your profile\",SocialLinks:SocialLinks,CustomLinks:CustomLinks,User:User,Profile:Profile})}),activeTab===1&&/*#__PURE__*/_jsx(Box,{sx:{\"@media (min-width: 900px) and (max-width: 1200px)\":{maxHeight:\"550px\",width:\"280px\",overflowY:\"auto\",position:\"fixed\",right:\"10px\",top:\"100px\",zIndex:1000},\"@media (max-width: 899px)\":{display:\"none\"// Hide on smaller screens to prevent overlap\n}},children:/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(AppLinksByProfile,{title:\"Social links\",subheader:\"Here are your social links\",type:\"socialLinks\",list:SocialLinks.map(_ref6=>{let{title,linkUrl,category,id,profileId}=_ref6;let iconn;let color;switch(category){case\"Twitter\":iconn=/*#__PURE__*/_jsx(XIcon,{});color=\"#43aff1\";break;case\"GitHub\":iconn=/*#__PURE__*/_jsx(GitHubIcon,{});color=\"#212121\";break;case\"Instagram\":iconn=/*#__PURE__*/_jsx(InstagramIcon,{});color=\"#c32aa3\";break;case\"Facebook\":iconn=/*#__PURE__*/_jsx(FacebookIcon,{});color=\"#5892d0\";break;case\"LinkedIn\":iconn=/*#__PURE__*/_jsx(LinkedInIcon,{});color=\"#00b9f1\";break;case\"TikTok\":iconn=/*#__PURE__*/_jsx(TikTokIcon,{icon:\"fab:tiktok\"});color=\"#000000\";break;case\"PhoneNumber\":iconn=/*#__PURE__*/_jsx(PhoneIcon,{});color=\"#212121\";break;default:iconn=null;color=\"#ffffff\";}return{Id:id,Title:title,LinkUrl:linkUrl,Color:color,Icon:iconn,ProfileId:profileId,Category:category};}),onDelete:handleDeleteSocialLink,onEdit:handleBringEditedSocialLink}),/*#__PURE__*/_jsx(AppLinksByProfile,{title:\"Custom links\",subheader:\"Here are your custom links\",type:\"customLinks\",list:CustomLinks.map(link=>{return{Id:link.id,ProfileId:link.profileId,Title:link.title,LinkUrl:link.linkUrl,Icon:link.icon};}),onDelete:handleDeleteCustomLink,onEdit:handleBringEditedCustomLink}),/*#__PURE__*/_jsx(AppLinksByProfile,{title:\"Contact links\",subheader:\"Here are your contact links\",type:\"contactLinks\",list:((_profile$contacts=profile.contacts)===null||_profile$contacts===void 0?void 0:_profile$contacts.filter(contact=>contact.category===\"Phone\"||contact.category===\"PhoneNumber\"||contact.category===\"Gmail\"||contact.category===\"Email\"||contact.category===\"WhatsApp\").map(contact=>{let iconClass;let color;switch(contact.category){case\"Phone\":case\"PhoneNumber\":iconClass=\"fas fa-phone\";color=\"#0d90e0\";break;case\"Gmail\":case\"Email\":iconClass=\"fab fa-google\";color=\"#EA4335\";break;case\"WhatsApp\":iconClass=\"fab fa-whatsapp\";color=\"#25D366\";break;default:iconClass=\"fas fa-phone\";color=\"#0d90e0\";}return{Id:contact.id,Title:contact.title||contact.category,LinkUrl:contact.contactInfo,Color:color,Icon:iconClass,Category:contact.category};}))||[],onDelete:handleDeleteContact,onEdit:handleEditContact})]})})]}),isMobile&&!isProfileCardVisible&&/*#__PURE__*/_jsx(Button,{disableRipple:true,color:\"primary\",onClick:()=>setIsProfileCardVisible(prev=>!prev),variant:\"contained\",sx:{margin:\"10px 21px 20px \",zIndex:1000,position:\"fixed\",right:\"1rem\",bottom:\"1rem\",borderRadius:\"50%\",height:\"55px\",minWidth:\"5px\"},children:/*#__PURE__*/_jsx(PhoneIphoneIcon,{})}),/*#__PURE__*/_jsx(Dialog,{open:isProfileCardVisible,fullScreen:true,children:/*#__PURE__*/_jsxs(DialogContent,{children:[/*#__PURE__*/_jsx(IconButton,{sx:{position:\"fixed\",top:16,right:16,zIndex:9999,backgroundColor:\"rgba(255, 255, 255, 0.9)\",\"&:hover\":{backgroundColor:\"rgba(255, 255, 255, 1)\"}},onClick:()=>setIsProfileCardVisible(false),\"aria-label\":\"close\",children:/*#__PURE__*/_jsx(CloseIcon,{})}),/*#__PURE__*/_jsx(AppProfileCard,{title:\"Profile\",subheader:\"Here is your profile\",SocialLinks:SocialLinks,CustomLinks:CustomLinks,User:User,Profile:Profile}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(AppLinksByProfile,{title:\"Social links\",subheader:\"Here are your social links\",type:\"socialLinks\",list:SocialLinks.map(_ref7=>{let{title,linkUrl,category,id,profileId}=_ref7;let iconn;let color;switch(category){case\"Twitter\":iconn=/*#__PURE__*/_jsx(XIcon,{});color=\"#43aff1\";break;case\"GitHub\":case\"Phone\":iconn=/*#__PURE__*/_jsx(GitHubIcon,{});color=\"#212121\";break;case\"Instagram\":iconn=/*#__PURE__*/_jsx(InstagramIcon,{});color=\"#c32aa3\";break;case\"Facebook\":iconn=/*#__PURE__*/_jsx(FacebookIcon,{});color=\"#5892d0\";break;case\"LinkedIn\":iconn=/*#__PURE__*/_jsx(LinkedInIcon,{});color=\"#00b9f1\";break;case\"TikTok\":iconn=/*#__PURE__*/_jsx(Iconify,{icon:\"fab:tiktok\"});color=\"#000000\";break;default:iconn=null;color=\"#ffffff\";}return{Id:id,Title:title,LinkUrl:linkUrl,Color:color,Icon:iconn,ProfileId:profileId,Category:category};}),onDelete:handleDeleteSocialLink,onEdit:handleBringEditedSocialLink,ProfileCardVisible:setIsProfileCardVisible}),/*#__PURE__*/_jsx(AppLinksByProfile,{title:\"Custom links\",subheader:\"Here are your custom links\",type:\"customLinks\",list:CustomLinks.map(link=>{return{Id:link.id,ProfileId:link.profileId,Title:link.title,LinkUrl:link.linkUrl,Icon:link.icon};}),onDelete:handleDeleteCustomLink,onEdit:handleBringEditedCustomLink,ProfileCardVisible:setIsProfileCardVisible}),/*#__PURE__*/_jsx(AppLinksByProfile,{title:\"Contact links\",subheader:\"Here are your contact links\",type:\"contactLinks\",list:((_profile$contacts2=profile.contacts)===null||_profile$contacts2===void 0?void 0:_profile$contacts2.filter(contact=>contact.category===\"Phone\"||contact.category===\"PhoneNumber\"||contact.category===\"Gmail\"||contact.category===\"Email\"||contact.category===\"WhatsApp\").map(contact=>{let iconClass;let color;switch(contact.category){case\"Phone\":case\"PhoneNumber\":iconClass=\"fas fa-phone\";color=\"#0d90e0\";break;case\"Gmail\":case\"Email\":iconClass=\"fab fa-google\";color=\"#EA4335\";break;case\"WhatsApp\":iconClass=\"fab fa-whatsapp\";color=\"#25D366\";break;default:iconClass=\"fas fa-phone\";color=\"#0d90e0\";}return{Id:contact.id,Title:contact.title||contact.category,LinkUrl:contact.contactInfo,Color:color,Icon:iconClass,Category:contact.category,ContactInfo:contact.contactInfo};}))||[],onDelete:handleDeleteContact,onEdit:handleEditContact,ProfileCardVisible:setIsProfileCardVisible})]})]})})]}),activeTab===1&&/*#__PURE__*/_jsx(Box,{sx:{\"@media (max-width: 899px)\":{display:\"block\",mt:3,mb:2},\"@media (min-width: 900px)\":{display:\"none\"}},children:/*#__PURE__*/_jsx(Grid,{container:true,spacing:2,children:/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(AppLinksByProfile,{title:\"Social links\",subheader:\"Here are your social links\",type:\"socialLinks\",list:SocialLinks.map(_ref8=>{let{title,linkUrl,category,id,profileId}=_ref8;let iconn;let color;switch(category){case\"Twitter\":iconn=/*#__PURE__*/_jsx(XIcon,{});color=\"#43aff1\";break;case\"Instagram\":iconn=/*#__PURE__*/_jsx(InstagramIcon,{});color=\"#e4405f\";break;case\"Facebook\":iconn=/*#__PURE__*/_jsx(FacebookIcon,{});color=\"#3b5998\";break;case\"LinkedIn\":iconn=/*#__PURE__*/_jsx(LinkedInIcon,{});color=\"#0077b5\";break;case\"TikTok\":iconn=/*#__PURE__*/_jsx(Iconify,{icon:\"fab:tiktok\"});color=\"#000000\";break;case\"YouTube\":iconn=/*#__PURE__*/_jsx(YouTubeIcon,{});color=\"#ff0000\";break;case\"TikTok\":iconn=/*#__PURE__*/_jsx(Iconify,{icon:\"fab:tiktok\"});color=\"#000000\";break;case\"Snapchat\":iconn=/*#__PURE__*/_jsx(LanguageIcon,{});color=\"#fffc00\";break;case\"Pinterest\":iconn=/*#__PURE__*/_jsx(PinterestIcon,{});color=\"#bd081c\";break;case\"Reddit\":iconn=/*#__PURE__*/_jsx(RedditIcon,{});color=\"#ff4500\";break;case\"Twitch\":iconn=/*#__PURE__*/_jsx(LanguageIcon,{});color=\"#9146ff\";break;case\"Discord\":iconn=/*#__PURE__*/_jsx(LanguageIcon,{});color=\"#7289da\";break;case\"Telegram\":iconn=/*#__PURE__*/_jsx(TelegramIcon,{});color=\"#0088cc\";break;case\"WhatsApp\":iconn=/*#__PURE__*/_jsx(WhatsAppIcon,{});color=\"#25d366\";break;case\"Spotify\":iconn=/*#__PURE__*/_jsx(SpotifyIcon,{});color=\"#1db954\";break;case\"SoundCloud\":iconn=/*#__PURE__*/_jsx(SpotifyIcon,{});color=\"#ff5500\";break;case\"Behance\":iconn=/*#__PURE__*/_jsx(LanguageIcon,{});color=\"#1769ff\";break;case\"Dribbble\":iconn=/*#__PURE__*/_jsx(LanguageIcon,{});color=\"#ea4c89\";break;case\"GitHub\":iconn=/*#__PURE__*/_jsx(GitHubIcon,{});color=\"#333\";break;case\"Website\":iconn=/*#__PURE__*/_jsx(LanguageIcon,{});color=\"#007bff\";break;default:iconn=/*#__PURE__*/_jsx(LanguageIcon,{});color=\"#007bff\";}return{Id:id,Title:title,LinkUrl:linkUrl,Color:color,Icon:iconn,Category:category};}),onDelete:handleDeleteSocialLink,onEdit:handleBringEditedSocialLink,ProfileCardVisible:setIsProfileCardVisible}),/*#__PURE__*/_jsx(AppLinksByProfile,{title:\"Contact links\",subheader:\"Here are your contact links\",type:\"contactLinks\",list:((_profile$contacts3=profile.contacts)===null||_profile$contacts3===void 0?void 0:_profile$contacts3.filter(contact=>[\"Gmail\",\"Email\",\"WhatsApp\",\"PhoneNumber\"].includes(contact.category)).map(contact=>{let iconClass;let color;switch(contact.category){case\"Gmail\":case\"Email\":iconClass=/*#__PURE__*/_jsx(EmailIcon,{});color=\"#ea4335\";break;case\"WhatsApp\":iconClass=/*#__PURE__*/_jsx(WhatsAppIcon,{});color=\"#25d366\";break;case\"PhoneNumber\":iconClass=/*#__PURE__*/_jsx(PhoneIcon,{});color=\"#007bff\";break;default:iconClass=/*#__PURE__*/_jsx(PhoneIcon,{});color=\"#007bff\";}return{Id:contact.id,Title:contact.title||contact.category,LinkUrl:contact.contactInfo,Color:color,Icon:iconClass,Category:contact.category};}))||[],onDelete:handleDeleteContact,onEdit:handleEditContact,ProfileCardVisible:setIsProfileCardVisible})]})})})})]})]});};export default ProfileUser;", "map": {"version": 3, "names": ["useState", "TextField", "Grid", "Avatar", "<PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Tab", "Tabs", "Card", "<PERSON><PERSON><PERSON><PERSON>", "IconButton", "Box", "<PERSON><PERSON><PERSON><PERSON>", "Container", "CircularProgress", "List", "ListItem", "ListItemIcon", "ListItemText", "Divider", "<PERSON><PERSON><PERSON>", "tooltipClasses", "useNavigate", "<PERSON><PERSON><PERSON>", "styled", "CreateIcon", "CloseIcon", "SaveIcon", "InstagramIcon", "FacebookIcon", "GitHubIcon", "CheckCircleOutlineIcon", "LinkedInIcon", "PhoneIcon", "PhoneIphoneIcon", "XIcon", "EmailIcon", "WhatsAppIcon", "YouTubeIcon", "PinterestIcon", "RedditIcon", "TelegramIcon", "SpotifyIcon", "LanguageIcon", "Typography", "AppLinksByProfile", "AppProfileCard", "EditProfile", "DeleteContact", "GetSocialLinks", "EditCustomLink", "EditSocialLink", "CreateSocialLink", "CreateCustomLink", "DeleteSocialLink", "GetCustomLinks", "DeleteCustomLink", "useEffect", "toast", "PhotoSelector", "useProfile", "PhoneLinkDialog", "EmailLinkDialog", "WhatsAppLinkDialog", "motion", "EmojiPeopleIcon", "BusinessCenterIcon", "EngineeringIcon", "ApartmentIcon", "Iconify", "Appearance", "SvgIcon", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "TikTokIcon", "props", "children", "d", "BootstrapTooltip", "_ref", "className", "arrow", "classes", "popper", "_ref2", "theme", "color", "tooltip", "backgroundColor", "ProfileUser", "_profile$contacts", "_profile$contacts2", "_profile$contacts3", "profile", "fetchProfile", "activeTab", "setActiveTab", "isMobile", "setIsMobile", "isProfileCardVisible", "setIsProfileCardVisible", "isVisible", "setIsVisible", "isSaveButtonActive", "setIsSaveButtonActive", "isProfileSaving", "setIsProfileSaving", "isCustomLinkSaving", "setIsCustomLinkSaving", "isSocialLinkSaving", "setIsSocialLinkSaving", "navigate", "handleTabChange", "event", "newValue", "getBundleIcon", "category", "fontSize", "sx", "User", "setUser", "id", "email", "firstName", "lastName", "budget", "Profile", "setProfile", "userId", "userName", "birthDate", "gender", "profilePicture", "profileCoverPicture", "profilePictureFrame", "occupation", "isPremium", "user", "socialLinks", "customLinks", "premium", "isSearch", "country", "isLoading", "setIsLoading", "SocialLinks", "setSocialLinks", "CustomLinks", "setCustomLinks", "newSocialLink", "setNewSocialLink", "ProfileId", "LinkUrl", "Category", "Title", "Color", "newCustomLink", "setNewCustomLink", "Icon", "editCustomSectionVisible", "setEditCustomSectionVisible", "editedCustomLink", "setEditedCustomLink", "Id", "editSocialSectionVisible", "setEditSocialSectionVisible", "editedSocialLink", "setEditedSocialLink", "open", "<PERSON><PERSON><PERSON>", "CategoryChosen", "setCategoryChosen", "handleClickOpen", "title", "text", "prevNewSocialLink", "platform", "icon", "label", "dialogTitle", "PhoneLinks", "handleUserChange", "name", "value", "target", "hasNonSpaceCharacter", "trim", "isValidInput", "test", "prevUser", "handleProfileChange", "prevProfile", "handleSearchChange", "handleNewSocialLinkChange", "handleNewCustomLinkChange", "prevNewCustomLink", "setValidationStatus", "validationStatus", "handlePhotoSelect", "photoDataUrl", "prevData", "handleCoverPhotoSelect", "handleSave", "response", "status", "success", "position", "autoClose", "Error", "error", "console", "message", "handleBringEditedCustomLink", "link", "handleDeleteContact", "contactId", "window", "confirm", "log", "handleEditContact", "contact", "setEditingContact", "setOpenPhoneDialog", "setOpenEmailDialog", "setOpenWhatsAppDialog", "handleEditedCustomLinkChange", "prevLink", "handleCustomLinkPhotoSelectEdit", "photo", "handleCustomLinkEdit", "isValidCustomURL", "fetchCustomLinksData", "handleBringEditedSocialLink", "handleEditedSocialLinkChange", "handleSocialLinkEdit", "isValidURL", "regex", "matches", "match", "domain", "toLowerCase", "fetchSocialLinksData", "handleDeleteCustomLink", "handleDeleteSocialLink", "fetchUserData", "redirectToLogin", "handleResize", "innerWidth", "addEventListener", "fetchData", "Promise", "all", "removeEventListener", "data", "handleClose", "handleDone", "localStorage", "setItem", "handleCustomLinkDone", "handleCustomLinkClose", "handleCustomLinkPhotoSelect", "openSecondDialog", "setOpenSecondDialog", "openPhoneDialog", "openEmailDialog", "openWhatsAppDialog", "editingContact", "openCategoryChooseDialog", "setOpenCategoryChooseDialog", "handleClickOpenSecond", "linkUrl", "isFormValid", "input", "urlPattern", "phonePattern", "isURL", "isPhoneNumber", "socialMediaDomains", "isSocialMedia", "some", "RegExp", "isCategoryLike", "IconFromPlatform", "cursor", "transition", "paddingRight", "content", "property", "location", "href", "div", "initial", "opacity", "y", "animate", "duration", "ease", "display", "justifyContent", "alignItems", "minHeight", "flexDirection", "gap", "size", "variant", "marginBottom", "onChange", "container", "spacing", "marginTop", "item", "xs", "md", "max<PERSON><PERSON><PERSON>", "p", "mb", "width", "height", "borderRadius", "background", "border", "boxShadow", "mt", "sm", "top", "right", "onClick", "handleIsSearchChange", "subheader", "focused", "disabled", "margin", "style", "marginRight", "onClose", "map", "_ref3", "lg", "borderColor", "padding", "src", "onSelect", "overflow", "left", "fontWeight", "lineHeight", "pb", "pt", "fullWidth", "textTransform", "transform", "zIndex", "textAlign", "flex", "_ref4", "min<PERSON><PERSON><PERSON>", "my", "borderWidth", "borderStyle", "_ref5", "alt", "helperText", "autoFocus", "type", "required", "clearEditingContact", "gutterBottom", "component", "objectFit", "maxHeight", "overflowY", "list", "_ref6", "profileId", "iconn", "onDelete", "onEdit", "contacts", "filter", "iconClass", "contactInfo", "disable<PERSON><PERSON><PERSON>", "prev", "bottom", "fullScreen", "_ref7", "ProfileCardVisible", "ContactInfo", "_ref8", "includes"], "sources": ["C:/Users/<USER>/Desktop/iDigitalX/ClientApp/src/pages/profileUser.js"], "sourcesContent": ["import { useState } from \"react\";\r\nimport {\r\n  TextField,\r\n  Grid,\r\n  Avatar,\r\n  Button,\r\n  Dialog,\r\n  DialogTitle,\r\n  DialogContent,\r\n  DialogActions,\r\n  Tab,\r\n  Tabs,\r\n  Card,\r\n  CardContent,\r\n  IconButton,\r\n  Box,\r\n  CardHeader,\r\n  Container,\r\n  CircularProgress,\r\n  List,\r\n  ListItem,\r\n  ListItemIcon,\r\n  ListItemText,\r\n  Divider,\r\n} from \"@mui/material\";\r\n\r\nimport Tooltip, { tooltipClasses } from \"@mui/material/Tooltip\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { Helmet } from \"react-helmet-async\";\r\nimport { styled } from \"@mui/material/styles\";\r\nimport CreateIcon from \"@mui/icons-material/Create\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport SaveIcon from \"@mui/icons-material/Save\";\r\n\r\nimport InstagramIcon from \"@mui/icons-material/Instagram\";\r\nimport FacebookIcon from \"@mui/icons-material/Facebook\";\r\nimport GitHubIcon from \"@mui/icons-material/GitHub\";\r\nimport CheckCircleOutlineIcon from \"@mui/icons-material/CheckCircleOutline\";\r\nimport LinkedInIcon from \"@mui/icons-material/LinkedIn\";\r\nimport PhoneIcon from \"@mui/icons-material/Phone\";\r\nimport PhoneIphoneIcon from \"@mui/icons-material/PhoneIphone\";\r\nimport XIcon from \"@mui/icons-material/X\";\r\nimport EmailIcon from \"@mui/icons-material/Email\";\r\nimport WhatsAppIcon from \"@mui/icons-material/WhatsApp\";\r\nimport YouTubeIcon from \"@mui/icons-material/YouTube\";\r\nimport PinterestIcon from \"@mui/icons-material/Pinterest\";\r\nimport RedditIcon from \"@mui/icons-material/Reddit\";\r\nimport TelegramIcon from \"@mui/icons-material/Telegram\";\r\nimport SpotifyIcon from \"@mui/icons-material/MusicNote\";\r\nimport LanguageIcon from \"@mui/icons-material/Language\";\r\nimport Typography from \"@mui/material/Typography\";\r\nimport AppLinksByProfile from \"../sections/@dashboard/app/AppLinksByProfile\";\r\nimport { AppProfileCard } from \"../sections/@dashboard/app\";\r\nimport { EditProfile } from \"../ProfileData.ts\";\r\nimport { DeleteContact } from \"../ContactData.ts\";\r\nimport {\r\n  GetSocialLinks,\r\n  EditCustomLink,\r\n  EditSocialLink,\r\n  CreateSocialLink,\r\n  CreateCustomLink,\r\n  DeleteSocialLink,\r\n  GetCustomLinks,\r\n  DeleteCustomLink,\r\n} from \"../LinkData.ts\";\r\nimport { useEffect } from \"react\";\r\nimport { toast } from \"react-toastify\";\r\nimport \"react-toastify/dist/ReactToastify.css\";\r\nimport PhotoSelector from \"../sections/auth/signup/PhotoSelector\";\r\nimport { useProfile } from \"../Context/ProfileContext\";\r\nimport PhoneLinkDialog from \"../sections/@dashboard/Link/PhoneLinkDialog\";\r\nimport EmailLinkDialog from \"../sections/@dashboard/Link/EmailLinkDialog\";\r\nimport WhatsAppLinkDialog from \"../sections/@dashboard/Link/WhatsAppLinkDialog\";\r\nimport { motion } from \"framer-motion\";\r\nimport EmojiPeopleIcon from \"@mui/icons-material/EmojiPeople\";\r\nimport BusinessCenterIcon from \"@mui/icons-material/BusinessCenter\";\r\nimport EngineeringIcon from \"@mui/icons-material/Engineering\";\r\nimport ApartmentIcon from \"@mui/icons-material/Apartment\";\r\nimport Iconify from \"../components/iconify\";\r\nimport Appearance from \"./Appearance\";\r\nimport { SvgIcon } from \"@mui/material\";\r\n\r\nfunction TikTokIcon(props) {\r\n  return (\r\n    <SvgIcon {...props}>\r\n      <path d=\"M12.95 2c.52 1.97 2.08 3.38 4.05 3.59v3.27a7.49 7.49 0 0 1-3.64-.98v5.77c0 3.58-3.2 6.54-7.15 5.38A5.55 5.55 0 0 1 4 13.3c0-2.74 2.05-5.06 4.83-5.35v3.28c-.89.18-1.55.97-1.55 1.93 0 1.08.9 1.96 2 1.96s2-.88 2-1.96V2h1.67z\" />\r\n    </SvgIcon>\r\n  );\r\n}\r\n\r\n// import About from \"./About\";\r\n\r\nconst BootstrapTooltip = styled(({ className, ...props }) => (\r\n  <Tooltip {...props} arrow classes={{ popper: className }} />\r\n))(({ theme }) => ({\r\n  [`& .${tooltipClasses.arrow}`]: {\r\n    color: \"#ee705e\",\r\n  },\r\n  [`& .${tooltipClasses.tooltip}`]: {\r\n    backgroundColor: \"#ee705e\",\r\n  },\r\n}));\r\n\r\nconst ProfileUser = () => {\r\n  const { profile, fetchProfile } = useProfile();\r\n  const [activeTab, setActiveTab] = useState(0);\r\n  const [isMobile, setIsMobile] = useState(false);\r\n  const [isProfileCardVisible, setIsProfileCardVisible] = useState(false);\r\n  const [isVisible, setIsVisible] = useState(true);\r\n  const [isSaveButtonActive, setIsSaveButtonActive] = useState(false);\r\n  const [isProfileSaving, setIsProfileSaving] = useState(false);\r\n  const [isCustomLinkSaving, setIsCustomLinkSaving] = useState(false);\r\n  const [isSocialLinkSaving, setIsSocialLinkSaving] = useState(false);\r\n  const navigate = useNavigate();\r\n\r\n  const handleTabChange = (event, newValue) => {\r\n    setActiveTab(newValue);\r\n  };\r\n\r\n  // Function to get bundle icon based on category\r\n  const getBundleIcon = (category) => {\r\n    switch (category) {\r\n      case \"Free\":\r\n        return <EmojiPeopleIcon fontSize=\"large\" sx={{ color: \"#ff715b\" }} />;\r\n      case \"Student\":\r\n        return (\r\n          <BusinessCenterIcon fontSize=\"large\" sx={{ color: \"#ff715b\" }} />\r\n        );\r\n      case \"Freelance\":\r\n        return <EngineeringIcon fontSize=\"large\" sx={{ color: \"#ff715b\" }} />;\r\n      case \"Enterprise\":\r\n        return <ApartmentIcon fontSize=\"large\" sx={{ color: \"#ff715b\" }} />;\r\n      default:\r\n        return <EmojiPeopleIcon fontSize=\"large\" sx={{ color: \"#ff715b\" }} />;\r\n    }\r\n  };\r\n\r\n  const [User, setUser] = useState({\r\n    id: 0,\r\n    email: \"\",\r\n\r\n    firstName: \"\",\r\n    lastName: \"\",\r\n    category: \"\",\r\n    budget: 0.0,\r\n  });\r\n\r\n  const [Profile, setProfile] = useState({\r\n    id: 0,\r\n    userId: 0,\r\n    userName: \"\",\r\n    birthDate: \"\",\r\n    gender: \"\",\r\n    profilePicture: \"\",\r\n    profileCoverPicture: \"\",\r\n    profilePictureFrame: 0,\r\n    occupation: \"\",\r\n    isPremium: false,\r\n    user: null,\r\n    socialLinks: null,\r\n    customLinks: null,\r\n    premium: null,\r\n    isSearch: null,\r\n    country: null,\r\n  });\r\n\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [SocialLinks, setSocialLinks] = useState([]);\r\n\r\n  const [CustomLinks, setCustomLinks] = useState([]);\r\n\r\n  const [newSocialLink, setNewSocialLink] = useState({\r\n    ProfileId: 0,\r\n    LinkUrl: \"\",\r\n    Category: \"\",\r\n    Title: \"\",\r\n    Color: \"\",\r\n  });\r\n\r\n  const [newCustomLink, setNewCustomLink] = useState({\r\n    ProfileId: 0,\r\n    LinkUrl: \"\",\r\n    Title: \"\",\r\n    Color: \"Gray\",\r\n    Icon: null,\r\n  });\r\n\r\n  const [editCustomSectionVisible, setEditCustomSectionVisible] =\r\n    useState(false);\r\n\r\n  const [editedCustomLink, setEditedCustomLink] = useState({\r\n    Id: 0,\r\n    ProfileId: 0,\r\n    LinkUrl: \"\",\r\n    Title: \"\",\r\n    Icon: null,\r\n  });\r\n\r\n  const [editSocialSectionVisible, setEditSocialSectionVisible] =\r\n    useState(false);\r\n\r\n  const [editedSocialLink, setEditedSocialLink] = useState({\r\n    Id: 0,\r\n    ProfileId: 0,\r\n    Title: \"\",\r\n    LinkUrl: \"\",\r\n    Category: \"\",\r\n    Color: \"\",\r\n  });\r\n\r\n  const [open, setOpen] = useState(false);\r\n  const [CategoryChosen, setCategoryChosen] = useState(false);\r\n\r\n  const handleClickOpen = (title, text, color) => {\r\n    setOpen(true);\r\n    setNewSocialLink((prevNewSocialLink) => ({\r\n      ...prevNewSocialLink,\r\n      Category: text,\r\n      Color: color,\r\n    }));\r\n  };\r\n\r\n  const socialLinks = [\r\n    {\r\n      platform: \"Twitter\",\r\n      icon: \"X\",\r\n      label: \"Can you provide your Twitter account link?\",\r\n      dialogTitle: \"Twitter link\",\r\n      color: \"linear-gradient(to bottom,#0d90e0, #1DA1F2)\",\r\n    },\r\n    {\r\n      platform: \"GitHub\",\r\n      icon: \"GitHub\",\r\n      label: \"Can you provide your GitHub account link?\",\r\n      dialogTitle: \"GitHub link\",\r\n      color:\r\n        \"linear-gradient(112.1deg, rgb(63, 76, 119) 11.4%, rgb(32, 38, 57) 70.2%)\",\r\n    },\r\n    {\r\n      platform: \"Instagram\",\r\n      icon: \"Instagram\",\r\n      label: \"Can you provide your Insta account link?\",\r\n      dialogTitle: \"insta link\",\r\n      color: \"linear-gradient(90deg, #f46f30, #c32aa3)\",\r\n    },\r\n    {\r\n      platform: \"Facebook\",\r\n      icon: \"Facebook\",\r\n      label: \"Can you provide your Facebook account link?\",\r\n      dialogTitle: \"facebook link\",\r\n      color: \"linear-gradient(180deg, #1877f2, #3b5998)\",\r\n    },\r\n    {\r\n      platform: \"TikTok\",\r\n      icon: \"TikTok\",\r\n      label: \"Can you provide your TikTok account link?\",\r\n      dialogTitle: \"TikTok link\",\r\n      color: \"linear-gradient(180deg, #000000, #ff0050)\",\r\n    },\r\n    {\r\n      platform: \"LinkedIn\",\r\n      icon: \"LinkedIn\",\r\n      label: \"Can you provide your LinkedIn account link?\",\r\n      dialogTitle: \"LinkedIn link\",\r\n      color: \"linear-gradient(135deg, #0077B5, #00A0DC)\",\r\n    },\r\n  ];\r\n\r\n  const PhoneLinks = [\r\n    {\r\n      platform: \"Phone\",\r\n      icon: \"Phone\",\r\n      label: \"Can you provide your Phone number?\",\r\n      dialogTitle: \"Phone link\",\r\n      color: \"linear-gradient(to bottom,#0d90e0, #1DA1F2)\",\r\n    },\r\n    {\r\n      platform: \"Email\",\r\n      icon: \"Email\",\r\n      label: \"Can you provide your Email address?\",\r\n      dialogTitle: \"Email link\",\r\n      color: \"linear-gradient(to bottom,#EA4335, #FBBC05)\",\r\n    },\r\n    {\r\n      platform: \"WhatsApp\",\r\n      icon: \"WhatsApp\",\r\n      label: \"Can you provide your WhatsApp number?\",\r\n      dialogTitle: \"WhatsApp link\",\r\n      color: \"linear-gradient(to bottom,#25D366, #128C7E)\",\r\n    },\r\n  ];\r\n\r\n  const handleUserChange = (event) => {\r\n    const { name, value } = event.target;\r\n\r\n    const hasNonSpaceCharacter = value.trim() !== \"\";\r\n\r\n    const isValidInput = /^[a-zA-Z\\s]+$/.test(value);\r\n\r\n    if (name === \"firstName\" || name === \"lastName\") {\r\n      if (isValidInput && hasNonSpaceCharacter) {\r\n        setUser((prevUser) => ({\r\n          ...prevUser,\r\n          [name]: value,\r\n        }));\r\n        setIsSaveButtonActive(true);\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleProfileChange = (event) => {\r\n    const { name, value } = event.target;\r\n\r\n    setProfile((prevProfile) => ({\r\n      ...prevProfile,\r\n      [name]: value,\r\n    }));\r\n    setIsSaveButtonActive(true);\r\n  };\r\n\r\n  const handleSearchChange = (value) => {\r\n    setProfile((prevProfile) => ({\r\n      ...prevProfile,\r\n      isSearch: value,\r\n    }));\r\n    setIsSaveButtonActive(true);\r\n  };\r\n\r\n  const handleNewSocialLinkChange = (event) => {\r\n    const { name, value } = event.target;\r\n    setNewSocialLink((prevNewSocialLink) => ({\r\n      ...prevNewSocialLink,\r\n      [name]: value,\r\n    }));\r\n  };\r\n\r\n  const handleNewCustomLinkChange = (event) => {\r\n    const { name, value } = event.target;\r\n    setNewCustomLink((prevNewCustomLink) => ({\r\n      ...prevNewCustomLink,\r\n      [name]: value,\r\n    }));\r\n\r\n    setValidationStatus({\r\n      ...validationStatus,\r\n      [name]: value !== \"\",\r\n    });\r\n  };\r\n\r\n  const handlePhotoSelect = (photoDataUrl) => {\r\n    setProfile((prevData) => ({\r\n      ...prevData,\r\n      profilePicture: photoDataUrl,\r\n    }));\r\n    setIsSaveButtonActive(true);\r\n  };\r\n\r\n  const handleCoverPhotoSelect = (photoDataUrl) => {\r\n    setProfile((prevData) => ({\r\n      ...prevData,\r\n      profileCoverPicture: photoDataUrl,\r\n    }));\r\n    setIsSaveButtonActive(true);\r\n  };\r\n\r\n  const handleSave = async () => {\r\n    setIsProfileSaving(true);\r\n    try {\r\n      const response = await EditProfile(User, Profile);\r\n\r\n      if (response && response.status === 200) {\r\n        await fetchProfile();\r\n        toast.success(\"Profile saved successfully\", {\r\n          position: \"top-center\",\r\n          autoClose: 1000,\r\n        });\r\n        setIsSaveButtonActive(false);\r\n      } else {\r\n        throw new Error(\"Failed to save profile\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error saving profile:\", error);\r\n      toast.error(`Error saving profile: ${error.message || \"Unknown error\"}`, {\r\n        position: \"top-center\",\r\n        autoClose: 3000,\r\n      });\r\n    } finally {\r\n      setIsProfileSaving(false);\r\n    }\r\n  };\r\n\r\n  // edit Custom links\r\n  const handleBringEditedCustomLink = async (link) => {\r\n    setEditedCustomLink(link);\r\n    setEditCustomSectionVisible(true);\r\n    // Switch to links tab when editing from mobile\r\n    if (isMobile) {\r\n      setActiveTab(1);\r\n    }\r\n  };\r\n\r\n  const handleDeleteContact = async (contactId) => {\r\n    if (!window.confirm(\"Are you sure you want to delete this contact?\")) {\r\n      return;\r\n    }\r\n\r\n    console.log(\"Attempting to delete contact with ID:\", contactId);\r\n    try {\r\n      const response = await DeleteContact(contactId);\r\n      console.log(\"Delete response:\", response);\r\n      if (response) {\r\n        toast.success(\"Contact deleted successfully\", {\r\n          position: \"top-center\",\r\n          autoClose: 1000,\r\n        });\r\n        fetchProfile();\r\n      } else {\r\n        toast.error(\"No response from server\", {\r\n          position: \"top-center\",\r\n          autoClose: 1000,\r\n        });\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error deleting contact:\", error);\r\n      toast.error(`Error deleting contact: ${error.message || error}`, {\r\n        position: \"top-center\",\r\n        autoClose: 3000,\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleEditContact = (contact) => {\r\n    // Set the contact being edited\r\n    setEditingContact(contact);\r\n\r\n    // Open the appropriate dialog based on contact category\r\n    if (contact.Category === \"Phone\" || contact.Category === \"PhoneNumber\") {\r\n      setOpenPhoneDialog(true);\r\n    } else if (contact.Category === \"Gmail\" || contact.Category === \"Email\") {\r\n      setOpenEmailDialog(true);\r\n    } else if (contact.Category === \"WhatsApp\") {\r\n      setOpenWhatsAppDialog(true);\r\n    }\r\n\r\n    // Switch to links tab when editing from mobile\r\n    if (isMobile) {\r\n      setActiveTab(1);\r\n    }\r\n  };\r\n\r\n  const handleEditedCustomLinkChange = async (event) => {\r\n    const { name, value } = event.target;\r\n    setEditedCustomLink((prevLink) => ({\r\n      ...prevLink,\r\n      [name]: value,\r\n    }));\r\n  };\r\n\r\n  const handleCustomLinkPhotoSelectEdit = (photoDataUrl) => {\r\n    setEditedCustomLink((prevLink) => ({\r\n      ...prevLink,\r\n      Icon: photoDataUrl,\r\n    }));\r\n    setValidationStatus({\r\n      ...validationStatus,\r\n      photo: photoDataUrl !== null,\r\n    });\r\n  };\r\n\r\n  const handleCustomLinkEdit = async () => {\r\n    setIsCustomLinkSaving(true);\r\n    try {\r\n      if (!isValidCustomURL(editedCustomLink.LinkUrl)) {\r\n        toast.error(\"Invalid URL format\", {\r\n          position: \"top-center\",\r\n          autoClose: 2000,\r\n        });\r\n        return;\r\n      }\r\n\r\n      if (editedCustomLink.Title === \"\") {\r\n        toast.error(\"Title can't be empty\", {\r\n          position: \"top-center\",\r\n          autoClose: 2000,\r\n        });\r\n        return;\r\n      }\r\n\r\n      await EditCustomLink(editedCustomLink);\r\n\r\n      setEditedCustomLink({\r\n        Id: 0,\r\n        ProfileId: 0,\r\n        LinkUrl: \"\",\r\n        Title: \"\",\r\n      });\r\n\r\n      setEditCustomSectionVisible(false);\r\n\r\n      toast.success(\"Link edited\", {\r\n        position: \"top-center\",\r\n        autoClose: 1000,\r\n      });\r\n\r\n      fetchCustomLinksData();\r\n    } catch (error) {\r\n      toast.error(error, {\r\n        position: \"top-center\",\r\n        autoClose: 1000,\r\n      });\r\n      console.error(\"Error saving Custom link data:\", error.message);\r\n    } finally {\r\n      setIsCustomLinkSaving(false);\r\n    }\r\n  };\r\n\r\n  // edit Social link\r\n  const handleBringEditedSocialLink = async (link) => {\r\n    setEditedSocialLink(link);\r\n    setEditSocialSectionVisible(true);\r\n    // Switch to links tab when editing from mobile\r\n    if (isMobile) {\r\n      setActiveTab(1);\r\n    }\r\n  };\r\n\r\n  const handleEditedSocialLinkChange = async (event) => {\r\n    const { name, value } = event.target;\r\n    setEditedSocialLink((prevLink) => ({\r\n      ...prevLink,\r\n      [name]: value,\r\n    }));\r\n  };\r\n\r\n  const handleSocialLinkEdit = async () => {\r\n    setIsSocialLinkSaving(true);\r\n    try {\r\n      if (!isValidURL(editedSocialLink.LinkUrl)) {\r\n        toast.error(\"Invalid URL format\", {\r\n          position: \"top-center\",\r\n          autoClose: 2000,\r\n        });\r\n        return;\r\n      }\r\n\r\n      if (editedSocialLink.Title === \"\") {\r\n        toast.error(\"Title can't be empty\", {\r\n          position: \"top-center\",\r\n          autoClose: 2000,\r\n        });\r\n        return;\r\n      }\r\n\r\n      const regex = /^(?:https?:\\/\\/)?(?:www\\.)?([^\\/.]+)/i;\r\n      const matches = editedSocialLink.LinkUrl.match(regex);\r\n      const domain = matches ? matches[1] : null;\r\n\r\n      if (domain !== editedSocialLink.Category.toLowerCase()) {\r\n        toast.error(\"You cant change the category of the link\", {\r\n          position: \"top-center\",\r\n          autoClose: 2000,\r\n        });\r\n        return;\r\n      }\r\n\r\n      await EditSocialLink(editedSocialLink);\r\n\r\n      setEditedSocialLink({\r\n        Id: 0,\r\n        ProfileId: 0,\r\n        Title: \"\",\r\n        LinkUrl: \"\",\r\n        Category: \"\",\r\n        Color: \"\",\r\n      });\r\n\r\n      setEditSocialSectionVisible(false);\r\n      setCategoryChosen(false);\r\n\r\n      toast.success(\"Link edited\", {\r\n        position: \"top-center\",\r\n        autoClose: 1000,\r\n      });\r\n\r\n      fetchSocialLinksData();\r\n    } catch (error) {\r\n      toast.error(error, {\r\n        position: \"top-center\",\r\n        autoClose: 1000,\r\n      });\r\n      console.error(\"Error saving Social link data:\", error.message);\r\n    } finally {\r\n      setIsSocialLinkSaving(false);\r\n    }\r\n  };\r\n\r\n  const handleDeleteCustomLink = async (Id) => {\r\n    // Add confirmation dialog\r\n    if (!window.confirm(\"Are you sure you want to delete this custom link?\")) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const response = await DeleteCustomLink(Id);\r\n      if (response != null) {\r\n        toast.success(\"Custom link deleted successfully\", {\r\n          position: \"top-center\",\r\n          autoClose: 1000,\r\n        });\r\n        fetchCustomLinksData();\r\n      }\r\n    } catch (error) {\r\n      toast.error(\"Failed to delete custom link\", {\r\n        position: \"top-center\",\r\n        autoClose: 1000,\r\n      });\r\n      console.error(\"Error deleting custom link:\", error);\r\n    }\r\n  };\r\n\r\n  const handleDeleteSocialLink = async (Id) => {\r\n    // Add confirmation dialog\r\n    if (!window.confirm(\"Are you sure you want to delete this social link?\")) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const response = await DeleteSocialLink(Id);\r\n      if (response != null) {\r\n        toast.success(\"Social link deleted successfully\", {\r\n          position: \"top-center\",\r\n          autoClose: 1000,\r\n        });\r\n        fetchSocialLinksData();\r\n      }\r\n    } catch (error) {\r\n      toast.error(\"Failed to delete social link\", {\r\n        position: \"top-center\",\r\n        autoClose: 1000,\r\n      });\r\n      console.error(\"Error deleting social link:\", error);\r\n    }\r\n  };\r\n\r\n  const fetchUserData = async () => {\r\n    try {\r\n      setUser({\r\n        id: profile.id,\r\n        email: profile.email,\r\n        firstName: profile.firstName,\r\n        lastName: profile.lastName,\r\n        category: profile.category,\r\n      });\r\n      setProfile(profile.profile);\r\n\r\n      setNewSocialLink((prevNewSocialLink) => ({\r\n        ...prevNewSocialLink,\r\n        ProfileId: profile.profile.id,\r\n      }));\r\n\r\n      setNewCustomLink((prevNewCustomLink) => ({\r\n        ...prevNewCustomLink,\r\n        ProfileId: profile.profile.id,\r\n      }));\r\n    } catch (error) {\r\n      if (error.redirectToLogin) {\r\n        navigate(\"/Login\");\r\n      }\r\n    }\r\n  };\r\n\r\n  // Update local state when profile context changes\r\n  useEffect(() => {\r\n    if (profile && profile.profile) {\r\n      fetchUserData();\r\n    }\r\n  }, [profile]);\r\n\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setIsMobile(window.innerWidth <= 768);\r\n    };\r\n\r\n    window.addEventListener(\"resize\", handleResize);\r\n\r\n    // Call handleResize immediately to set the initial state\r\n    handleResize();\r\n\r\n    // Fetch data when component mounts\r\n    const fetchData = async () => {\r\n      if (profile && profile.profile) {\r\n        setIsLoading(true);\r\n        try {\r\n          await Promise.all([\r\n            fetchUserData(),\r\n            fetchSocialLinksData(),\r\n            fetchCustomLinksData(),\r\n          ]);\r\n        } catch (error) {\r\n          console.error(\"Error fetching profile data:\", error);\r\n        } finally {\r\n          setIsLoading(false);\r\n        }\r\n      }\r\n    };\r\n\r\n    fetchData();\r\n\r\n    return () => {\r\n      window.removeEventListener(\"resize\", handleResize);\r\n    };\r\n  }, []);\r\n\r\n  const fetchSocialLinksData = async () => {\r\n    try {\r\n      const response = await GetSocialLinks();\r\n      setSocialLinks(response.data);\r\n    } catch (error) {\r\n      console.error(\"Error fetching social links data:\", error);\r\n    }\r\n  };\r\n\r\n  const fetchCustomLinksData = async () => {\r\n    try {\r\n      const response = await GetCustomLinks();\r\n      setCustomLinks(response.data);\r\n    } catch (error) {\r\n      console.error(\"Error fetching social links data:\", error);\r\n    }\r\n  };\r\n\r\n  const handleClose = () => {\r\n    setOpen(false);\r\n    setNewSocialLink((prevNewSocialLink) => ({\r\n      ...prevNewSocialLink,\r\n      LinkUrl: \"\",\r\n      Category: \"\",\r\n      Title: \"\",\r\n      Color: \"\",\r\n    }));\r\n  };\r\n\r\n  const handleDone = async () => {\r\n    if (!isValidURL(newSocialLink.LinkUrl)) {\r\n      toast.error(\"Invalid URL format\", {\r\n        position: \"top-center\",\r\n        autoClose: 2000,\r\n      });\r\n      return;\r\n    }\r\n\r\n    const response = await CreateSocialLink(newSocialLink);\r\n    localStorage.setItem(\"isLinksCardVisible\", \"true\");\r\n    handleClose();\r\n    if (response) {\r\n      fetchSocialLinksData();\r\n      toast.success(\"Social link created\", {\r\n        position: \"top-center\",\r\n        autoClose: 1000,\r\n      });\r\n    } else {\r\n      toast.error(\"Error while creating social link\", {\r\n        position: \"top-center\",\r\n        autoClose: 1000,\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleCustomLinkDone = async () => {\r\n    if (!isValidCustomURL(newCustomLink.LinkUrl)) {\r\n      toast.error(\"Invalid URL format\", {\r\n        position: \"top-center\",\r\n        autoClose: 2000,\r\n      });\r\n      return;\r\n    }\r\n\r\n    const response = await CreateCustomLink(newCustomLink);\r\n\r\n    if (response) {\r\n      fetchCustomLinksData();\r\n      toast.success(\"Custom link created\", {\r\n        position: \"top-center\",\r\n        autoClose: 1000,\r\n      });\r\n      handleCustomLinkClose();\r\n    } else {\r\n      toast.error(\"Error while creating custom link\", {\r\n        position: \"top-center\",\r\n        autoClose: 1000,\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleCustomLinkPhotoSelect = (photoDataUrl) => {\r\n    setNewCustomLink((prevData) => ({\r\n      ...prevData,\r\n      Icon: photoDataUrl,\r\n    }));\r\n    setValidationStatus({\r\n      ...validationStatus,\r\n      photo: photoDataUrl !== null,\r\n    });\r\n  };\r\n\r\n  // Second Dialog\r\n  const [openSecondDialog, setOpenSecondDialog] = useState(false);\r\n  const [openPhoneDialog, setOpenPhoneDialog] = useState(false);\r\n  const [openEmailDialog, setOpenEmailDialog] = useState(false);\r\n  const [openWhatsAppDialog, setOpenWhatsAppDialog] = useState(false);\r\n  const [editingContact, setEditingContact] = useState(null);\r\n\r\n  const [openCategoryChooseDialog, setOpenCategoryChooseDialog] =\r\n    useState(false);\r\n\r\n  // Second Dialog Handlers\r\n  const handleClickOpenSecond = () => {\r\n    setOpenSecondDialog(true);\r\n  };\r\n\r\n  const handleCustomLinkClose = () => {\r\n    setNewCustomLink((prevData) => ({\r\n      ...prevData,\r\n      Title: \"\",\r\n      LinkUrl: \"\",\r\n      Icon: \"\",\r\n    }));\r\n    setOpenSecondDialog(false);\r\n  };\r\n\r\n  //lazem create\r\n  const [validationStatus, setValidationStatus] = useState({\r\n    title: false,\r\n    linkUrl: false,\r\n    photo: false,\r\n  });\r\n\r\n  const isFormValid =\r\n    newCustomLink.Title.trim() !== \"\" && newCustomLink.LinkUrl.trim() !== \"\";\r\n\r\n  const isValidURL = (input) => {\r\n    // Regular expression pattern to match URLs that start with http or https\r\n    const urlPattern =\r\n      /^(https?:\\/\\/)([\\da-z.-]+)\\.([a-z.]{2,6})(\\/[^?#]*)?(\\?[^#]*)?(#.*)?$/i;\r\n\r\n    // Regular expression pattern to match phone numbers with 8 digits\r\n    const phonePattern = /^\\d{8}$/;\r\n\r\n    // Check if the input matches URL pattern\r\n    const isURL = urlPattern.test(input);\r\n\r\n    // Check if the input matches phone number pattern\r\n    const isPhoneNumber = phonePattern.test(input);\r\n\r\n    // List of social media domains\r\n    const socialMediaDomains = [\r\n      \"facebook.com\",\r\n      \"twitter.com\",\r\n      \"instagram.com\",\r\n      \"linkedin.com\",\r\n      \"instagram.com\",\r\n      \"github.com\",\r\n      \"tiktok.com\",\r\n    ];\r\n\r\n    // Check if the input matches any social media domain and starts with http or https\r\n    const isSocialMedia = socialMediaDomains.some((domain) =>\r\n      new RegExp(`^https?:\\/\\/(?:www\\.)?${domain}`, \"i\").test(input)\r\n    );\r\n\r\n    const isCategoryLike = new RegExp(\r\n      `^https?:\\/\\/(?:www\\.)?${newSocialLink.Category}`,\r\n      \"i\"\r\n    ).test(input);\r\n\r\n    // Return true if it's a valid URL with http/https, a valid social media URL with http/https, OR a valid phone number\r\n    return (isURL && isSocialMedia && isCategoryLike) || isPhoneNumber;\r\n  };\r\n\r\n  const isValidCustomURL = (input) => {\r\n    // Regular expression pattern to match URLs that start with http or https\r\n    const urlPattern =\r\n      /^(https?:\\/\\/)([\\da-z.-]+)\\.([a-z.]{2,6})(\\/[^?#]*)?(\\?[^#]*)?(#.*)?$/i;\r\n\r\n    // Check if the input matches URL pattern\r\n    const isURL = urlPattern.test(input);\r\n\r\n    return isURL;\r\n  };\r\n\r\n  const IconFromPlatform = (platform) => {\r\n    switch (platform) {\r\n      case \"Twitter\":\r\n        return (\r\n          <XIcon\r\n            sx={{\r\n              fontSize: \"36px\",\r\n              cursor: \"pointer\",\r\n              transition: \"color 0.3s\",\r\n              \"&:hover\": {\r\n                color: \"#212121\",\r\n              },\r\n            }}\r\n          />\r\n        );\r\n      case \"GitHub\":\r\n        return (\r\n          <GitHubIcon\r\n            sx={{\r\n              fontSize: \"36px\",\r\n              cursor: \"pointer\",\r\n              transition: \"color 0.3s\",\r\n              \"&:hover\": {\r\n                color: \"#212121\",\r\n              },\r\n            }}\r\n          />\r\n        );\r\n      case \"Instagram\":\r\n        return (\r\n          <InstagramIcon\r\n            sx={{\r\n              fontSize: \"35px\",\r\n              cursor: \"pointer\",\r\n              transition: \"color 0.3s\",\r\n              \"&:hover\": {\r\n                color: \"#D81B60\",\r\n              },\r\n            }}\r\n          />\r\n        );\r\n      case \"Facebook\":\r\n        return (\r\n          <FacebookIcon\r\n            sx={{\r\n              fontSize: \"35px\",\r\n              cursor: \"pointer\",\r\n              transition: \"color 0.3s\",\r\n              \"&:hover\": {\r\n                color: \"#5892d0\",\r\n              },\r\n            }}\r\n          />\r\n        );\r\n      case \"LinkedIn\":\r\n        return (\r\n          <LinkedInIcon\r\n            sx={{\r\n              fontSize: \"35px\",\r\n              cursor: \"pointer\",\r\n              transition: \"color 0.3s\",\r\n              \"&:hover\": {\r\n                color: \"#00b9f1\",\r\n              },\r\n            }}\r\n          />\r\n        );\r\n      case \"TikTok\":\r\n        return (\r\n          <TikTokIcon\r\n            icon=\"fab:tiktok\"\r\n            sx={{\r\n              fontSize: \"35px\",\r\n              cursor: \"pointer\",\r\n              transition: \"color 0.3s\",\r\n              \"&:hover\": {\r\n                color: \"#000\",\r\n              },\r\n            }}\r\n          />\r\n        );\r\n      case \"Phone\":\r\n        return (\r\n          <PhoneIcon\r\n            sx={{\r\n              fontSize: \"35px\",\r\n              cursor: \"pointer\",\r\n              transition: \"color 0.3s\",\r\n              \"&:hover\": {\r\n                color: \"#4CAF50\",\r\n              },\r\n            }}\r\n          />\r\n        );\r\n\r\n      case \"Gmail\":\r\n      case \"Email\":\r\n        return (\r\n          <EmailIcon\r\n            sx={{\r\n              fontSize: \"35px\",\r\n              cursor: \"pointer\",\r\n              transition: \"color 0.3s\",\r\n              \"&:hover\": {\r\n                color: \"#EA4335\",\r\n              },\r\n            }}\r\n          />\r\n        );\r\n      case \"WhatsApp\":\r\n        return (\r\n          <WhatsAppIcon\r\n            sx={{\r\n              fontSize: \"35px\",\r\n              cursor: \"pointer\",\r\n              transition: \"color 0.3s\",\r\n              \"&:hover\": {\r\n                color: \"#25D366\",\r\n              },\r\n            }}\r\n          />\r\n        );\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Container\r\n      sx={{\r\n        \"@media (min-width: 900px)\": {\r\n          paddingRight: \"0\", // Remove default padding to prevent overlap with sidebar\r\n        },\r\n      }}\r\n    >\r\n      <Helmet>\r\n        <title>IDigics | Profile</title>\r\n        <meta\r\n          name=\"description\"\r\n          content={`Manage your IDigics profile${\r\n            User.firstName && User.lastName\r\n              ? ` - ${User.firstName} ${User.lastName}`\r\n              : \"\"\r\n          }${\r\n            Profile.occupation ? `, ${Profile.occupation}` : \"\"\r\n          }. Update your social links, contact information, and professional details.`}\r\n        />\r\n\r\n        {/* Open Graph meta tags */}\r\n        <meta\r\n          property=\"og:title\"\r\n          content={`${\r\n            User.firstName && User.lastName\r\n              ? `${User.firstName} ${User.lastName} | `\r\n              : \"\"\r\n          }IDigics Profile`}\r\n        />\r\n        <meta\r\n          property=\"og:description\"\r\n          content={`Manage your IDigics profile${\r\n            User.firstName && User.lastName\r\n              ? ` - ${User.firstName} ${User.lastName}`\r\n              : \"\"\r\n          }${\r\n            Profile.occupation ? `, ${Profile.occupation}` : \"\"\r\n          }. Update your social links, contact information, and professional details.`}\r\n        />\r\n        <meta property=\"og:type\" content=\"profile\" />\r\n        <meta property=\"og:url\" content={window.location.href} />\r\n        {Profile.profilePicture && (\r\n          <meta property=\"og:image\" content={Profile.profilePicture} />\r\n        )}\r\n        <meta property=\"og:site_name\" content=\"IDigics\" />\r\n\r\n        {/* Twitter Card meta tags */}\r\n        <meta name=\"twitter:card\" content=\"summary_large_image\" />\r\n        <meta\r\n          name=\"twitter:title\"\r\n          content={`${\r\n            User.firstName && User.lastName\r\n              ? `${User.firstName} ${User.lastName} | `\r\n              : \"\"\r\n          }IDigics Profile`}\r\n        />\r\n        <meta\r\n          name=\"twitter:description\"\r\n          content={`Manage your IDigics profile${\r\n            User.firstName && User.lastName\r\n              ? ` - ${User.firstName} ${User.lastName}`\r\n              : \"\"\r\n          }${\r\n            Profile.occupation ? `, ${Profile.occupation}` : \"\"\r\n          }. Update your social links, contact information, and professional details.`}\r\n        />\r\n        {Profile.profilePicture && (\r\n          <meta name=\"twitter:image\" content={Profile.profilePicture} />\r\n        )}\r\n\r\n        {/* Additional profile-specific meta tags */}\r\n        {User.firstName && (\r\n          <meta property=\"profile:first_name\" content={User.firstName} />\r\n        )}\r\n        {User.lastName && (\r\n          <meta property=\"profile:last_name\" content={User.lastName} />\r\n        )}\r\n        {Profile.userName && (\r\n          <meta property=\"profile:username\" content={Profile.userName} />\r\n        )}\r\n      </Helmet>\r\n      {isLoading ? (\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{\r\n            duration: 0.5,\r\n            ease: \"easeOut\",\r\n          }}\r\n        >\r\n          <Box\r\n            sx={{\r\n              display: \"flex\",\r\n              justifyContent: \"center\",\r\n              alignItems: \"center\",\r\n              minHeight: \"400px\",\r\n              flexDirection: \"column\",\r\n              gap: 2,\r\n            }}\r\n          >\r\n            <CircularProgress size={60} />\r\n            <Typography variant=\"h6\" color=\"textSecondary\">\r\n              Loading profile data...\r\n            </Typography>\r\n          </Box>\r\n        </motion.div>\r\n      ) : (\r\n        <>\r\n          {/* Tabs at the top */}\r\n          <Box\r\n            sx={{\r\n              display: \"flex\",\r\n              justifyContent: \"center\",\r\n              marginBottom: \"45px\",\r\n            }}\r\n          >\r\n            <Tabs\r\n              value={activeTab}\r\n              onChange={handleTabChange}\r\n              aria-label=\"Account tabs\"\r\n            >\r\n              <Tab label=\"Appearance\" />\r\n              {/* <Tab label=\"About (Beta)\" /> */}\r\n              <Tab label=\"Links\" />\r\n            </Tabs>\r\n          </Box>\r\n          <Grid container spacing={2} sx={{ marginTop: \"-50px\" }}>\r\n            <Grid\r\n              item\r\n              xs={12}\r\n              md={9}\r\n              sx={{\r\n                \"@media (min-width: 900px) and (max-width: 1200px)\": {\r\n                  maxWidth: \"calc(100vw - 320px)\", // Account for sidebar + margins\r\n                  paddingRight: \"20px\",\r\n                },\r\n                \"@media (min-width: 1201px)\": {\r\n                  maxWidth: \"calc(75vw - 40px)\",\r\n                  paddingRight: \"20px\",\r\n                },\r\n                \"@media (max-width: 899px)\": {\r\n                  maxWidth: \"100%\",\r\n                  paddingRight: \"0\",\r\n                },\r\n              }}\r\n            >\r\n              {User.category && (\r\n                <Card sx={{ p: 3, marginBottom: \"30px\", position: \"relative\" }}>\r\n                  <Typography\r\n                    variant=\"overline\"\r\n                    sx={{\r\n                      mb: 3,\r\n                      display: \"block\",\r\n                      color: \"text.secondary\",\r\n                    }}\r\n                  >\r\n                    Your Plan\r\n                  </Typography>\r\n                  <Box\r\n                    sx={{\r\n                      display: \"flex\",\r\n                      alignItems: \"center\",\r\n                      gap: 2,\r\n                      mb: 2,\r\n                    }}\r\n                  >\r\n                    <Box\r\n                      sx={{\r\n                        display: \"flex\",\r\n                        alignItems: \"center\",\r\n                        justifyContent: \"center\",\r\n                        width: 60,\r\n                        height: 60,\r\n                        borderRadius: \"12px\",\r\n                        background:\r\n                          \"linear-gradient(135deg, #ff715b20 0%, #e65d4710 100%)\",\r\n                        border: \"2px solid #ff715b30\",\r\n                        boxShadow: \"0 8px 32px #ff715b20\",\r\n                      }}\r\n                    >\r\n                      {getBundleIcon(User.category)}\r\n                    </Box>\r\n                    <Typography variant=\"h4\">{User.category}</Typography>\r\n                  </Box>\r\n                  {User.category !== \"Freelance\" &&\r\n                    User.category !== \"Enterprise\" && (\r\n                      <Box\r\n                        sx={{\r\n                          mt: { xs: 2, sm: 0 },\r\n                          position: { sm: \"absolute\" },\r\n                          top: { sm: 24 },\r\n                          right: { sm: 24 },\r\n                        }}\r\n                      >\r\n                        <Button\r\n                          size=\"small\"\r\n                          variant=\"outlined\"\r\n                          onClick={() => {\r\n                            navigate(\"/admin/bundles\");\r\n                          }}\r\n                        >\r\n                          Upgrade plan\r\n                        </Button>\r\n                      </Box>\r\n                    )}\r\n                </Card>\r\n              )}\r\n              {activeTab === 0 && (\r\n                <Appearance\r\n                  Profile={Profile}\r\n                  User={User}\r\n                  isSaveButtonActive={isSaveButtonActive}\r\n                  setIsSaveButtonActive={setIsSaveButtonActive}\r\n                  isProfileSaving={isProfileSaving}\r\n                  handlePhotoSelect={handlePhotoSelect}\r\n                  handleProfileChange={handleProfileChange}\r\n                  handleUserChange={handleUserChange}\r\n                  handleSave={handleSave}\r\n                  handleIsSearchChange={handleSearchChange}\r\n                  handleCoverPhotoSelect={handleCoverPhotoSelect}\r\n                />\r\n              )}\r\n              {/* {activeTab === 1 && <About />} */}\r\n              {activeTab === 1 && (\r\n                <Grid container spacing={2}>\r\n                  {/* Create Links */}\r\n                  <Grid item xs={12} md={12}>\r\n                    {/* edit Social */}\r\n                    {editSocialSectionVisible && (\r\n                      <Grid item xs={12} md={12} sx={{ marginBottom: \"10px\" }}>\r\n                        <Card>\r\n                          <CardHeader\r\n                            title=\"Edit your links\"\r\n                            subheader=\"This is where you can edit your entire profile! Here, you can manage and edit your 'About' section.\"\r\n                          />\r\n                          <CardContent>\r\n                            <Grid container spacing={2}>\r\n                              <IconButton\r\n                                sx={{\r\n                                  position: \"absolute\",\r\n                                  right: 8,\r\n                                  top: 8,\r\n                                }}\r\n                                aria-label=\"close\"\r\n                                onClick={() => {\r\n                                  setEditSocialSectionVisible(false);\r\n                                  setCategoryChosen(false);\r\n                                }}\r\n                              >\r\n                                <CloseIcon />\r\n                              </IconButton>\r\n                              <Grid item xs={9} md={7}>\r\n                                <TextField\r\n                                  name=\"Title\"\r\n                                  label=\"Type your link title\"\r\n                                  focused\r\n                                  value={editedSocialLink.Title}\r\n                                  sx={{ width: \"100%\" }}\r\n                                  onChange={handleEditedSocialLinkChange}\r\n                                />\r\n                              </Grid>\r\n                              <Grid item xs={12} md={12}>\r\n                                <TextField\r\n                                  name=\"LinkUrl\"\r\n                                  label=\"Type your link url\"\r\n                                  value={editedSocialLink.LinkUrl}\r\n                                  focused\r\n                                  sx={{ width: \"100%\" }}\r\n                                  onChange={handleEditedSocialLinkChange}\r\n                                />\r\n                              </Grid>\r\n                            </Grid>\r\n                          </CardContent>\r\n                          <Button\r\n                            onClick={handleSocialLinkEdit}\r\n                            color=\"primary\"\r\n                            variant=\"outlined\"\r\n                            disabled={isSocialLinkSaving}\r\n                            sx={{\r\n                              margin: \"25px\",\r\n                              backgroundColor: \"#ee705e\",\r\n                              color: \"white\",\r\n                              \"&:hover\": {\r\n                                color: \"#ee705e\",\r\n                              },\r\n                            }}\r\n                          >\r\n                            <span\r\n                              style={{\r\n                                marginRight: \"10px\",\r\n                              }}\r\n                            >\r\n                              {isSocialLinkSaving\r\n                                ? \"Saving...\"\r\n                                : \"Save your link\"}\r\n                            </span>\r\n                            {isSocialLinkSaving ? (\r\n                              <CircularProgress size={20} color=\"inherit\" />\r\n                            ) : (\r\n                              <SaveIcon />\r\n                            )}\r\n                          </Button>\r\n\r\n                          <Dialog\r\n                            open={openCategoryChooseDialog}\r\n                            onClose={() => {\r\n                              setOpenCategoryChooseDialog(false);\r\n                            }}\r\n                          >\r\n                            <DialogTitle color=\"primary\">\r\n                              Choose a website\r\n                            </DialogTitle>\r\n\r\n                            <DialogContent>\r\n                              <Grid\r\n                                sx={{\r\n                                  display: \"flex\",\r\n                                }}\r\n                                container\r\n                                spacing={2}\r\n                              >\r\n                                {socialLinks.map(({ platform }) => {\r\n                                  let icon = IconFromPlatform(platform);\r\n                                  return (\r\n                                    <Grid\r\n                                      item\r\n                                      xs={3}\r\n                                      sm={6}\r\n                                      md={2}\r\n                                      lg={2}\r\n                                      sx={{\r\n                                        display: \"flex\",\r\n                                        justifyContent: \"center\",\r\n                                        marginTop: \"5px\",\r\n                                      }}\r\n                                      key={platform}\r\n                                    >\r\n                                      <BootstrapTooltip\r\n                                        title={platform}\r\n                                        sx={{\r\n                                          \"& .MuiTooltip-tooltip\": {\r\n                                            fontSize: \"13px\",\r\n                                          },\r\n                                        }}\r\n                                      >\r\n                                        <Button\r\n                                          variant=\"outlined\"\r\n                                          sx={{\r\n                                            color: \"rgba(20, 43, 58, 0.5)\",\r\n                                            borderColor:\r\n                                              \"rgba(20, 43, 58, 0.3)\",\r\n                                            height: \"100%\",\r\n                                            padding: \"15px 20px\",\r\n                                          }}\r\n                                          onClick={() => {\r\n                                            setEditedSocialLink((prevLink) => ({\r\n                                              ...prevLink,\r\n                                              Category: platform,\r\n                                            }));\r\n                                            setOpenCategoryChooseDialog(false);\r\n                                            setCategoryChosen(true);\r\n                                          }}\r\n                                        >\r\n                                          {icon}\r\n                                        </Button>\r\n                                      </BootstrapTooltip>\r\n                                    </Grid>\r\n                                  );\r\n                                })}\r\n                              </Grid>\r\n                            </DialogContent>\r\n                            <DialogActions></DialogActions>\r\n                          </Dialog>\r\n                        </Card>\r\n                      </Grid>\r\n                    )}\r\n                    {/* edit Custom */}\r\n                    {editCustomSectionVisible && (\r\n                      <Grid\r\n                        item\r\n                        xs={12}\r\n                        md={12}\r\n                        sx={{ marginTop: \"10px\", marginBottom: \"10px\" }}\r\n                      >\r\n                        <Card>\r\n                          <CardHeader\r\n                            title=\"Edit your links\"\r\n                            subheader=\"Analyze the daily views to understand the trends and patterns in the number of views your content receives.Gain valuable insights into the most active days and make informed decisions based on this data.\"\r\n                          />\r\n                          <CardContent>\r\n                            <Grid container spacing={2}>\r\n                              <IconButton\r\n                                sx={{\r\n                                  position: \"absolute\",\r\n                                  right: 8,\r\n                                  top: 8,\r\n                                }}\r\n                                aria-label=\"close\"\r\n                                onClick={() => {\r\n                                  setEditCustomSectionVisible(false);\r\n                                }}\r\n                              >\r\n                                <CloseIcon />\r\n                              </IconButton>\r\n                              <Grid item xs={10} md={11}>\r\n                                <TextField\r\n                                  name=\"Title\"\r\n                                  label=\"Type your link title\"\r\n                                  value={editedCustomLink.Title}\r\n                                  focused\r\n                                  sx={{ width: \"100%\" }}\r\n                                  onChange={handleEditedCustomLinkChange}\r\n                                />\r\n                              </Grid>\r\n                              <Grid item xs={1} md={1}>\r\n                                <Avatar\r\n                                  style={{\r\n                                    width: \"3rem\",\r\n                                    height: \"3rem\",\r\n                                  }}\r\n                                  focused\r\n                                  src={editedCustomLink.Icon}\r\n                                />\r\n                                <PhotoSelector\r\n                                  onSelect={handleCustomLinkPhotoSelectEdit}\r\n                                />\r\n                              </Grid>\r\n                              <Grid item xs={12} md={12}>\r\n                                <TextField\r\n                                  name=\"LinkUrl\"\r\n                                  label=\"Type your link url\"\r\n                                  value={editedCustomLink.LinkUrl}\r\n                                  focused\r\n                                  sx={{ width: \"100%\" }}\r\n                                  onChange={handleEditedCustomLinkChange}\r\n                                />\r\n                              </Grid>\r\n                            </Grid>\r\n                          </CardContent>\r\n                          <Button\r\n                            onClick={handleCustomLinkEdit}\r\n                            color=\"primary\"\r\n                            variant=\"outlined\"\r\n                            disabled={isCustomLinkSaving}\r\n                            sx={{\r\n                              margin: \"25px\",\r\n                              backgroundColor: \"#ee705e\",\r\n                              color: \"white\",\r\n                              \"&:hover\": {\r\n                                color: \"#ee705e\",\r\n                              },\r\n                            }}\r\n                          >\r\n                            <span\r\n                              style={{\r\n                                marginRight: \"10px\",\r\n                              }}\r\n                            >\r\n                              {isCustomLinkSaving\r\n                                ? \"Saving...\"\r\n                                : \"Save your link\"}\r\n                            </span>\r\n                            {isCustomLinkSaving ? (\r\n                              <CircularProgress size={20} color=\"inherit\" />\r\n                            ) : (\r\n                              <SaveIcon />\r\n                            )}\r\n                          </Button>\r\n                        </Card>\r\n                      </Grid>\r\n                    )}\r\n                    {/* Custom Links */}\r\n                    <Card\r\n                      sx={{\r\n                        background:\r\n                          \"linear-gradient(135deg, #ff715b08, #ff715b03)\",\r\n                        border: \"1px solid #ff715b20\",\r\n                        boxShadow: \"0 8px 32px rgba(255, 113, 91, 0.12)\",\r\n                        borderRadius: \"16px\",\r\n                        overflow: \"hidden\",\r\n                        position: \"relative\",\r\n                        \"&::before\": {\r\n                          content: '\"\"',\r\n                          position: \"absolute\",\r\n                          top: 0,\r\n                          left: 0,\r\n                          right: 0,\r\n                          height: \"4px\",\r\n                          background:\r\n                            \"linear-gradient(90deg, #ff715b, #e65d47)\",\r\n                        },\r\n                      }}\r\n                    >\r\n                      <CardHeader\r\n                        title={\r\n                          <Box\r\n                            sx={{\r\n                              display: \"flex\",\r\n                              alignItems: \"center\",\r\n                              gap: 1,\r\n                            }}\r\n                          >\r\n                            <Box\r\n                              sx={{\r\n                                width: 40,\r\n                                height: 40,\r\n                                borderRadius: \"12px\",\r\n                                background:\r\n                                  \"linear-gradient(135deg, #ff715b, #e65d47)\",\r\n                                display: \"flex\",\r\n                                alignItems: \"center\",\r\n                                justifyContent: \"center\",\r\n                                boxShadow: \"0 4px 16px rgba(255, 113, 91, 0.3)\",\r\n                              }}\r\n                            >\r\n                              <CreateIcon\r\n                                sx={{ color: \"white\", fontSize: 20 }}\r\n                              />\r\n                            </Box>\r\n                            <Typography\r\n                              variant=\"h6\"\r\n                              sx={{ fontWeight: 700, color: \"#212B36\" }}\r\n                            >\r\n                              Create Custom Links\r\n                            </Typography>\r\n                          </Box>\r\n                        }\r\n                        subheader={\r\n                          <Typography\r\n                            variant=\"body2\"\r\n                            sx={{\r\n                              color: \"text.secondary\",\r\n                              mt: 1,\r\n                              lineHeight: 1.6,\r\n                              fontSize: \"14px\",\r\n                              marginBottom: \"14px\",\r\n                            }}\r\n                          >\r\n                            Design personalized links with custom icons and\r\n                            titles. Perfect for showcasing your portfolio,\r\n                            business, or any important links you want to share.\r\n                          </Typography>\r\n                        }\r\n                        sx={{ pb: 1 }}\r\n                      />\r\n                      <CardContent sx={{ pt: 0 }}>\r\n                        <Grid container spacing={1}>\r\n                          <Grid item xs={12} md={12}>\r\n                            <Button\r\n                              size=\"large\"\r\n                              fullWidth\r\n                              variant=\"outlined\"\r\n                              sx={{\r\n                                minHeight: \"80px\",\r\n                                background:\r\n                                  \"linear-gradient(135deg, #ff715b08, #ff715b03)\",\r\n                                border: \"2px dashed #ff715b40\",\r\n                                borderRadius: \"20px\",\r\n                                color: \"#ff715b\",\r\n                                fontSize: \"16px\",\r\n                                fontWeight: \"600\",\r\n                                textTransform: \"none\",\r\n                                position: \"relative\",\r\n                                overflow: \"hidden\",\r\n                                transition:\r\n                                  \"all 0.4s cubic-bezier(0.4, 0, 0.2, 1)\",\r\n                                \"&::before\": {\r\n                                  content: '\"\"',\r\n                                  position: \"absolute\",\r\n                                  top: 0,\r\n                                  left: \"-100%\",\r\n                                  width: \"100%\",\r\n                                  height: \"100%\",\r\n                                  background:\r\n                                    \"linear-gradient(90deg, transparent, rgba(255, 113, 91, 0.1), transparent)\",\r\n                                  transition: \"left 0.6s ease\",\r\n                                },\r\n                                \"&:hover\": {\r\n                                  background:\r\n                                    \"linear-gradient(135deg, #ff715b15, #ff715b08)\",\r\n                                  border: \"2px solid #ff715b\",\r\n                                  transform: \"translateY(-3px) scale(1.02)\",\r\n                                  boxShadow:\r\n                                    \"0 12px 35px rgba(255, 113, 91, 0.3)\",\r\n                                  \"&::before\": {\r\n                                    left: \"100%\",\r\n                                  },\r\n                                },\r\n                                \"&:active\": {\r\n                                  transform: \"translateY(-1px) scale(1.01)\",\r\n                                },\r\n                              }}\r\n                              onClick={() => handleClickOpenSecond()}\r\n                            >\r\n                              <Box\r\n                                sx={{\r\n                                  display: \"flex\",\r\n                                  alignItems: \"center\",\r\n                                  gap: 2.5,\r\n                                  position: \"relative\",\r\n                                  zIndex: 1,\r\n                                }}\r\n                              >\r\n                                <Box\r\n                                  sx={{\r\n                                    width: 48,\r\n                                    height: 48,\r\n                                    borderRadius: \"12px\",\r\n                                    background:\r\n                                      \"linear-gradient(135deg, #ff715b, #e65d47)\",\r\n                                    display: \"flex\",\r\n                                    alignItems: \"center\",\r\n                                    justifyContent: \"center\",\r\n                                    boxShadow:\r\n                                      \"0 4px 16px rgba(255, 113, 91, 0.4)\",\r\n                                    transition: \"all 0.3s ease\",\r\n                                  }}\r\n                                >\r\n                                  <CreateIcon\r\n                                    sx={{ fontSize: 24, color: \"white\" }}\r\n                                  />\r\n                                </Box>\r\n                                <Box sx={{ textAlign: \"left\", flex: 1 }}>\r\n                                  <Typography\r\n                                    variant=\"h7\"\r\n                                    sx={{\r\n                                      fontWeight: 700,\r\n                                      color: \"#ff715b\",\r\n                                      fontSize: \"18px\",\r\n                                      mb: 0.5,\r\n                                    }}\r\n                                  >\r\n                                    Create Custom Link\r\n                                  </Typography>\r\n                                  <Typography\r\n                                    variant=\"body2\"\r\n                                    sx={{\r\n                                      color: \"text.secondary\",\r\n                                      fontSize: \"14px\",\r\n                                      lineHeight: 1.4,\r\n                                    }}\r\n                                  >\r\n                                    Design personalized links\r\n                                  </Typography>\r\n                                </Box>\r\n                              </Box>\r\n                            </Button>\r\n                          </Grid>\r\n                        </Grid>\r\n                      </CardContent>\r\n                    </Card>\r\n                    {/* Social Links */}\r\n                    <Card\r\n                      sx={{\r\n                        marginTop: \"20px\",\r\n                        background:\r\n                          \"linear-gradient(135deg, #667eea08, #764ba203)\",\r\n                        border: \"1px solid #667eea20\",\r\n                        boxShadow: \"0 8px 32px rgba(102, 126, 234, 0.12)\",\r\n                        borderRadius: \"16px\",\r\n                        overflow: \"hidden\",\r\n                        position: \"relative\",\r\n                        \"&::before\": {\r\n                          content: '\"\"',\r\n                          position: \"absolute\",\r\n                          top: 0,\r\n                          left: 0,\r\n                          right: 0,\r\n                          height: \"4px\",\r\n                          background:\r\n                            \"linear-gradient(90deg, #667eea, #764ba2)\",\r\n                        },\r\n                      }}\r\n                    >\r\n                      <CardHeader\r\n                        title={\r\n                          <Box\r\n                            sx={{\r\n                              display: \"flex\",\r\n                              alignItems: \"center\",\r\n                              gap: 1,\r\n                            }}\r\n                          >\r\n                            <Box\r\n                              sx={{\r\n                                width: 40,\r\n                                height: 40,\r\n                                borderRadius: \"12px\",\r\n                                background:\r\n                                  \"linear-gradient(135deg, #667eea, #764ba2)\",\r\n                                display: \"flex\",\r\n                                alignItems: \"center\",\r\n                                justifyContent: \"center\",\r\n                                boxShadow:\r\n                                  \"0 4px 16px rgba(102, 126, 234, 0.3)\",\r\n                              }}\r\n                            >\r\n                              <LanguageIcon\r\n                                sx={{ color: \"white\", fontSize: 20 }}\r\n                              />\r\n                            </Box>\r\n                            <Typography\r\n                              variant=\"h6\"\r\n                              sx={{ fontWeight: 700, color: \"#212B36\" }}\r\n                            >\r\n                              Social Platforms\r\n                            </Typography>\r\n                          </Box>\r\n                        }\r\n                        subheader={\r\n                          <Typography\r\n                            variant=\"body2\"\r\n                            sx={{\r\n                              color: \"text.secondary\",\r\n                              mt: 1,\r\n                              lineHeight: 1.6,\r\n                              fontSize: \"14px\",\r\n                              marginBottom: \"14px\",\r\n                            }}\r\n                          >\r\n                            Connect your social media accounts and professional\r\n                            profiles. Choose from popular platforms and add your\r\n                            custom titles and URLs to build your online\r\n                            presence.\r\n                          </Typography>\r\n                        }\r\n                        sx={{ pb: 1 }}\r\n                      />\r\n                      <CardContent sx={{ pt: 0 }}>\r\n                        <Grid container spacing={2}>\r\n                          {socialLinks.map(({ platform, color }) => {\r\n                            let icon = IconFromPlatform(platform);\r\n                            return (\r\n                              <Grid\r\n                                item\r\n                                xs={6}\r\n                                sm={4}\r\n                                md={2.4}\r\n                                lg={2.4}\r\n                                sx={{\r\n                                  display: \"flex\",\r\n                                  justifyContent: \"center\",\r\n                                }}\r\n                                key={platform}\r\n                              >\r\n                                <BootstrapTooltip\r\n                                  title={platform}\r\n                                  sx={{\r\n                                    \"& .MuiTooltip-tooltip\": {\r\n                                      fontSize: \"13px\",\r\n                                    },\r\n                                  }}\r\n                                >\r\n                                  <Button\r\n                                    variant=\"outlined\"\r\n                                    sx={{\r\n                                      color: \"rgba(20, 43, 58, 0.5)\",\r\n                                      borderColor: \"rgba(20, 43, 58, 0.3)\",\r\n                                      height: \"100%\",\r\n                                      padding: \"15px 28px\",\r\n                                      minHeight: \"100px\",\r\n                                      minWidth: \"100px\",\r\n                                      boxShadow: `0 6px 24px ${color}18`,\r\n                                      transition:\r\n                                        \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\r\n                                      display: \"flex\",\r\n                                      flexDirection: \"column\",\r\n                                      gap: 1,\r\n                                      position: \"relative\",\r\n                                      overflow: \"hidden\",\r\n                                      \"&::before\": {\r\n                                        content: '\"\"',\r\n                                        position: \"absolute\",\r\n                                        top: 0,\r\n                                        left: 0,\r\n                                        right: 0,\r\n                                        height: \"3px\",\r\n                                        background: `linear-gradient(90deg, ${color}, ${color}80)`,\r\n                                        opacity: 0,\r\n                                        transition: \"opacity 0.3s ease\",\r\n                                      },\r\n                                      \"&:hover\": {\r\n                                        background: `linear-gradient(135deg, ${color}20, ${color}08)`,\r\n                                        border: `2px solid ${color}50`,\r\n                                        transform:\r\n                                          \"translateY(-6px) scale(1.02)\",\r\n                                        boxShadow: `0 12px 40px ${color}30`,\r\n                                        \"&::before\": {\r\n                                          opacity: 1,\r\n                                        },\r\n                                      },\r\n                                    }}\r\n                                    onClick={() =>\r\n                                      handleClickOpen(\r\n                                        \"Customize your link\",\r\n                                        platform,\r\n                                        color\r\n                                      )\r\n                                    }\r\n                                  >\r\n                                    <Box sx={{ fontSize: 32, mb: 0.5 }}>\r\n                                      {icon}\r\n                                    </Box>\r\n                                    <Typography\r\n                                      variant=\"caption\"\r\n                                      sx={{\r\n                                        fontWeight: 600,\r\n                                        fontSize: \"11px\",\r\n                                        textTransform: \"none\",\r\n                                        opacity: 0.8,\r\n                                      }}\r\n                                    >\r\n                                      {platform}\r\n                                    </Typography>\r\n                                  </Button>\r\n                                </BootstrapTooltip>\r\n                              </Grid>\r\n                            );\r\n                          })}\r\n                        </Grid>\r\n                        {/* Contact Links Section Header */}\r\n                        <Box sx={{ my: 4 }}>\r\n                          <Box\r\n                            sx={{\r\n                              display: \"flex\",\r\n                              alignItems: \"center\",\r\n                              gap: 2,\r\n                              mb: 2,\r\n                            }}\r\n                          >\r\n                            <Box\r\n                              sx={{\r\n                                width: 32,\r\n                                height: 32,\r\n                                borderRadius: \"8px\",\r\n                                background:\r\n                                  \"linear-gradient(135deg, #25D366, #128C7E)\",\r\n                                display: \"flex\",\r\n                                alignItems: \"center\",\r\n                                justifyContent: \"center\",\r\n                                boxShadow: \"0 4px 12px rgba(37, 211, 102, 0.3)\",\r\n                              }}\r\n                            >\r\n                              <PhoneIcon\r\n                                sx={{ color: \"white\", fontSize: 16 }}\r\n                              />\r\n                            </Box>\r\n                            <Typography\r\n                              variant=\"h6\"\r\n                              sx={{ fontWeight: 600, color: \"#212B36\" }}\r\n                            >\r\n                              Contact Information\r\n                            </Typography>\r\n                          </Box>\r\n                          <Typography\r\n                            variant=\"body2\"\r\n                            sx={{\r\n                              color: \"text.secondary\",\r\n                              mb: 3,\r\n                              lineHeight: 1.6,\r\n                              fontSize: \"13px\",\r\n                            }}\r\n                          >\r\n                            Add your contact details to make it easy for people\r\n                            to reach you directly.\r\n                          </Typography>\r\n                          <Divider\r\n                            sx={{\r\n                              borderColor: \"rgba(102, 126, 234, 0.2)\",\r\n                              borderWidth: \"1px\",\r\n                              borderStyle: \"dashed\",\r\n                            }}\r\n                          />\r\n                        </Box>\r\n                        <Grid container spacing={2}>\r\n                          {PhoneLinks.map(({ platform, color }) => {\r\n                            let icon = IconFromPlatform(platform);\r\n                            return (\r\n                              <Grid\r\n                                item\r\n                                xs={6}\r\n                                sm={4}\r\n                                md={2.4}\r\n                                lg={2.4}\r\n                                sx={{\r\n                                  display: \"flex\",\r\n                                  justifyContent: \"center\",\r\n                                }}\r\n                                key={platform}\r\n                              >\r\n                                <BootstrapTooltip\r\n                                  title={platform}\r\n                                  sx={{\r\n                                    \"& .MuiTooltip-tooltip\": {\r\n                                      fontSize: \"13px\",\r\n                                    },\r\n                                  }}\r\n                                >\r\n                                  <Button\r\n                                    variant=\"outlined\"\r\n                                    sx={{\r\n                                      color: \"rgba(20, 43, 58, 0.5)\",\r\n                                      borderColor: \"rgba(20, 43, 58, 0.3)\",\r\n                                      height: \"100%\",\r\n                                      padding: \"15px 20px\",\r\n                                      minHeight: \"100px\",\r\n                                      minWidth: \"100px\",\r\n                                      boxShadow: `0 6px 24px ${color}18`,\r\n                                      transition:\r\n                                        \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\r\n                                      display: \"flex\",\r\n                                      flexDirection: \"column\",\r\n                                      gap: 1,\r\n                                      position: \"relative\",\r\n                                      overflow: \"hidden\",\r\n                                      \"&::before\": {\r\n                                        content: '\"\"',\r\n                                        position: \"absolute\",\r\n                                        top: 0,\r\n                                        left: 0,\r\n                                        right: 0,\r\n                                        height: \"3px\",\r\n                                        background: `linear-gradient(90deg, ${color}, ${color}80)`,\r\n                                        opacity: 0,\r\n                                        transition: \"opacity 0.3s ease\",\r\n                                      },\r\n                                      \"&:hover\": {\r\n                                        background: `linear-gradient(135deg, ${color}20, ${color}08)`,\r\n                                        border: `2px solid ${color}50`,\r\n                                        transform:\r\n                                          \"translateY(-6px) scale(1.02)\",\r\n                                        boxShadow: `0 12px 40px ${color}30`,\r\n                                        \"&::before\": {\r\n                                          opacity: 1,\r\n                                        },\r\n                                      },\r\n                                    }}\r\n                                    onClick={() => {\r\n                                      setEditingContact(null);\r\n                                      if (platform === \"Phone\") {\r\n                                        setOpenPhoneDialog(true);\r\n                                      } else if (platform === \"Email\") {\r\n                                        setOpenEmailDialog(true);\r\n                                      } else if (platform === \"WhatsApp\") {\r\n                                        setOpenWhatsAppDialog(true);\r\n                                      }\r\n                                    }}\r\n                                  >\r\n                                    <Box sx={{ fontSize: 32, mb: 0.5 }}>\r\n                                      {icon}\r\n                                    </Box>\r\n                                    <Typography\r\n                                      variant=\"caption\"\r\n                                      sx={{\r\n                                        fontWeight: 600,\r\n                                        fontSize: \"11px\",\r\n                                        textTransform: \"none\",\r\n                                        opacity: 0.8,\r\n                                      }}\r\n                                    >\r\n                                      {platform}\r\n                                    </Typography>\r\n                                  </Button>\r\n                                </BootstrapTooltip>\r\n                              </Grid>\r\n                            );\r\n                          })}\r\n                        </Grid>\r\n                      </CardContent>\r\n                    </Card>\r\n                  </Grid>\r\n\r\n                  <Dialog\r\n                    open={openSecondDialog}\r\n                    onClose={handleCustomLinkClose}\r\n                  >\r\n                    <DialogTitle color=\"primary\">\r\n                      Create your custom link\r\n                    </DialogTitle>\r\n\r\n                    <div\r\n                      style={{\r\n                        display: \"flex\",\r\n                        alignItems: \"center\",\r\n                        justifyContent: \"center\",\r\n                      }}\r\n                    >\r\n                      <div>\r\n                        <Avatar\r\n                          style={{\r\n                            width: \"5rem\",\r\n                            height: \"5rem\",\r\n                          }}\r\n                          src={newCustomLink.Icon}\r\n                          alt=\"User Profile Photo\"\r\n                        />\r\n                        <PhotoSelector\r\n                          onSelect={handleCustomLinkPhotoSelect}\r\n                          error={\r\n                            newCustomLink.Icon === null &&\r\n                            validationStatus.photo\r\n                          }\r\n                          helperText={\r\n                            newCustomLink.Icon === null\r\n                              ? \"Photo is required\"\r\n                              : \"\"\r\n                          }\r\n                        />\r\n                      </div>\r\n                    </div>\r\n\r\n                    <DialogContent>\r\n                      <TextField\r\n                        autoFocus\r\n                        margin=\"dense\"\r\n                        name=\"Title\"\r\n                        label=\"Title\"\r\n                        type=\"text\"\r\n                        fullWidth\r\n                        required\r\n                        color=\"primary\"\r\n                        value={newCustomLink.Title}\r\n                        onChange={handleNewCustomLinkChange}\r\n                        error={\r\n                          newCustomLink.Title.trim() === \"\" &&\r\n                          validationStatus.title\r\n                        }\r\n                        helperText={\r\n                          newCustomLink.Title.trim() === \"\"\r\n                            ? \"Title is required\"\r\n                            : \"\"\r\n                        }\r\n                      />\r\n\r\n                      <TextField\r\n                        name=\"LinkUrl\"\r\n                        margin=\"dense\"\r\n                        label=\"Your link\"\r\n                        type=\"url\"\r\n                        fullWidth\r\n                        required\r\n                        value={newCustomLink.LinkUrl}\r\n                        onChange={handleNewCustomLinkChange}\r\n                        error={\r\n                          newCustomLink.LinkUrl.trim() === \"\" &&\r\n                          validationStatus.linkUrl\r\n                        }\r\n                        helperText={\r\n                          newCustomLink.LinkUrl.trim() === \"\"\r\n                            ? \"URL is required\"\r\n                            : \"\"\r\n                        }\r\n                      />\r\n                      {/* Hints and Tips Section */}\r\n                      <Box\r\n                        mt={2}\r\n                        p={2}\r\n                        sx={{\r\n                          backgroundColor: \"#f0f0f0\",\r\n                          borderRadius: \"5px\",\r\n                        }}\r\n                      >\r\n                        <Typography variant=\"subtitle1\" color=\"textPrimary\">\r\n                          Tips for Creating Your Custom Link\r\n                        </Typography>\r\n                        <Typography variant=\"body2\" color=\"textSecondary\">\r\n                          - Ensure your link is correctly formatted, e.g.,\r\n                          \"https://www.facebook.com/yourprofile\"\r\n                        </Typography>\r\n                      </Box>\r\n                    </DialogContent>\r\n                    <DialogActions>\r\n                      <Button onClick={handleCustomLinkClose}>Cancel</Button>\r\n                      <Button\r\n                        onClick={handleCustomLinkDone}\r\n                        disabled={!isFormValid}\r\n                      >\r\n                        Done\r\n                      </Button>\r\n                    </DialogActions>\r\n                  </Dialog>\r\n                  <Dialog open={open} onClose={handleClose}>\r\n                    <DialogTitle>Create your social link</DialogTitle>\r\n                    <DialogContent>\r\n                      <TextField\r\n                        name=\"Title\"\r\n                        autoFocus\r\n                        margin=\"dense\"\r\n                        label=\"Title\"\r\n                        type=\"url\"\r\n                        fullWidth\r\n                        required\r\n                        value={newSocialLink.Title}\r\n                        onChange={handleNewSocialLinkChange}\r\n                        helperText={\r\n                          newSocialLink.Title === \"\" ? \"Title is required\" : \"\"\r\n                        }\r\n                      />\r\n\r\n                      <TextField\r\n                        name=\"LinkUrl\"\r\n                        margin=\"dense\"\r\n                        id=\"linkUrl\"\r\n                        label=\"Url\"\r\n                        type=\"url\"\r\n                        fullWidth\r\n                        required\r\n                        value={newSocialLink.LinkUrl}\r\n                        onChange={handleNewSocialLinkChange}\r\n                        helperText={\r\n                          newSocialLink.LinkUrl === \"\" ? \"URL is required\" : \"\"\r\n                        }\r\n                      />\r\n                      {/* Hints and Tips Section */}\r\n                      <Box\r\n                        mt={2}\r\n                        p={2}\r\n                        sx={{\r\n                          backgroundColor: \"#f0f0f0\",\r\n                          borderRadius: \"5px\",\r\n                        }}\r\n                      >\r\n                        <Typography variant=\"subtitle1\" color=\"textPrimary\">\r\n                          Tips for Creating Predefined Link\r\n                        </Typography>\r\n                        <Typography variant=\"body2\" color=\"textSecondary\">\r\n                          - Ensure your link is correctly formatted, e.g.,\r\n                          https://www.facebook.com/yourprofile\r\n                        </Typography>\r\n                        <Typography variant=\"body2\" color=\"textSecondary\">\r\n                          - Only links from social media platforms like\r\n                          Facebook, Twitter, Instagram, LinkedIn, GitHub, and\r\n                          TikTok are accepted.\r\n                        </Typography>\r\n                        <Typography variant=\"body2\" color=\"textSecondary\">\r\n                          - For phone numbers, simply enter an 8-digit number\r\n                          without spaces or symbols.\r\n                        </Typography>\r\n                      </Box>\r\n                    </DialogContent>\r\n                    <DialogActions>\r\n                      <Button onClick={handleClose}>Cancel</Button>\r\n                      <Button\r\n                        onClick={handleDone}\r\n                        disabled={\r\n                          newSocialLink.Title === \"\" ||\r\n                          newSocialLink.LinkUrl === \"\"\r\n                        }\r\n                      >\r\n                        Done\r\n                      </Button>\r\n                    </DialogActions>\r\n                  </Dialog>\r\n                  <PhoneLinkDialog\r\n                    setOpenPhoneDialog={setOpenPhoneDialog}\r\n                    openPhoneDialog={openPhoneDialog}\r\n                    Id={profile.id}\r\n                    editingContact={editingContact}\r\n                    fetchProfile={fetchProfile}\r\n                    clearEditingContact={() => setEditingContact(null)}\r\n                  />\r\n                  <EmailLinkDialog\r\n                    setOpenEmailDialog={setOpenEmailDialog}\r\n                    openEmailDialog={openEmailDialog}\r\n                    Id={profile.id}\r\n                    editingContact={editingContact}\r\n                    fetchProfile={fetchProfile}\r\n                    clearEditingContact={() => setEditingContact(null)}\r\n                  />\r\n                  <WhatsAppLinkDialog\r\n                    setOpenWhatsAppDialog={setOpenWhatsAppDialog}\r\n                    openWhatsAppDialog={openWhatsAppDialog}\r\n                    Id={profile.id}\r\n                    editingContact={editingContact}\r\n                    fetchProfile={fetchProfile}\r\n                    clearEditingContact={() => setEditingContact(null)}\r\n                  />\r\n                  <Grid item xs={12} md={12}>\r\n                    <Card\r\n                      sx={{\r\n                        display: isVisible ? \"flex\" : \"none\",\r\n                        marginTop: \"20px\",\r\n                      }}\r\n                    >\r\n                      <CardContent>\r\n                        <IconButton\r\n                          sx={{\r\n                            position: \"absolute\",\r\n                            right: 8,\r\n                            top: 8,\r\n                          }}\r\n                          aria-label=\"close\"\r\n                          onClick={() => {\r\n                            setIsVisible(false);\r\n                            localStorage.setItem(\"isLinksCardVisible\", \"false\");\r\n                          }}\r\n                        >\r\n                          <CloseIcon />\r\n                        </IconButton>\r\n                        <Typography gutterBottom variant=\"h6\" component=\"div\">\r\n                          Create Your Custom Link!\r\n                        </Typography>\r\n                        <List>\r\n                          <ListItem>\r\n                            <ListItemIcon>\r\n                              <CheckCircleOutlineIcon\r\n                                sx={{\r\n                                  fontSize: \"20px\",\r\n                                }}\r\n                              />\r\n                            </ListItemIcon>\r\n                            <ListItemText>\r\n                              <Typography\r\n                                sx={{\r\n                                  fontSize: \"16px\",\r\n                                }}\r\n                              >\r\n                                Automate content with dynamic feeds and images.\r\n                              </Typography>\r\n                            </ListItemText>\r\n                          </ListItem>\r\n                          <ListItem>\r\n                            <ListItemIcon>\r\n                              <CheckCircleOutlineIcon\r\n                                sx={{\r\n                                  fontSize: \"20px\",\r\n                                }}\r\n                              />\r\n                            </ListItemIcon>\r\n                            <ListItemText>\r\n                              <Typography\r\n                                sx={{\r\n                                  fontSize: \"16px\",\r\n                                }}\r\n                              >\r\n                                Use your own domain to boost branding.\r\n                              </Typography>\r\n                            </ListItemText>\r\n                          </ListItem>\r\n                          <ListItem>\r\n                            <ListItemIcon>\r\n                              <CheckCircleOutlineIcon\r\n                                sx={{\r\n                                  fontSize: \"20px\",\r\n                                }}\r\n                              />\r\n                            </ListItemIcon>\r\n                            <ListItemText>\r\n                              <Typography\r\n                                sx={{\r\n                                  fontSize: \"16px\",\r\n                                }}\r\n                              >\r\n                                Access analytics to improve your strategy.\r\n                              </Typography>\r\n                            </ListItemText>\r\n                          </ListItem>\r\n                          <ListItem>\r\n                            <ListItemIcon>\r\n                              <CheckCircleOutlineIcon\r\n                                sx={{\r\n                                  fontSize: \"20px\",\r\n                                }}\r\n                              />\r\n                            </ListItemIcon>\r\n                            <ListItemText>\r\n                              <Typography\r\n                                sx={{\r\n                                  fontSize: \"16px\",\r\n                                }}\r\n                              >\r\n                                Unlock premium features for more engagement.\r\n                              </Typography>\r\n                            </ListItemText>\r\n                          </ListItem>\r\n                        </List>\r\n                        <Button\r\n                          variant=\"contained\"\r\n                          onClick={() => handleClickOpenSecond()}\r\n                        >\r\n                          Get Started\r\n                        </Button>\r\n                      </CardContent>\r\n                      <Box\r\n                        sx={{\r\n                          objectFit: \"cover\",\r\n                          width: \"50%\",\r\n                          display: {\r\n                            xs: \"none\",\r\n                            sm: \"block\",\r\n                          },\r\n                        }}\r\n                      >\r\n                        <img src=\"https://linktr.ee/_gatsby/image/1f7e31106ab8fd6cf4d62970cef6fec5/5dd82ff0dad9ac8464af794a9dc6bbeb/lsp_16x9-linktree-edm-release-may-2020-hero-socials-1_260520-024625.png?u=https%3A%2F%2Fapi.blog.production.linktr.ee%2Fwp-content%2Fuploads%2F2020%2F05%2Flsp_16x9-linktree-edm-release-may-2020-hero-socials-1_260520-024625.png&a=w%3D364%26h%3D449%26fit%3Dcrop%26crop%3Dcenter%26fm%3Dpng%26q%3D75&cd=4f02ff65d6cf6e346076e548f1a232da\" />\r\n                      </Box>\r\n                    </Card>\r\n                  </Grid>\r\n                </Grid>\r\n              )}\r\n            </Grid>\r\n            {!isMobile && (\r\n              <Grid item xs={12} md={3}>\r\n                {/* Profile */}\r\n                {activeTab === 0 && (\r\n                  <Box\r\n                    sx={{\r\n                      \"@media (min-width: 900px) and (max-width: 1200px)\": {\r\n                        maxHeight: \"100vh\",\r\n                        width: \"280px\",\r\n                        overflowY: \"auto\",\r\n                        position: \"fixed\",\r\n                        right: \"10px\",\r\n                        top: \"100px\",\r\n                        zIndex: 1000,\r\n                      },\r\n                      \"@media (max-width: 899px)\": {\r\n                        display: \"none\",\r\n                      },\r\n                    }}\r\n                  >\r\n                    <AppProfileCard\r\n                      title=\"Profile\"\r\n                      subheader=\"Here is your profile\"\r\n                      SocialLinks={SocialLinks}\r\n                      CustomLinks={CustomLinks}\r\n                      User={User}\r\n                      Profile={Profile}\r\n                    />\r\n                  </Box>\r\n                )}\r\n                {activeTab === 1 && (\r\n                  <Box\r\n                    sx={{\r\n                      \"@media (min-width: 900px) and (max-width: 1200px)\": {\r\n                        maxHeight: \"550px\",\r\n                        width: \"280px\",\r\n                        overflowY: \"auto\",\r\n                        position: \"fixed\",\r\n                        right: \"10px\",\r\n                        top: \"100px\",\r\n                        zIndex: 1000,\r\n                      },\r\n                      \"@media (max-width: 899px)\": {\r\n                        display: \"none\", // Hide on smaller screens to prevent overlap\r\n                      },\r\n                    }}\r\n                  >\r\n                    <Box>\r\n                      {/* social links */}\r\n                      <AppLinksByProfile\r\n                        title=\"Social links\"\r\n                        subheader=\"Here are your social links\"\r\n                        type=\"socialLinks\"\r\n                        list={SocialLinks.map(\r\n                          ({ title, linkUrl, category, id, profileId }) => {\r\n                            let iconn;\r\n                            let color;\r\n                            switch (category) {\r\n                              case \"Twitter\":\r\n                                iconn = <XIcon />;\r\n                                color = \"#43aff1\";\r\n                                break;\r\n                              case \"GitHub\":\r\n                                iconn = <GitHubIcon />;\r\n                                color = \"#212121\";\r\n                                break;\r\n                              case \"Instagram\":\r\n                                iconn = <InstagramIcon />;\r\n                                color = \"#c32aa3\";\r\n                                break;\r\n                              case \"Facebook\":\r\n                                iconn = <FacebookIcon />;\r\n                                color = \"#5892d0\";\r\n                                break;\r\n                              case \"LinkedIn\":\r\n                                iconn = <LinkedInIcon />;\r\n                                color = \"#00b9f1\";\r\n                                break;\r\n                              case \"TikTok\":\r\n                                iconn = <TikTokIcon icon=\"fab:tiktok\" />;\r\n                                color = \"#000000\";\r\n                                break;\r\n                              case \"PhoneNumber\":\r\n                                iconn = <PhoneIcon />;\r\n                                color = \"#212121\";\r\n                                break;\r\n                              default:\r\n                                iconn = null;\r\n                                color = \"#ffffff\";\r\n                            }\r\n                            return {\r\n                              Id: id,\r\n                              Title: title,\r\n                              LinkUrl: linkUrl,\r\n                              Color: color,\r\n                              Icon: iconn,\r\n                              ProfileId: profileId,\r\n                              Category: category,\r\n                            };\r\n                          }\r\n                        )}\r\n                        onDelete={handleDeleteSocialLink}\r\n                        onEdit={handleBringEditedSocialLink}\r\n                      />\r\n                      {/* custom links */}\r\n                      <AppLinksByProfile\r\n                        title=\"Custom links\"\r\n                        subheader=\"Here are your custom links\"\r\n                        type=\"customLinks\"\r\n                        list={CustomLinks.map((link) => {\r\n                          return {\r\n                            Id: link.id,\r\n                            ProfileId: link.profileId,\r\n                            Title: link.title,\r\n                            LinkUrl: link.linkUrl,\r\n                            Icon: link.icon,\r\n                          };\r\n                        })}\r\n                        onDelete={handleDeleteCustomLink}\r\n                        onEdit={handleBringEditedCustomLink}\r\n                      />\r\n                      {/* Contact links */}\r\n                      <AppLinksByProfile\r\n                        title=\"Contact links\"\r\n                        subheader=\"Here are your contact links\"\r\n                        type=\"contactLinks\"\r\n                        list={\r\n                          profile.contacts\r\n                            ?.filter(\r\n                              (contact) =>\r\n                                contact.category === \"Phone\" ||\r\n                                contact.category === \"PhoneNumber\" ||\r\n                                contact.category === \"Gmail\" ||\r\n                                contact.category === \"Email\" ||\r\n                                contact.category === \"WhatsApp\"\r\n                            )\r\n                            .map((contact) => {\r\n                              let iconClass;\r\n                              let color;\r\n                              switch (contact.category) {\r\n                                case \"Phone\":\r\n                                case \"PhoneNumber\":\r\n                                  iconClass = \"fas fa-phone\";\r\n                                  color = \"#0d90e0\";\r\n                                  break;\r\n                                case \"Gmail\":\r\n                                case \"Email\":\r\n                                  iconClass = \"fab fa-google\";\r\n                                  color = \"#EA4335\";\r\n                                  break;\r\n                                case \"WhatsApp\":\r\n                                  iconClass = \"fab fa-whatsapp\";\r\n                                  color = \"#25D366\";\r\n                                  break;\r\n                                default:\r\n                                  iconClass = \"fas fa-phone\";\r\n                                  color = \"#0d90e0\";\r\n                              }\r\n                              return {\r\n                                Id: contact.id,\r\n                                Title: contact.title || contact.category,\r\n                                LinkUrl: contact.contactInfo,\r\n                                Color: color,\r\n                                Icon: iconClass,\r\n                                Category: contact.category,\r\n                              };\r\n                            }) || []\r\n                        }\r\n                        onDelete={handleDeleteContact}\r\n                        onEdit={handleEditContact}\r\n                      />\r\n                    </Box>\r\n                  </Box>\r\n                )}\r\n              </Grid>\r\n            )}\r\n\r\n            {isMobile && !isProfileCardVisible && (\r\n              <Button\r\n                disableRipple\r\n                color=\"primary\"\r\n                onClick={() => setIsProfileCardVisible((prev) => !prev)}\r\n                variant=\"contained\"\r\n                sx={{\r\n                  margin: \"10px 21px 20px \",\r\n                  zIndex: 1000,\r\n                  position: \"fixed\",\r\n                  right: \"1rem\",\r\n                  bottom: \"1rem\",\r\n                  borderRadius: \"50%\",\r\n                  height: \"55px\",\r\n                  minWidth: \"5px\",\r\n                }}\r\n              >\r\n                <PhoneIphoneIcon />\r\n              </Button>\r\n            )}\r\n\r\n            <Dialog open={isProfileCardVisible} fullScreen>\r\n              <DialogContent>\r\n                {/* X button to close the dialog */}\r\n                <IconButton\r\n                  sx={{\r\n                    position: \"fixed\",\r\n                    top: 16,\r\n                    right: 16,\r\n                    zIndex: 9999,\r\n                    backgroundColor: \"rgba(255, 255, 255, 0.9)\",\r\n                    \"&:hover\": {\r\n                      backgroundColor: \"rgba(255, 255, 255, 1)\",\r\n                    },\r\n                  }}\r\n                  onClick={() => setIsProfileCardVisible(false)}\r\n                  aria-label=\"close\"\r\n                >\r\n                  <CloseIcon />\r\n                </IconButton>\r\n                <AppProfileCard\r\n                  title=\"Profile\"\r\n                  subheader=\"Here is your profile\"\r\n                  SocialLinks={SocialLinks}\r\n                  CustomLinks={CustomLinks}\r\n                  User={User}\r\n                  Profile={Profile}\r\n                />\r\n                <Box>\r\n                  {/* social links */}\r\n                  <AppLinksByProfile\r\n                    title=\"Social links\"\r\n                    subheader=\"Here are your social links\"\r\n                    type=\"socialLinks\"\r\n                    list={SocialLinks.map(\r\n                      ({ title, linkUrl, category, id, profileId }) => {\r\n                        let iconn;\r\n                        let color;\r\n                        switch (category) {\r\n                          case \"Twitter\":\r\n                            iconn = <XIcon />;\r\n                            color = \"#43aff1\";\r\n                            break;\r\n                          case \"GitHub\":\r\n                          case \"Phone\":\r\n                            iconn = <GitHubIcon />;\r\n                            color = \"#212121\";\r\n                            break;\r\n                          case \"Instagram\":\r\n                            iconn = <InstagramIcon />;\r\n                            color = \"#c32aa3\";\r\n                            break;\r\n                          case \"Facebook\":\r\n                            iconn = <FacebookIcon />;\r\n                            color = \"#5892d0\";\r\n                            break;\r\n                          case \"LinkedIn\":\r\n                            iconn = <LinkedInIcon />;\r\n                            color = \"#00b9f1\";\r\n                            break;\r\n                          case \"TikTok\":\r\n                            iconn = <Iconify icon=\"fab:tiktok\" />;\r\n                            color = \"#000000\";\r\n                            break;\r\n                          default:\r\n                            iconn = null;\r\n                            color = \"#ffffff\";\r\n                        }\r\n                        return {\r\n                          Id: id,\r\n                          Title: title,\r\n                          LinkUrl: linkUrl,\r\n                          Color: color,\r\n                          Icon: iconn,\r\n                          ProfileId: profileId,\r\n                          Category: category,\r\n                        };\r\n                      }\r\n                    )}\r\n                    onDelete={handleDeleteSocialLink}\r\n                    onEdit={handleBringEditedSocialLink}\r\n                    ProfileCardVisible={setIsProfileCardVisible}\r\n                  />\r\n                  {/* custom links */}\r\n                  <AppLinksByProfile\r\n                    title=\"Custom links\"\r\n                    subheader=\"Here are your custom links\"\r\n                    type=\"customLinks\"\r\n                    list={CustomLinks.map((link) => {\r\n                      return {\r\n                        Id: link.id,\r\n                        ProfileId: link.profileId,\r\n                        Title: link.title,\r\n                        LinkUrl: link.linkUrl,\r\n                        Icon: link.icon,\r\n                      };\r\n                    })}\r\n                    onDelete={handleDeleteCustomLink}\r\n                    onEdit={handleBringEditedCustomLink}\r\n                    ProfileCardVisible={setIsProfileCardVisible}\r\n                  />\r\n                  {/* Contact links */}\r\n                  <AppLinksByProfile\r\n                    title=\"Contact links\"\r\n                    subheader=\"Here are your contact links\"\r\n                    type=\"contactLinks\"\r\n                    list={\r\n                      profile.contacts\r\n                        ?.filter(\r\n                          (contact) =>\r\n                            contact.category === \"Phone\" ||\r\n                            contact.category === \"PhoneNumber\" ||\r\n                            contact.category === \"Gmail\" ||\r\n                            contact.category === \"Email\" ||\r\n                            contact.category === \"WhatsApp\"\r\n                        )\r\n                        .map((contact) => {\r\n                          let iconClass;\r\n                          let color;\r\n                          switch (contact.category) {\r\n                            case \"Phone\":\r\n                            case \"PhoneNumber\":\r\n                              iconClass = \"fas fa-phone\";\r\n                              color = \"#0d90e0\";\r\n                              break;\r\n                            case \"Gmail\":\r\n                            case \"Email\":\r\n                              iconClass = \"fab fa-google\";\r\n                              color = \"#EA4335\";\r\n                              break;\r\n                            case \"WhatsApp\":\r\n                              iconClass = \"fab fa-whatsapp\";\r\n                              color = \"#25D366\";\r\n                              break;\r\n                            default:\r\n                              iconClass = \"fas fa-phone\";\r\n                              color = \"#0d90e0\";\r\n                          }\r\n                          return {\r\n                            Id: contact.id,\r\n                            Title: contact.title || contact.category,\r\n                            LinkUrl: contact.contactInfo,\r\n                            Color: color,\r\n                            Icon: iconClass,\r\n                            Category: contact.category,\r\n                            ContactInfo: contact.contactInfo,\r\n                          };\r\n                        }) || []\r\n                    }\r\n                    onDelete={handleDeleteContact}\r\n                    onEdit={handleEditContact}\r\n                    ProfileCardVisible={setIsProfileCardVisible}\r\n                  />\r\n                </Box>\r\n              </DialogContent>\r\n            </Dialog>\r\n          </Grid>\r\n\r\n          {/* Mobile Sidebar - Shows below main content on small screens */}\r\n          {activeTab === 1 && (\r\n            <Box\r\n              sx={{\r\n                \"@media (max-width: 899px)\": {\r\n                  display: \"block\",\r\n                  mt: 3,\r\n                  mb: 2,\r\n                },\r\n                \"@media (min-width: 900px)\": {\r\n                  display: \"none\",\r\n                },\r\n              }}\r\n            >\r\n              <Grid container spacing={2}>\r\n                <Grid item xs={12}>\r\n                  <Box>\r\n                    {/* Social links for mobile */}\r\n                    <AppLinksByProfile\r\n                      title=\"Social links\"\r\n                      subheader=\"Here are your social links\"\r\n                      type=\"socialLinks\"\r\n                      list={SocialLinks.map(\r\n                        ({ title, linkUrl, category, id, profileId }) => {\r\n                          let iconn;\r\n                          let color;\r\n                          switch (category) {\r\n                            case \"Twitter\":\r\n                              iconn = <XIcon />;\r\n                              color = \"#43aff1\";\r\n                              break;\r\n                            case \"Instagram\":\r\n                              iconn = <InstagramIcon />;\r\n                              color = \"#e4405f\";\r\n                              break;\r\n                            case \"Facebook\":\r\n                              iconn = <FacebookIcon />;\r\n                              color = \"#3b5998\";\r\n                              break;\r\n                            case \"LinkedIn\":\r\n                              iconn = <LinkedInIcon />;\r\n                              color = \"#0077b5\";\r\n                              break;\r\n                            case \"TikTok\":\r\n                              iconn = <Iconify icon=\"fab:tiktok\" />;\r\n                              color = \"#000000\";\r\n                              break;\r\n                            case \"YouTube\":\r\n                              iconn = <YouTubeIcon />;\r\n                              color = \"#ff0000\";\r\n                              break;\r\n                            case \"TikTok\":\r\n                              iconn = <Iconify icon=\"fab:tiktok\" />;\r\n                              color = \"#000000\";\r\n                              break;\r\n                            case \"Snapchat\":\r\n                              iconn = <LanguageIcon />;\r\n                              color = \"#fffc00\";\r\n                              break;\r\n                            case \"Pinterest\":\r\n                              iconn = <PinterestIcon />;\r\n                              color = \"#bd081c\";\r\n                              break;\r\n                            case \"Reddit\":\r\n                              iconn = <RedditIcon />;\r\n                              color = \"#ff4500\";\r\n                              break;\r\n                            case \"Twitch\":\r\n                              iconn = <LanguageIcon />;\r\n                              color = \"#9146ff\";\r\n                              break;\r\n                            case \"Discord\":\r\n                              iconn = <LanguageIcon />;\r\n                              color = \"#7289da\";\r\n                              break;\r\n                            case \"Telegram\":\r\n                              iconn = <TelegramIcon />;\r\n                              color = \"#0088cc\";\r\n                              break;\r\n                            case \"WhatsApp\":\r\n                              iconn = <WhatsAppIcon />;\r\n                              color = \"#25d366\";\r\n                              break;\r\n                            case \"Spotify\":\r\n                              iconn = <SpotifyIcon />;\r\n                              color = \"#1db954\";\r\n                              break;\r\n                            case \"SoundCloud\":\r\n                              iconn = <SpotifyIcon />;\r\n                              color = \"#ff5500\";\r\n                              break;\r\n                            case \"Behance\":\r\n                              iconn = <LanguageIcon />;\r\n                              color = \"#1769ff\";\r\n                              break;\r\n                            case \"Dribbble\":\r\n                              iconn = <LanguageIcon />;\r\n                              color = \"#ea4c89\";\r\n                              break;\r\n                            case \"GitHub\":\r\n                              iconn = <GitHubIcon />;\r\n                              color = \"#333\";\r\n                              break;\r\n                            case \"Website\":\r\n                              iconn = <LanguageIcon />;\r\n                              color = \"#007bff\";\r\n                              break;\r\n                            default:\r\n                              iconn = <LanguageIcon />;\r\n                              color = \"#007bff\";\r\n                          }\r\n                          return {\r\n                            Id: id,\r\n                            Title: title,\r\n                            LinkUrl: linkUrl,\r\n                            Color: color,\r\n                            Icon: iconn,\r\n                            Category: category,\r\n                          };\r\n                        }\r\n                      )}\r\n                      onDelete={handleDeleteSocialLink}\r\n                      onEdit={handleBringEditedSocialLink}\r\n                      ProfileCardVisible={setIsProfileCardVisible}\r\n                    />\r\n\r\n                    {/* Contact links for mobile */}\r\n                    <AppLinksByProfile\r\n                      title=\"Contact links\"\r\n                      subheader=\"Here are your contact links\"\r\n                      type=\"contactLinks\"\r\n                      list={\r\n                        profile.contacts\r\n                          ?.filter((contact) =>\r\n                            [\r\n                              \"Gmail\",\r\n                              \"Email\",\r\n                              \"WhatsApp\",\r\n                              \"PhoneNumber\",\r\n                            ].includes(contact.category)\r\n                          )\r\n                          .map((contact) => {\r\n                            let iconClass;\r\n                            let color;\r\n                            switch (contact.category) {\r\n                              case \"Gmail\":\r\n                              case \"Email\":\r\n                                iconClass = <EmailIcon />;\r\n                                color = \"#ea4335\";\r\n                                break;\r\n                              case \"WhatsApp\":\r\n                                iconClass = <WhatsAppIcon />;\r\n                                color = \"#25d366\";\r\n                                break;\r\n                              case \"PhoneNumber\":\r\n                                iconClass = <PhoneIcon />;\r\n                                color = \"#007bff\";\r\n                                break;\r\n                              default:\r\n                                iconClass = <PhoneIcon />;\r\n                                color = \"#007bff\";\r\n                            }\r\n                            return {\r\n                              Id: contact.id,\r\n                              Title: contact.title || contact.category,\r\n                              LinkUrl: contact.contactInfo,\r\n                              Color: color,\r\n                              Icon: iconClass,\r\n                              Category: contact.category,\r\n                            };\r\n                          }) || []\r\n                      }\r\n                      onDelete={handleDeleteContact}\r\n                      onEdit={handleEditContact}\r\n                      ProfileCardVisible={setIsProfileCardVisible}\r\n                    />\r\n                  </Box>\r\n                </Grid>\r\n              </Grid>\r\n            </Box>\r\n          )}\r\n        </>\r\n      )}\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default ProfileUser;\r\n"], "mappings": "AAAA,OAASA,QAAQ,KAAQ,OAAO,CAChC,OACEC,SAAS,CACTC,IAAI,CACJC,MAAM,CACNC,MAAM,CACNC,MAAM,CACNC,WAAW,CACXC,aAAa,CACbC,aAAa,CACbC,GAAG,CACHC,IAAI,CACJC,IAAI,CACJC,WAAW,CACXC,UAAU,CACVC,GAAG,CACHC,UAAU,CACVC,SAAS,CACTC,gBAAgB,CAChBC,IAAI,CACJC,QAAQ,CACRC,YAAY,CACZC,YAAY,CACZC,OAAO,KACF,eAAe,CAEtB,MAAO,CAAAC,OAAO,EAAIC,cAAc,KAAQ,uBAAuB,CAC/D,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,MAAM,KAAQ,oBAAoB,CAC3C,OAASC,MAAM,KAAQ,sBAAsB,CAC7C,MAAO,CAAAC,UAAU,KAAM,4BAA4B,CACnD,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CACjD,MAAO,CAAAC,QAAQ,KAAM,0BAA0B,CAE/C,MAAO,CAAAC,aAAa,KAAM,+BAA+B,CACzD,MAAO,CAAAC,YAAY,KAAM,8BAA8B,CACvD,MAAO,CAAAC,UAAU,KAAM,4BAA4B,CACnD,MAAO,CAAAC,sBAAsB,KAAM,wCAAwC,CAC3E,MAAO,CAAAC,YAAY,KAAM,8BAA8B,CACvD,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CACjD,MAAO,CAAAC,eAAe,KAAM,iCAAiC,CAC7D,MAAO,CAAAC,KAAK,KAAM,uBAAuB,CACzC,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CACjD,MAAO,CAAAC,YAAY,KAAM,8BAA8B,CACvD,MAAO,CAAAC,WAAW,KAAM,6BAA6B,CACrD,MAAO,CAAAC,aAAa,KAAM,+BAA+B,CACzD,MAAO,CAAAC,UAAU,KAAM,4BAA4B,CACnD,MAAO,CAAAC,YAAY,KAAM,8BAA8B,CACvD,MAAO,CAAAC,WAAW,KAAM,+BAA+B,CACvD,MAAO,CAAAC,YAAY,KAAM,8BAA8B,CACvD,MAAO,CAAAC,UAAU,KAAM,0BAA0B,CACjD,MAAO,CAAAC,iBAAiB,KAAM,8CAA8C,CAC5E,OAASC,cAAc,KAAQ,4BAA4B,CAC3D,OAASC,WAAW,KAAQ,mBAAmB,CAC/C,OAASC,aAAa,KAAQ,mBAAmB,CACjD,OACEC,cAAc,CACdC,cAAc,CACdC,cAAc,CACdC,gBAAgB,CAChBC,gBAAgB,CAChBC,gBAAgB,CAChBC,cAAc,CACdC,gBAAgB,KACX,gBAAgB,CACvB,OAASC,SAAS,KAAQ,OAAO,CACjC,OAASC,KAAK,KAAQ,gBAAgB,CACtC,MAAO,uCAAuC,CAC9C,MAAO,CAAAC,aAAa,KAAM,uCAAuC,CACjE,OAASC,UAAU,KAAQ,2BAA2B,CACtD,MAAO,CAAAC,eAAe,KAAM,6CAA6C,CACzE,MAAO,CAAAC,eAAe,KAAM,6CAA6C,CACzE,MAAO,CAAAC,kBAAkB,KAAM,gDAAgD,CAC/E,OAASC,MAAM,KAAQ,eAAe,CACtC,MAAO,CAAAC,eAAe,KAAM,iCAAiC,CAC7D,MAAO,CAAAC,kBAAkB,KAAM,oCAAoC,CACnE,MAAO,CAAAC,eAAe,KAAM,iCAAiC,CAC7D,MAAO,CAAAC,aAAa,KAAM,+BAA+B,CACzD,MAAO,CAAAC,OAAO,KAAM,uBAAuB,CAC3C,MAAO,CAAAC,UAAU,KAAM,cAAc,CACrC,OAASC,OAAO,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,gCAAAC,IAAA,IAAAC,KAAA,gCAAAC,QAAA,IAAAC,SAAA,yBAExC,QAAS,CAAAC,UAAUA,CAACC,KAAK,CAAE,CACzB,mBACEN,IAAA,CAACF,OAAO,KAAKQ,KAAK,CAAAC,QAAA,cAChBP,IAAA,SAAMQ,CAAC,CAAC,+NAA+N,CAAE,CAAC,CACnO,CAAC,CAEd,CAEA;AAEA,KAAM,CAAAC,gBAAgB,CAAG1D,MAAM,CAAC2D,IAAA,MAAC,CAAEC,SAAS,CAAE,GAAGL,KAAM,CAAC,CAAAI,IAAA,oBACtDV,IAAA,CAACrD,OAAO,KAAK2D,KAAK,CAAEM,KAAK,MAACC,OAAO,CAAE,CAAEC,MAAM,CAAEH,SAAU,CAAE,CAAE,CAAC,EAC7D,CAAC,CAACI,KAAA,MAAC,CAAEC,KAAM,CAAC,CAAAD,KAAA,OAAM,CACjB,CAAC,MAAMnE,cAAc,CAACgE,KAAK,EAAE,EAAG,CAC9BK,KAAK,CAAE,SACT,CAAC,CACD,CAAC,MAAMrE,cAAc,CAACsE,OAAO,EAAE,EAAG,CAChCC,eAAe,CAAE,SACnB,CACF,CAAC,EAAC,CAAC,CAEH,KAAM,CAAAC,WAAW,CAAGA,CAAA,GAAM,KAAAC,iBAAA,CAAAC,kBAAA,CAAAC,kBAAA,CACxB,KAAM,CAAEC,OAAO,CAAEC,YAAa,CAAC,CAAGtC,UAAU,CAAC,CAAC,CAC9C,KAAM,CAACuC,SAAS,CAAEC,YAAY,CAAC,CAAGvG,QAAQ,CAAC,CAAC,CAAC,CAC7C,KAAM,CAACwG,QAAQ,CAAEC,WAAW,CAAC,CAAGzG,QAAQ,CAAC,KAAK,CAAC,CAC/C,KAAM,CAAC0G,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG3G,QAAQ,CAAC,KAAK,CAAC,CACvE,KAAM,CAAC4G,SAAS,CAAEC,YAAY,CAAC,CAAG7G,QAAQ,CAAC,IAAI,CAAC,CAChD,KAAM,CAAC8G,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG/G,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAACgH,eAAe,CAAEC,kBAAkB,CAAC,CAAGjH,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAACkH,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGnH,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAACoH,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGrH,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAAAsH,QAAQ,CAAG7F,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAA8F,eAAe,CAAGA,CAACC,KAAK,CAAEC,QAAQ,GAAK,CAC3ClB,YAAY,CAACkB,QAAQ,CAAC,CACxB,CAAC,CAED;AACA,KAAM,CAAAC,aAAa,CAAIC,QAAQ,EAAK,CAClC,OAAQA,QAAQ,EACd,IAAK,MAAM,CACT,mBAAO/C,IAAA,CAACR,eAAe,EAACwD,QAAQ,CAAC,OAAO,CAACC,EAAE,CAAE,CAAEhC,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CACvE,IAAK,SAAS,CACZ,mBACEjB,IAAA,CAACP,kBAAkB,EAACuD,QAAQ,CAAC,OAAO,CAACC,EAAE,CAAE,CAAEhC,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CAErE,IAAK,WAAW,CACd,mBAAOjB,IAAA,CAACN,eAAe,EAACsD,QAAQ,CAAC,OAAO,CAACC,EAAE,CAAE,CAAEhC,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CACvE,IAAK,YAAY,CACf,mBAAOjB,IAAA,CAACL,aAAa,EAACqD,QAAQ,CAAC,OAAO,CAACC,EAAE,CAAE,CAAEhC,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CACrE,QACE,mBAAOjB,IAAA,CAACR,eAAe,EAACwD,QAAQ,CAAC,OAAO,CAACC,EAAE,CAAE,CAAEhC,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CACzE,CACF,CAAC,CAED,KAAM,CAACiC,IAAI,CAAEC,OAAO,CAAC,CAAG/H,QAAQ,CAAC,CAC/BgI,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,EAAE,CAETC,SAAS,CAAE,EAAE,CACbC,QAAQ,CAAE,EAAE,CACZR,QAAQ,CAAE,EAAE,CACZS,MAAM,CAAE,GACV,CAAC,CAAC,CAEF,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGtI,QAAQ,CAAC,CACrCgI,EAAE,CAAE,CAAC,CACLO,MAAM,CAAE,CAAC,CACTC,QAAQ,CAAE,EAAE,CACZC,SAAS,CAAE,EAAE,CACbC,MAAM,CAAE,EAAE,CACVC,cAAc,CAAE,EAAE,CAClBC,mBAAmB,CAAE,EAAE,CACvBC,mBAAmB,CAAE,CAAC,CACtBC,UAAU,CAAE,EAAE,CACdC,SAAS,CAAE,KAAK,CAChBC,IAAI,CAAE,IAAI,CACVC,WAAW,CAAE,IAAI,CACjBC,WAAW,CAAE,IAAI,CACjBC,OAAO,CAAE,IAAI,CACbC,QAAQ,CAAE,IAAI,CACdC,OAAO,CAAE,IACX,CAAC,CAAC,CAEF,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAGvJ,QAAQ,CAAC,IAAI,CAAC,CAChD,KAAM,CAACwJ,WAAW,CAAEC,cAAc,CAAC,CAAGzJ,QAAQ,CAAC,EAAE,CAAC,CAElD,KAAM,CAAC0J,WAAW,CAAEC,cAAc,CAAC,CAAG3J,QAAQ,CAAC,EAAE,CAAC,CAElD,KAAM,CAAC4J,aAAa,CAAEC,gBAAgB,CAAC,CAAG7J,QAAQ,CAAC,CACjD8J,SAAS,CAAE,CAAC,CACZC,OAAO,CAAE,EAAE,CACXC,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAE,EAAE,CACTC,KAAK,CAAE,EACT,CAAC,CAAC,CAEF,KAAM,CAACC,aAAa,CAAEC,gBAAgB,CAAC,CAAGpK,QAAQ,CAAC,CACjD8J,SAAS,CAAE,CAAC,CACZC,OAAO,CAAE,EAAE,CACXE,KAAK,CAAE,EAAE,CACTC,KAAK,CAAE,MAAM,CACbG,IAAI,CAAE,IACR,CAAC,CAAC,CAEF,KAAM,CAACC,wBAAwB,CAAEC,2BAA2B,CAAC,CAC3DvK,QAAQ,CAAC,KAAK,CAAC,CAEjB,KAAM,CAACwK,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGzK,QAAQ,CAAC,CACvD0K,EAAE,CAAE,CAAC,CACLZ,SAAS,CAAE,CAAC,CACZC,OAAO,CAAE,EAAE,CACXE,KAAK,CAAE,EAAE,CACTI,IAAI,CAAE,IACR,CAAC,CAAC,CAEF,KAAM,CAACM,wBAAwB,CAAEC,2BAA2B,CAAC,CAC3D5K,QAAQ,CAAC,KAAK,CAAC,CAEjB,KAAM,CAAC6K,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG9K,QAAQ,CAAC,CACvD0K,EAAE,CAAE,CAAC,CACLZ,SAAS,CAAE,CAAC,CACZG,KAAK,CAAE,EAAE,CACTF,OAAO,CAAE,EAAE,CACXC,QAAQ,CAAE,EAAE,CACZE,KAAK,CAAE,EACT,CAAC,CAAC,CAEF,KAAM,CAACa,IAAI,CAAEC,OAAO,CAAC,CAAGhL,QAAQ,CAAC,KAAK,CAAC,CACvC,KAAM,CAACiL,cAAc,CAAEC,iBAAiB,CAAC,CAAGlL,QAAQ,CAAC,KAAK,CAAC,CAE3D,KAAM,CAAAmL,eAAe,CAAGA,CAACC,KAAK,CAAEC,IAAI,CAAExF,KAAK,GAAK,CAC9CmF,OAAO,CAAC,IAAI,CAAC,CACbnB,gBAAgB,CAAEyB,iBAAiB,GAAM,CACvC,GAAGA,iBAAiB,CACpBtB,QAAQ,CAAEqB,IAAI,CACdnB,KAAK,CAAErE,KACT,CAAC,CAAC,CAAC,CACL,CAAC,CAED,KAAM,CAAAoD,WAAW,CAAG,CAClB,CACEsC,QAAQ,CAAE,SAAS,CACnBC,IAAI,CAAE,GAAG,CACTC,KAAK,CAAE,4CAA4C,CACnDC,WAAW,CAAE,cAAc,CAC3B7F,KAAK,CAAE,6CACT,CAAC,CACD,CACE0F,QAAQ,CAAE,QAAQ,CAClBC,IAAI,CAAE,QAAQ,CACdC,KAAK,CAAE,2CAA2C,CAClDC,WAAW,CAAE,aAAa,CAC1B7F,KAAK,CACH,0EACJ,CAAC,CACD,CACE0F,QAAQ,CAAE,WAAW,CACrBC,IAAI,CAAE,WAAW,CACjBC,KAAK,CAAE,0CAA0C,CACjDC,WAAW,CAAE,YAAY,CACzB7F,KAAK,CAAE,0CACT,CAAC,CACD,CACE0F,QAAQ,CAAE,UAAU,CACpBC,IAAI,CAAE,UAAU,CAChBC,KAAK,CAAE,6CAA6C,CACpDC,WAAW,CAAE,eAAe,CAC5B7F,KAAK,CAAE,2CACT,CAAC,CACD,CACE0F,QAAQ,CAAE,QAAQ,CAClBC,IAAI,CAAE,QAAQ,CACdC,KAAK,CAAE,2CAA2C,CAClDC,WAAW,CAAE,aAAa,CAC1B7F,KAAK,CAAE,2CACT,CAAC,CACD,CACE0F,QAAQ,CAAE,UAAU,CACpBC,IAAI,CAAE,UAAU,CAChBC,KAAK,CAAE,6CAA6C,CACpDC,WAAW,CAAE,eAAe,CAC5B7F,KAAK,CAAE,2CACT,CAAC,CACF,CAED,KAAM,CAAA8F,UAAU,CAAG,CACjB,CACEJ,QAAQ,CAAE,OAAO,CACjBC,IAAI,CAAE,OAAO,CACbC,KAAK,CAAE,oCAAoC,CAC3CC,WAAW,CAAE,YAAY,CACzB7F,KAAK,CAAE,6CACT,CAAC,CACD,CACE0F,QAAQ,CAAE,OAAO,CACjBC,IAAI,CAAE,OAAO,CACbC,KAAK,CAAE,qCAAqC,CAC5CC,WAAW,CAAE,YAAY,CACzB7F,KAAK,CAAE,6CACT,CAAC,CACD,CACE0F,QAAQ,CAAE,UAAU,CACpBC,IAAI,CAAE,UAAU,CAChBC,KAAK,CAAE,uCAAuC,CAC9CC,WAAW,CAAE,eAAe,CAC5B7F,KAAK,CAAE,6CACT,CAAC,CACF,CAED,KAAM,CAAA+F,gBAAgB,CAAIpE,KAAK,EAAK,CAClC,KAAM,CAAEqE,IAAI,CAAEC,KAAM,CAAC,CAAGtE,KAAK,CAACuE,MAAM,CAEpC,KAAM,CAAAC,oBAAoB,CAAGF,KAAK,CAACG,IAAI,CAAC,CAAC,GAAK,EAAE,CAEhD,KAAM,CAAAC,YAAY,CAAG,eAAe,CAACC,IAAI,CAACL,KAAK,CAAC,CAEhD,GAAID,IAAI,GAAK,WAAW,EAAIA,IAAI,GAAK,UAAU,CAAE,CAC/C,GAAIK,YAAY,EAAIF,oBAAoB,CAAE,CACxCjE,OAAO,CAAEqE,QAAQ,GAAM,CACrB,GAAGA,QAAQ,CACX,CAACP,IAAI,EAAGC,KACV,CAAC,CAAC,CAAC,CACH/E,qBAAqB,CAAC,IAAI,CAAC,CAC7B,CACF,CACF,CAAC,CAED,KAAM,CAAAsF,mBAAmB,CAAI7E,KAAK,EAAK,CACrC,KAAM,CAAEqE,IAAI,CAAEC,KAAM,CAAC,CAAGtE,KAAK,CAACuE,MAAM,CAEpCzD,UAAU,CAAEgE,WAAW,GAAM,CAC3B,GAAGA,WAAW,CACd,CAACT,IAAI,EAAGC,KACV,CAAC,CAAC,CAAC,CACH/E,qBAAqB,CAAC,IAAI,CAAC,CAC7B,CAAC,CAED,KAAM,CAAAwF,kBAAkB,CAAIT,KAAK,EAAK,CACpCxD,UAAU,CAAEgE,WAAW,GAAM,CAC3B,GAAGA,WAAW,CACdlD,QAAQ,CAAE0C,KACZ,CAAC,CAAC,CAAC,CACH/E,qBAAqB,CAAC,IAAI,CAAC,CAC7B,CAAC,CAED,KAAM,CAAAyF,yBAAyB,CAAIhF,KAAK,EAAK,CAC3C,KAAM,CAAEqE,IAAI,CAAEC,KAAM,CAAC,CAAGtE,KAAK,CAACuE,MAAM,CACpClC,gBAAgB,CAAEyB,iBAAiB,GAAM,CACvC,GAAGA,iBAAiB,CACpB,CAACO,IAAI,EAAGC,KACV,CAAC,CAAC,CAAC,CACL,CAAC,CAED,KAAM,CAAAW,yBAAyB,CAAIjF,KAAK,EAAK,CAC3C,KAAM,CAAEqE,IAAI,CAAEC,KAAM,CAAC,CAAGtE,KAAK,CAACuE,MAAM,CACpC3B,gBAAgB,CAAEsC,iBAAiB,GAAM,CACvC,GAAGA,iBAAiB,CACpB,CAACb,IAAI,EAAGC,KACV,CAAC,CAAC,CAAC,CAEHa,mBAAmB,CAAC,CAClB,GAAGC,gBAAgB,CACnB,CAACf,IAAI,EAAGC,KAAK,GAAK,EACpB,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAe,iBAAiB,CAAIC,YAAY,EAAK,CAC1CxE,UAAU,CAAEyE,QAAQ,GAAM,CACxB,GAAGA,QAAQ,CACXpE,cAAc,CAAEmE,YAClB,CAAC,CAAC,CAAC,CACH/F,qBAAqB,CAAC,IAAI,CAAC,CAC7B,CAAC,CAED,KAAM,CAAAiG,sBAAsB,CAAIF,YAAY,EAAK,CAC/CxE,UAAU,CAAEyE,QAAQ,GAAM,CACxB,GAAGA,QAAQ,CACXnE,mBAAmB,CAAEkE,YACvB,CAAC,CAAC,CAAC,CACH/F,qBAAqB,CAAC,IAAI,CAAC,CAC7B,CAAC,CAED,KAAM,CAAAkG,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7BhG,kBAAkB,CAAC,IAAI,CAAC,CACxB,GAAI,CACF,KAAM,CAAAiG,QAAQ,CAAG,KAAM,CAAAhK,WAAW,CAAC4E,IAAI,CAAEO,OAAO,CAAC,CAEjD,GAAI6E,QAAQ,EAAIA,QAAQ,CAACC,MAAM,GAAK,GAAG,CAAE,CACvC,KAAM,CAAA9G,YAAY,CAAC,CAAC,CACpBxC,KAAK,CAACuJ,OAAO,CAAC,4BAA4B,CAAE,CAC1CC,QAAQ,CAAE,YAAY,CACtBC,SAAS,CAAE,IACb,CAAC,CAAC,CACFvG,qBAAqB,CAAC,KAAK,CAAC,CAC9B,CAAC,IAAM,CACL,KAAM,IAAI,CAAAwG,KAAK,CAAC,wBAAwB,CAAC,CAC3C,CACF,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7C3J,KAAK,CAAC2J,KAAK,CAAC,yBAAyBA,KAAK,CAACE,OAAO,EAAI,eAAe,EAAE,CAAE,CACvEL,QAAQ,CAAE,YAAY,CACtBC,SAAS,CAAE,IACb,CAAC,CAAC,CACJ,CAAC,OAAS,CACRrG,kBAAkB,CAAC,KAAK,CAAC,CAC3B,CACF,CAAC,CAED;AACA,KAAM,CAAA0G,2BAA2B,CAAG,KAAO,CAAAC,IAAI,EAAK,CAClDnD,mBAAmB,CAACmD,IAAI,CAAC,CACzBrD,2BAA2B,CAAC,IAAI,CAAC,CACjC;AACA,GAAI/D,QAAQ,CAAE,CACZD,YAAY,CAAC,CAAC,CAAC,CACjB,CACF,CAAC,CAED,KAAM,CAAAsH,mBAAmB,CAAG,KAAO,CAAAC,SAAS,EAAK,CAC/C,GAAI,CAACC,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,CAAE,CACpE,OACF,CAEAP,OAAO,CAACQ,GAAG,CAAC,uCAAuC,CAAEH,SAAS,CAAC,CAC/D,GAAI,CACF,KAAM,CAAAZ,QAAQ,CAAG,KAAM,CAAA/J,aAAa,CAAC2K,SAAS,CAAC,CAC/CL,OAAO,CAACQ,GAAG,CAAC,kBAAkB,CAAEf,QAAQ,CAAC,CACzC,GAAIA,QAAQ,CAAE,CACZrJ,KAAK,CAACuJ,OAAO,CAAC,8BAA8B,CAAE,CAC5CC,QAAQ,CAAE,YAAY,CACtBC,SAAS,CAAE,IACb,CAAC,CAAC,CACFjH,YAAY,CAAC,CAAC,CAChB,CAAC,IAAM,CACLxC,KAAK,CAAC2J,KAAK,CAAC,yBAAyB,CAAE,CACrCH,QAAQ,CAAE,YAAY,CACtBC,SAAS,CAAE,IACb,CAAC,CAAC,CACJ,CACF,CAAE,MAAOE,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/C3J,KAAK,CAAC2J,KAAK,CAAC,2BAA2BA,KAAK,CAACE,OAAO,EAAIF,KAAK,EAAE,CAAE,CAC/DH,QAAQ,CAAE,YAAY,CACtBC,SAAS,CAAE,IACb,CAAC,CAAC,CACJ,CACF,CAAC,CAED,KAAM,CAAAY,iBAAiB,CAAIC,OAAO,EAAK,CACrC;AACAC,iBAAiB,CAACD,OAAO,CAAC,CAE1B;AACA,GAAIA,OAAO,CAACnE,QAAQ,GAAK,OAAO,EAAImE,OAAO,CAACnE,QAAQ,GAAK,aAAa,CAAE,CACtEqE,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CAAC,IAAM,IAAIF,OAAO,CAACnE,QAAQ,GAAK,OAAO,EAAImE,OAAO,CAACnE,QAAQ,GAAK,OAAO,CAAE,CACvEsE,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CAAC,IAAM,IAAIH,OAAO,CAACnE,QAAQ,GAAK,UAAU,CAAE,CAC1CuE,qBAAqB,CAAC,IAAI,CAAC,CAC7B,CAEA;AACA,GAAI/H,QAAQ,CAAE,CACZD,YAAY,CAAC,CAAC,CAAC,CACjB,CACF,CAAC,CAED,KAAM,CAAAiI,4BAA4B,CAAG,KAAO,CAAAhH,KAAK,EAAK,CACpD,KAAM,CAAEqE,IAAI,CAAEC,KAAM,CAAC,CAAGtE,KAAK,CAACuE,MAAM,CACpCtB,mBAAmB,CAAEgE,QAAQ,GAAM,CACjC,GAAGA,QAAQ,CACX,CAAC5C,IAAI,EAAGC,KACV,CAAC,CAAC,CAAC,CACL,CAAC,CAED,KAAM,CAAA4C,+BAA+B,CAAI5B,YAAY,EAAK,CACxDrC,mBAAmB,CAAEgE,QAAQ,GAAM,CACjC,GAAGA,QAAQ,CACXpE,IAAI,CAAEyC,YACR,CAAC,CAAC,CAAC,CACHH,mBAAmB,CAAC,CAClB,GAAGC,gBAAgB,CACnB+B,KAAK,CAAE7B,YAAY,GAAK,IAC1B,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAA8B,oBAAoB,CAAG,KAAAA,CAAA,GAAY,CACvCzH,qBAAqB,CAAC,IAAI,CAAC,CAC3B,GAAI,CACF,GAAI,CAAC0H,gBAAgB,CAACrE,gBAAgB,CAACT,OAAO,CAAC,CAAE,CAC/ClG,KAAK,CAAC2J,KAAK,CAAC,oBAAoB,CAAE,CAChCH,QAAQ,CAAE,YAAY,CACtBC,SAAS,CAAE,IACb,CAAC,CAAC,CACF,OACF,CAEA,GAAI9C,gBAAgB,CAACP,KAAK,GAAK,EAAE,CAAE,CACjCpG,KAAK,CAAC2J,KAAK,CAAC,sBAAsB,CAAE,CAClCH,QAAQ,CAAE,YAAY,CACtBC,SAAS,CAAE,IACb,CAAC,CAAC,CACF,OACF,CAEA,KAAM,CAAAjK,cAAc,CAACmH,gBAAgB,CAAC,CAEtCC,mBAAmB,CAAC,CAClBC,EAAE,CAAE,CAAC,CACLZ,SAAS,CAAE,CAAC,CACZC,OAAO,CAAE,EAAE,CACXE,KAAK,CAAE,EACT,CAAC,CAAC,CAEFM,2BAA2B,CAAC,KAAK,CAAC,CAElC1G,KAAK,CAACuJ,OAAO,CAAC,aAAa,CAAE,CAC3BC,QAAQ,CAAE,YAAY,CACtBC,SAAS,CAAE,IACb,CAAC,CAAC,CAEFwB,oBAAoB,CAAC,CAAC,CACxB,CAAE,MAAOtB,KAAK,CAAE,CACd3J,KAAK,CAAC2J,KAAK,CAACA,KAAK,CAAE,CACjBH,QAAQ,CAAE,YAAY,CACtBC,SAAS,CAAE,IACb,CAAC,CAAC,CACFG,OAAO,CAACD,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAACE,OAAO,CAAC,CAChE,CAAC,OAAS,CACRvG,qBAAqB,CAAC,KAAK,CAAC,CAC9B,CACF,CAAC,CAED;AACA,KAAM,CAAA4H,2BAA2B,CAAG,KAAO,CAAAnB,IAAI,EAAK,CAClD9C,mBAAmB,CAAC8C,IAAI,CAAC,CACzBhD,2BAA2B,CAAC,IAAI,CAAC,CACjC;AACA,GAAIpE,QAAQ,CAAE,CACZD,YAAY,CAAC,CAAC,CAAC,CACjB,CACF,CAAC,CAED,KAAM,CAAAyI,4BAA4B,CAAG,KAAO,CAAAxH,KAAK,EAAK,CACpD,KAAM,CAAEqE,IAAI,CAAEC,KAAM,CAAC,CAAGtE,KAAK,CAACuE,MAAM,CACpCjB,mBAAmB,CAAE2D,QAAQ,GAAM,CACjC,GAAGA,QAAQ,CACX,CAAC5C,IAAI,EAAGC,KACV,CAAC,CAAC,CAAC,CACL,CAAC,CAED,KAAM,CAAAmD,oBAAoB,CAAG,KAAAA,CAAA,GAAY,CACvC5H,qBAAqB,CAAC,IAAI,CAAC,CAC3B,GAAI,CACF,GAAI,CAAC6H,UAAU,CAACrE,gBAAgB,CAACd,OAAO,CAAC,CAAE,CACzClG,KAAK,CAAC2J,KAAK,CAAC,oBAAoB,CAAE,CAChCH,QAAQ,CAAE,YAAY,CACtBC,SAAS,CAAE,IACb,CAAC,CAAC,CACF,OACF,CAEA,GAAIzC,gBAAgB,CAACZ,KAAK,GAAK,EAAE,CAAE,CACjCpG,KAAK,CAAC2J,KAAK,CAAC,sBAAsB,CAAE,CAClCH,QAAQ,CAAE,YAAY,CACtBC,SAAS,CAAE,IACb,CAAC,CAAC,CACF,OACF,CAEA,KAAM,CAAA6B,KAAK,CAAG,uCAAuC,CACrD,KAAM,CAAAC,OAAO,CAAGvE,gBAAgB,CAACd,OAAO,CAACsF,KAAK,CAACF,KAAK,CAAC,CACrD,KAAM,CAAAG,MAAM,CAAGF,OAAO,CAAGA,OAAO,CAAC,CAAC,CAAC,CAAG,IAAI,CAE1C,GAAIE,MAAM,GAAKzE,gBAAgB,CAACb,QAAQ,CAACuF,WAAW,CAAC,CAAC,CAAE,CACtD1L,KAAK,CAAC2J,KAAK,CAAC,0CAA0C,CAAE,CACtDH,QAAQ,CAAE,YAAY,CACtBC,SAAS,CAAE,IACb,CAAC,CAAC,CACF,OACF,CAEA,KAAM,CAAAhK,cAAc,CAACuH,gBAAgB,CAAC,CAEtCC,mBAAmB,CAAC,CAClBJ,EAAE,CAAE,CAAC,CACLZ,SAAS,CAAE,CAAC,CACZG,KAAK,CAAE,EAAE,CACTF,OAAO,CAAE,EAAE,CACXC,QAAQ,CAAE,EAAE,CACZE,KAAK,CAAE,EACT,CAAC,CAAC,CAEFU,2BAA2B,CAAC,KAAK,CAAC,CAClCM,iBAAiB,CAAC,KAAK,CAAC,CAExBrH,KAAK,CAACuJ,OAAO,CAAC,aAAa,CAAE,CAC3BC,QAAQ,CAAE,YAAY,CACtBC,SAAS,CAAE,IACb,CAAC,CAAC,CAEFkC,oBAAoB,CAAC,CAAC,CACxB,CAAE,MAAOhC,KAAK,CAAE,CACd3J,KAAK,CAAC2J,KAAK,CAACA,KAAK,CAAE,CACjBH,QAAQ,CAAE,YAAY,CACtBC,SAAS,CAAE,IACb,CAAC,CAAC,CACFG,OAAO,CAACD,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAACE,OAAO,CAAC,CAChE,CAAC,OAAS,CACRrG,qBAAqB,CAAC,KAAK,CAAC,CAC9B,CACF,CAAC,CAED,KAAM,CAAAoI,sBAAsB,CAAG,KAAO,CAAA/E,EAAE,EAAK,CAC3C;AACA,GAAI,CAACqD,MAAM,CAACC,OAAO,CAAC,mDAAmD,CAAC,CAAE,CACxE,OACF,CAEA,GAAI,CACF,KAAM,CAAAd,QAAQ,CAAG,KAAM,CAAAvJ,gBAAgB,CAAC+G,EAAE,CAAC,CAC3C,GAAIwC,QAAQ,EAAI,IAAI,CAAE,CACpBrJ,KAAK,CAACuJ,OAAO,CAAC,kCAAkC,CAAE,CAChDC,QAAQ,CAAE,YAAY,CACtBC,SAAS,CAAE,IACb,CAAC,CAAC,CACFwB,oBAAoB,CAAC,CAAC,CACxB,CACF,CAAE,MAAOtB,KAAK,CAAE,CACd3J,KAAK,CAAC2J,KAAK,CAAC,8BAA8B,CAAE,CAC1CH,QAAQ,CAAE,YAAY,CACtBC,SAAS,CAAE,IACb,CAAC,CAAC,CACFG,OAAO,CAACD,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACrD,CACF,CAAC,CAED,KAAM,CAAAkC,sBAAsB,CAAG,KAAO,CAAAhF,EAAE,EAAK,CAC3C;AACA,GAAI,CAACqD,MAAM,CAACC,OAAO,CAAC,mDAAmD,CAAC,CAAE,CACxE,OACF,CAEA,GAAI,CACF,KAAM,CAAAd,QAAQ,CAAG,KAAM,CAAAzJ,gBAAgB,CAACiH,EAAE,CAAC,CAC3C,GAAIwC,QAAQ,EAAI,IAAI,CAAE,CACpBrJ,KAAK,CAACuJ,OAAO,CAAC,kCAAkC,CAAE,CAChDC,QAAQ,CAAE,YAAY,CACtBC,SAAS,CAAE,IACb,CAAC,CAAC,CACFkC,oBAAoB,CAAC,CAAC,CACxB,CACF,CAAE,MAAOhC,KAAK,CAAE,CACd3J,KAAK,CAAC2J,KAAK,CAAC,8BAA8B,CAAE,CAC1CH,QAAQ,CAAE,YAAY,CACtBC,SAAS,CAAE,IACb,CAAC,CAAC,CACFG,OAAO,CAACD,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACrD,CACF,CAAC,CAED,KAAM,CAAAmC,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CACF5H,OAAO,CAAC,CACNC,EAAE,CAAE5B,OAAO,CAAC4B,EAAE,CACdC,KAAK,CAAE7B,OAAO,CAAC6B,KAAK,CACpBC,SAAS,CAAE9B,OAAO,CAAC8B,SAAS,CAC5BC,QAAQ,CAAE/B,OAAO,CAAC+B,QAAQ,CAC1BR,QAAQ,CAAEvB,OAAO,CAACuB,QACpB,CAAC,CAAC,CACFW,UAAU,CAAClC,OAAO,CAACA,OAAO,CAAC,CAE3ByD,gBAAgB,CAAEyB,iBAAiB,GAAM,CACvC,GAAGA,iBAAiB,CACpBxB,SAAS,CAAE1D,OAAO,CAACA,OAAO,CAAC4B,EAC7B,CAAC,CAAC,CAAC,CAEHoC,gBAAgB,CAAEsC,iBAAiB,GAAM,CACvC,GAAGA,iBAAiB,CACpB5C,SAAS,CAAE1D,OAAO,CAACA,OAAO,CAAC4B,EAC7B,CAAC,CAAC,CAAC,CACL,CAAE,MAAOwF,KAAK,CAAE,CACd,GAAIA,KAAK,CAACoC,eAAe,CAAE,CACzBtI,QAAQ,CAAC,QAAQ,CAAC,CACpB,CACF,CACF,CAAC,CAED;AACA1D,SAAS,CAAC,IAAM,CACd,GAAIwC,OAAO,EAAIA,OAAO,CAACA,OAAO,CAAE,CAC9BuJ,aAAa,CAAC,CAAC,CACjB,CACF,CAAC,CAAE,CAACvJ,OAAO,CAAC,CAAC,CAEbxC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAiM,YAAY,CAAGA,CAAA,GAAM,CACzBpJ,WAAW,CAACsH,MAAM,CAAC+B,UAAU,EAAI,GAAG,CAAC,CACvC,CAAC,CAED/B,MAAM,CAACgC,gBAAgB,CAAC,QAAQ,CAAEF,YAAY,CAAC,CAE/C;AACAA,YAAY,CAAC,CAAC,CAEd;AACA,KAAM,CAAAG,SAAS,CAAG,KAAAA,CAAA,GAAY,CAC5B,GAAI5J,OAAO,EAAIA,OAAO,CAACA,OAAO,CAAE,CAC9BmD,YAAY,CAAC,IAAI,CAAC,CAClB,GAAI,CACF,KAAM,CAAA0G,OAAO,CAACC,GAAG,CAAC,CAChBP,aAAa,CAAC,CAAC,CACfH,oBAAoB,CAAC,CAAC,CACtBV,oBAAoB,CAAC,CAAC,CACvB,CAAC,CACJ,CAAE,MAAOtB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACtD,CAAC,OAAS,CACRjE,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CACF,CAAC,CAEDyG,SAAS,CAAC,CAAC,CAEX,MAAO,IAAM,CACXjC,MAAM,CAACoC,mBAAmB,CAAC,QAAQ,CAAEN,YAAY,CAAC,CACpD,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAL,oBAAoB,CAAG,KAAAA,CAAA,GAAY,CACvC,GAAI,CACF,KAAM,CAAAtC,QAAQ,CAAG,KAAM,CAAA9J,cAAc,CAAC,CAAC,CACvCqG,cAAc,CAACyD,QAAQ,CAACkD,IAAI,CAAC,CAC/B,CAAE,MAAO5C,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,CAAEA,KAAK,CAAC,CAC3D,CACF,CAAC,CAED,KAAM,CAAAsB,oBAAoB,CAAG,KAAAA,CAAA,GAAY,CACvC,GAAI,CACF,KAAM,CAAA5B,QAAQ,CAAG,KAAM,CAAAxJ,cAAc,CAAC,CAAC,CACvCiG,cAAc,CAACuD,QAAQ,CAACkD,IAAI,CAAC,CAC/B,CAAE,MAAO5C,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,CAAEA,KAAK,CAAC,CAC3D,CACF,CAAC,CAED,KAAM,CAAA6C,WAAW,CAAGA,CAAA,GAAM,CACxBrF,OAAO,CAAC,KAAK,CAAC,CACdnB,gBAAgB,CAAEyB,iBAAiB,GAAM,CACvC,GAAGA,iBAAiB,CACpBvB,OAAO,CAAE,EAAE,CACXC,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAE,EAAE,CACTC,KAAK,CAAE,EACT,CAAC,CAAC,CAAC,CACL,CAAC,CAED,KAAM,CAAAoG,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7B,GAAI,CAACpB,UAAU,CAACtF,aAAa,CAACG,OAAO,CAAC,CAAE,CACtClG,KAAK,CAAC2J,KAAK,CAAC,oBAAoB,CAAE,CAChCH,QAAQ,CAAE,YAAY,CACtBC,SAAS,CAAE,IACb,CAAC,CAAC,CACF,OACF,CAEA,KAAM,CAAAJ,QAAQ,CAAG,KAAM,CAAA3J,gBAAgB,CAACqG,aAAa,CAAC,CACtD2G,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAE,MAAM,CAAC,CAClDH,WAAW,CAAC,CAAC,CACb,GAAInD,QAAQ,CAAE,CACZsC,oBAAoB,CAAC,CAAC,CACtB3L,KAAK,CAACuJ,OAAO,CAAC,qBAAqB,CAAE,CACnCC,QAAQ,CAAE,YAAY,CACtBC,SAAS,CAAE,IACb,CAAC,CAAC,CACJ,CAAC,IAAM,CACLzJ,KAAK,CAAC2J,KAAK,CAAC,kCAAkC,CAAE,CAC9CH,QAAQ,CAAE,YAAY,CACtBC,SAAS,CAAE,IACb,CAAC,CAAC,CACJ,CACF,CAAC,CAED,KAAM,CAAAmD,oBAAoB,CAAG,KAAAA,CAAA,GAAY,CACvC,GAAI,CAAC5B,gBAAgB,CAAC1E,aAAa,CAACJ,OAAO,CAAC,CAAE,CAC5ClG,KAAK,CAAC2J,KAAK,CAAC,oBAAoB,CAAE,CAChCH,QAAQ,CAAE,YAAY,CACtBC,SAAS,CAAE,IACb,CAAC,CAAC,CACF,OACF,CAEA,KAAM,CAAAJ,QAAQ,CAAG,KAAM,CAAA1J,gBAAgB,CAAC2G,aAAa,CAAC,CAEtD,GAAI+C,QAAQ,CAAE,CACZ4B,oBAAoB,CAAC,CAAC,CACtBjL,KAAK,CAACuJ,OAAO,CAAC,qBAAqB,CAAE,CACnCC,QAAQ,CAAE,YAAY,CACtBC,SAAS,CAAE,IACb,CAAC,CAAC,CACFoD,qBAAqB,CAAC,CAAC,CACzB,CAAC,IAAM,CACL7M,KAAK,CAAC2J,KAAK,CAAC,kCAAkC,CAAE,CAC9CH,QAAQ,CAAE,YAAY,CACtBC,SAAS,CAAE,IACb,CAAC,CAAC,CACJ,CACF,CAAC,CAED,KAAM,CAAAqD,2BAA2B,CAAI7D,YAAY,EAAK,CACpD1C,gBAAgB,CAAE2C,QAAQ,GAAM,CAC9B,GAAGA,QAAQ,CACX1C,IAAI,CAAEyC,YACR,CAAC,CAAC,CAAC,CACHH,mBAAmB,CAAC,CAClB,GAAGC,gBAAgB,CACnB+B,KAAK,CAAE7B,YAAY,GAAK,IAC1B,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAC8D,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG7Q,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAAC8Q,eAAe,CAAEzC,kBAAkB,CAAC,CAAGrO,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAAC+Q,eAAe,CAAEzC,kBAAkB,CAAC,CAAGtO,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAACgR,kBAAkB,CAAEzC,qBAAqB,CAAC,CAAGvO,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAACiR,cAAc,CAAE7C,iBAAiB,CAAC,CAAGpO,QAAQ,CAAC,IAAI,CAAC,CAE1D,KAAM,CAACkR,wBAAwB,CAAEC,2BAA2B,CAAC,CAC3DnR,QAAQ,CAAC,KAAK,CAAC,CAEjB;AACA,KAAM,CAAAoR,qBAAqB,CAAGA,CAAA,GAAM,CAClCP,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CAAC,CAED,KAAM,CAAAH,qBAAqB,CAAGA,CAAA,GAAM,CAClCtG,gBAAgB,CAAE2C,QAAQ,GAAM,CAC9B,GAAGA,QAAQ,CACX9C,KAAK,CAAE,EAAE,CACTF,OAAO,CAAE,EAAE,CACXM,IAAI,CAAE,EACR,CAAC,CAAC,CAAC,CACHwG,mBAAmB,CAAC,KAAK,CAAC,CAC5B,CAAC,CAED;AACA,KAAM,CAACjE,gBAAgB,CAAED,mBAAmB,CAAC,CAAG3M,QAAQ,CAAC,CACvDoL,KAAK,CAAE,KAAK,CACZiG,OAAO,CAAE,KAAK,CACd1C,KAAK,CAAE,KACT,CAAC,CAAC,CAEF,KAAM,CAAA2C,WAAW,CACfnH,aAAa,CAACF,KAAK,CAACgC,IAAI,CAAC,CAAC,GAAK,EAAE,EAAI9B,aAAa,CAACJ,OAAO,CAACkC,IAAI,CAAC,CAAC,GAAK,EAAE,CAE1E,KAAM,CAAAiD,UAAU,CAAIqC,KAAK,EAAK,CAC5B;AACA,KAAM,CAAAC,UAAU,CACd,wEAAwE,CAE1E;AACA,KAAM,CAAAC,YAAY,CAAG,SAAS,CAE9B;AACA,KAAM,CAAAC,KAAK,CAAGF,UAAU,CAACrF,IAAI,CAACoF,KAAK,CAAC,CAEpC;AACA,KAAM,CAAAI,aAAa,CAAGF,YAAY,CAACtF,IAAI,CAACoF,KAAK,CAAC,CAE9C;AACA,KAAM,CAAAK,kBAAkB,CAAG,CACzB,cAAc,CACd,aAAa,CACb,eAAe,CACf,cAAc,CACd,eAAe,CACf,YAAY,CACZ,YAAY,CACb,CAED;AACA,KAAM,CAAAC,aAAa,CAAGD,kBAAkB,CAACE,IAAI,CAAExC,MAAM,EACnD,GAAI,CAAAyC,MAAM,CAAC,yBAAyBzC,MAAM,EAAE,CAAE,GAAG,CAAC,CAACnD,IAAI,CAACoF,KAAK,CAC/D,CAAC,CAED,KAAM,CAAAS,cAAc,CAAG,GAAI,CAAAD,MAAM,CAC/B,yBAAyBnI,aAAa,CAACI,QAAQ,EAAE,CACjD,GACF,CAAC,CAACmC,IAAI,CAACoF,KAAK,CAAC,CAEb;AACA,MAAQ,CAAAG,KAAK,EAAIG,aAAa,EAAIG,cAAc,EAAKL,aAAa,CACpE,CAAC,CAED,KAAM,CAAA9C,gBAAgB,CAAI0C,KAAK,EAAK,CAClC;AACA,KAAM,CAAAC,UAAU,CACd,wEAAwE,CAE1E;AACA,KAAM,CAAAE,KAAK,CAAGF,UAAU,CAACrF,IAAI,CAACoF,KAAK,CAAC,CAEpC,MAAO,CAAAG,KAAK,CACd,CAAC,CAED,KAAM,CAAAO,gBAAgB,CAAI1G,QAAQ,EAAK,CACrC,OAAQA,QAAQ,EACd,IAAK,SAAS,CACZ,mBACE3G,IAAA,CAACtC,KAAK,EACJuF,EAAE,CAAE,CACFD,QAAQ,CAAE,MAAM,CAChBsK,MAAM,CAAE,SAAS,CACjBC,UAAU,CAAE,YAAY,CACxB,SAAS,CAAE,CACTtM,KAAK,CAAE,SACT,CACF,CAAE,CACH,CAAC,CAEN,IAAK,QAAQ,CACX,mBACEjB,IAAA,CAAC3C,UAAU,EACT4F,EAAE,CAAE,CACFD,QAAQ,CAAE,MAAM,CAChBsK,MAAM,CAAE,SAAS,CACjBC,UAAU,CAAE,YAAY,CACxB,SAAS,CAAE,CACTtM,KAAK,CAAE,SACT,CACF,CAAE,CACH,CAAC,CAEN,IAAK,WAAW,CACd,mBACEjB,IAAA,CAAC7C,aAAa,EACZ8F,EAAE,CAAE,CACFD,QAAQ,CAAE,MAAM,CAChBsK,MAAM,CAAE,SAAS,CACjBC,UAAU,CAAE,YAAY,CACxB,SAAS,CAAE,CACTtM,KAAK,CAAE,SACT,CACF,CAAE,CACH,CAAC,CAEN,IAAK,UAAU,CACb,mBACEjB,IAAA,CAAC5C,YAAY,EACX6F,EAAE,CAAE,CACFD,QAAQ,CAAE,MAAM,CAChBsK,MAAM,CAAE,SAAS,CACjBC,UAAU,CAAE,YAAY,CACxB,SAAS,CAAE,CACTtM,KAAK,CAAE,SACT,CACF,CAAE,CACH,CAAC,CAEN,IAAK,UAAU,CACb,mBACEjB,IAAA,CAACzC,YAAY,EACX0F,EAAE,CAAE,CACFD,QAAQ,CAAE,MAAM,CAChBsK,MAAM,CAAE,SAAS,CACjBC,UAAU,CAAE,YAAY,CACxB,SAAS,CAAE,CACTtM,KAAK,CAAE,SACT,CACF,CAAE,CACH,CAAC,CAEN,IAAK,QAAQ,CACX,mBACEjB,IAAA,CAACK,UAAU,EACTuG,IAAI,CAAC,YAAY,CACjB3D,EAAE,CAAE,CACFD,QAAQ,CAAE,MAAM,CAChBsK,MAAM,CAAE,SAAS,CACjBC,UAAU,CAAE,YAAY,CACxB,SAAS,CAAE,CACTtM,KAAK,CAAE,MACT,CACF,CAAE,CACH,CAAC,CAEN,IAAK,OAAO,CACV,mBACEjB,IAAA,CAACxC,SAAS,EACRyF,EAAE,CAAE,CACFD,QAAQ,CAAE,MAAM,CAChBsK,MAAM,CAAE,SAAS,CACjBC,UAAU,CAAE,YAAY,CACxB,SAAS,CAAE,CACTtM,KAAK,CAAE,SACT,CACF,CAAE,CACH,CAAC,CAGN,IAAK,OAAO,CACZ,IAAK,OAAO,CACV,mBACEjB,IAAA,CAACrC,SAAS,EACRsF,EAAE,CAAE,CACFD,QAAQ,CAAE,MAAM,CAChBsK,MAAM,CAAE,SAAS,CACjBC,UAAU,CAAE,YAAY,CACxB,SAAS,CAAE,CACTtM,KAAK,CAAE,SACT,CACF,CAAE,CACH,CAAC,CAEN,IAAK,UAAU,CACb,mBACEjB,IAAA,CAACpC,YAAY,EACXqF,EAAE,CAAE,CACFD,QAAQ,CAAE,MAAM,CAChBsK,MAAM,CAAE,SAAS,CACjBC,UAAU,CAAE,YAAY,CACxB,SAAS,CAAE,CACTtM,KAAK,CAAE,SACT,CACF,CAAE,CACH,CAAC,CAEN,QACE,MAAO,KAAI,CACf,CACF,CAAC,CAED,mBACEf,KAAA,CAAC9D,SAAS,EACR6G,EAAE,CAAE,CACF,2BAA2B,CAAE,CAC3BuK,YAAY,CAAE,GAAK;AACrB,CACF,CAAE,CAAAjN,QAAA,eAEFL,KAAA,CAACpD,MAAM,EAAAyD,QAAA,eACLP,IAAA,UAAAO,QAAA,CAAO,mBAAiB,CAAO,CAAC,cAChCP,IAAA,SACEiH,IAAI,CAAC,aAAa,CAClBwG,OAAO,CAAE,8BACPvK,IAAI,CAACI,SAAS,EAAIJ,IAAI,CAACK,QAAQ,CAC3B,MAAML,IAAI,CAACI,SAAS,IAAIJ,IAAI,CAACK,QAAQ,EAAE,CACvC,EAAE,GAENE,OAAO,CAACS,UAAU,CAAG,KAAKT,OAAO,CAACS,UAAU,EAAE,CAAG,EAAE,4EACwB,CAC9E,CAAC,cAGFlE,IAAA,SACE0N,QAAQ,CAAC,UAAU,CACnBD,OAAO,CAAE,GACPvK,IAAI,CAACI,SAAS,EAAIJ,IAAI,CAACK,QAAQ,CAC3B,GAAGL,IAAI,CAACI,SAAS,IAAIJ,IAAI,CAACK,QAAQ,KAAK,CACvC,EAAE,iBACU,CACnB,CAAC,cACFvD,IAAA,SACE0N,QAAQ,CAAC,gBAAgB,CACzBD,OAAO,CAAE,8BACPvK,IAAI,CAACI,SAAS,EAAIJ,IAAI,CAACK,QAAQ,CAC3B,MAAML,IAAI,CAACI,SAAS,IAAIJ,IAAI,CAACK,QAAQ,EAAE,CACvC,EAAE,GAENE,OAAO,CAACS,UAAU,CAAG,KAAKT,OAAO,CAACS,UAAU,EAAE,CAAG,EAAE,4EACwB,CAC9E,CAAC,cACFlE,IAAA,SAAM0N,QAAQ,CAAC,SAAS,CAACD,OAAO,CAAC,SAAS,CAAE,CAAC,cAC7CzN,IAAA,SAAM0N,QAAQ,CAAC,QAAQ,CAACD,OAAO,CAAEtE,MAAM,CAACwE,QAAQ,CAACC,IAAK,CAAE,CAAC,CACxDnK,OAAO,CAACM,cAAc,eACrB/D,IAAA,SAAM0N,QAAQ,CAAC,UAAU,CAACD,OAAO,CAAEhK,OAAO,CAACM,cAAe,CAAE,CAC7D,cACD/D,IAAA,SAAM0N,QAAQ,CAAC,cAAc,CAACD,OAAO,CAAC,SAAS,CAAE,CAAC,cAGlDzN,IAAA,SAAMiH,IAAI,CAAC,cAAc,CAACwG,OAAO,CAAC,qBAAqB,CAAE,CAAC,cAC1DzN,IAAA,SACEiH,IAAI,CAAC,eAAe,CACpBwG,OAAO,CAAE,GACPvK,IAAI,CAACI,SAAS,EAAIJ,IAAI,CAACK,QAAQ,CAC3B,GAAGL,IAAI,CAACI,SAAS,IAAIJ,IAAI,CAACK,QAAQ,KAAK,CACvC,EAAE,iBACU,CACnB,CAAC,cACFvD,IAAA,SACEiH,IAAI,CAAC,qBAAqB,CAC1BwG,OAAO,CAAE,8BACPvK,IAAI,CAACI,SAAS,EAAIJ,IAAI,CAACK,QAAQ,CAC3B,MAAML,IAAI,CAACI,SAAS,IAAIJ,IAAI,CAACK,QAAQ,EAAE,CACvC,EAAE,GAENE,OAAO,CAACS,UAAU,CAAG,KAAKT,OAAO,CAACS,UAAU,EAAE,CAAG,EAAE,4EACwB,CAC9E,CAAC,CACDT,OAAO,CAACM,cAAc,eACrB/D,IAAA,SAAMiH,IAAI,CAAC,eAAe,CAACwG,OAAO,CAAEhK,OAAO,CAACM,cAAe,CAAE,CAC9D,CAGAb,IAAI,CAACI,SAAS,eACbtD,IAAA,SAAM0N,QAAQ,CAAC,oBAAoB,CAACD,OAAO,CAAEvK,IAAI,CAACI,SAAU,CAAE,CAC/D,CACAJ,IAAI,CAACK,QAAQ,eACZvD,IAAA,SAAM0N,QAAQ,CAAC,mBAAmB,CAACD,OAAO,CAAEvK,IAAI,CAACK,QAAS,CAAE,CAC7D,CACAE,OAAO,CAACG,QAAQ,eACf5D,IAAA,SAAM0N,QAAQ,CAAC,kBAAkB,CAACD,OAAO,CAAEhK,OAAO,CAACG,QAAS,CAAE,CAC/D,EACK,CAAC,CACRc,SAAS,cACR1E,IAAA,CAACT,MAAM,CAACsO,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BT,UAAU,CAAE,CACVW,QAAQ,CAAE,GAAG,CACbC,IAAI,CAAE,SACR,CAAE,CAAA5N,QAAA,cAEFL,KAAA,CAAChE,GAAG,EACF+G,EAAE,CAAE,CACFmL,OAAO,CAAE,MAAM,CACfC,cAAc,CAAE,QAAQ,CACxBC,UAAU,CAAE,QAAQ,CACpBC,SAAS,CAAE,OAAO,CAClBC,aAAa,CAAE,QAAQ,CACvBC,GAAG,CAAE,CACP,CAAE,CAAAlO,QAAA,eAEFP,IAAA,CAAC3D,gBAAgB,EAACqS,IAAI,CAAE,EAAG,CAAE,CAAC,cAC9B1O,IAAA,CAAC7B,UAAU,EAACwQ,OAAO,CAAC,IAAI,CAAC1N,KAAK,CAAC,eAAe,CAAAV,QAAA,CAAC,yBAE/C,CAAY,CAAC,EACV,CAAC,CACI,CAAC,cAEbL,KAAA,CAAAE,SAAA,EAAAG,QAAA,eAEEP,IAAA,CAAC9D,GAAG,EACF+G,EAAE,CAAE,CACFmL,OAAO,CAAE,MAAM,CACfC,cAAc,CAAE,QAAQ,CACxBO,YAAY,CAAE,MAChB,CAAE,CAAArO,QAAA,cAEFL,KAAA,CAACpE,IAAI,EACHoL,KAAK,CAAExF,SAAU,CACjBmN,QAAQ,CAAElM,eAAgB,CAC1B,aAAW,cAAc,CAAApC,QAAA,eAEzBP,IAAA,CAACnE,GAAG,EAACgL,KAAK,CAAC,YAAY,CAAE,CAAC,cAE1B7G,IAAA,CAACnE,GAAG,EAACgL,KAAK,CAAC,OAAO,CAAE,CAAC,EACjB,CAAC,CACJ,CAAC,cACN3G,KAAA,CAAC5E,IAAI,EAACwT,SAAS,MAACC,OAAO,CAAE,CAAE,CAAC9L,EAAE,CAAE,CAAE+L,SAAS,CAAE,OAAQ,CAAE,CAAAzO,QAAA,eACrDL,KAAA,CAAC5E,IAAI,EACH2T,IAAI,MACJC,EAAE,CAAE,EAAG,CACPC,EAAE,CAAE,CAAE,CACNlM,EAAE,CAAE,CACF,mDAAmD,CAAE,CACnDmM,QAAQ,CAAE,qBAAqB,CAAE;AACjC5B,YAAY,CAAE,MAChB,CAAC,CACD,4BAA4B,CAAE,CAC5B4B,QAAQ,CAAE,mBAAmB,CAC7B5B,YAAY,CAAE,MAChB,CAAC,CACD,2BAA2B,CAAE,CAC3B4B,QAAQ,CAAE,MAAM,CAChB5B,YAAY,CAAE,GAChB,CACF,CAAE,CAAAjN,QAAA,EAED2C,IAAI,CAACH,QAAQ,eACZ7C,KAAA,CAACnE,IAAI,EAACkH,EAAE,CAAE,CAAEoM,CAAC,CAAE,CAAC,CAAET,YAAY,CAAE,MAAM,CAAEnG,QAAQ,CAAE,UAAW,CAAE,CAAAlI,QAAA,eAC7DP,IAAA,CAAC7B,UAAU,EACTwQ,OAAO,CAAC,UAAU,CAClB1L,EAAE,CAAE,CACFqM,EAAE,CAAE,CAAC,CACLlB,OAAO,CAAE,OAAO,CAChBnN,KAAK,CAAE,gBACT,CAAE,CAAAV,QAAA,CACH,WAED,CAAY,CAAC,cACbL,KAAA,CAAChE,GAAG,EACF+G,EAAE,CAAE,CACFmL,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBG,GAAG,CAAE,CAAC,CACNa,EAAE,CAAE,CACN,CAAE,CAAA/O,QAAA,eAEFP,IAAA,CAAC9D,GAAG,EACF+G,EAAE,CAAE,CACFmL,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBD,cAAc,CAAE,QAAQ,CACxBkB,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,EAAE,CACVC,YAAY,CAAE,MAAM,CACpBC,UAAU,CACR,uDAAuD,CACzDC,MAAM,CAAE,qBAAqB,CAC7BC,SAAS,CAAE,sBACb,CAAE,CAAArP,QAAA,CAEDuC,aAAa,CAACI,IAAI,CAACH,QAAQ,CAAC,CAC1B,CAAC,cACN/C,IAAA,CAAC7B,UAAU,EAACwQ,OAAO,CAAC,IAAI,CAAApO,QAAA,CAAE2C,IAAI,CAACH,QAAQ,CAAa,CAAC,EAClD,CAAC,CACLG,IAAI,CAACH,QAAQ,GAAK,WAAW,EAC5BG,IAAI,CAACH,QAAQ,GAAK,YAAY,eAC5B/C,IAAA,CAAC9D,GAAG,EACF+G,EAAE,CAAE,CACF4M,EAAE,CAAE,CAAEX,EAAE,CAAE,CAAC,CAAEY,EAAE,CAAE,CAAE,CAAC,CACpBrH,QAAQ,CAAE,CAAEqH,EAAE,CAAE,UAAW,CAAC,CAC5BC,GAAG,CAAE,CAAED,EAAE,CAAE,EAAG,CAAC,CACfE,KAAK,CAAE,CAAEF,EAAE,CAAE,EAAG,CAClB,CAAE,CAAAvP,QAAA,cAEFP,IAAA,CAACxE,MAAM,EACLkT,IAAI,CAAC,OAAO,CACZC,OAAO,CAAC,UAAU,CAClBsB,OAAO,CAAEA,CAAA,GAAM,CACbvN,QAAQ,CAAC,gBAAgB,CAAC,CAC5B,CAAE,CAAAnC,QAAA,CACH,cAED,CAAQ,CAAC,CACN,CACN,EACC,CACP,CACAmB,SAAS,GAAK,CAAC,eACd1B,IAAA,CAACH,UAAU,EACT4D,OAAO,CAAEA,OAAQ,CACjBP,IAAI,CAAEA,IAAK,CACXhB,kBAAkB,CAAEA,kBAAmB,CACvCC,qBAAqB,CAAEA,qBAAsB,CAC7CC,eAAe,CAAEA,eAAgB,CACjC6F,iBAAiB,CAAEA,iBAAkB,CACrCR,mBAAmB,CAAEA,mBAAoB,CACzCT,gBAAgB,CAAEA,gBAAiB,CACnCqB,UAAU,CAAEA,UAAW,CACvB6H,oBAAoB,CAAEvI,kBAAmB,CACzCS,sBAAsB,CAAEA,sBAAuB,CAChD,CACF,CAEA1G,SAAS,GAAK,CAAC,eACdxB,KAAA,CAAC5E,IAAI,EAACwT,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAxO,QAAA,eAEzBL,KAAA,CAAC5E,IAAI,EAAC2T,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAAA5O,QAAA,EAEvBwF,wBAAwB,eACvB/F,IAAA,CAAC1E,IAAI,EAAC2T,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAAClM,EAAE,CAAE,CAAE2L,YAAY,CAAE,MAAO,CAAE,CAAArO,QAAA,cACtDL,KAAA,CAACnE,IAAI,EAAAwE,QAAA,eACHP,IAAA,CAAC7D,UAAU,EACTqK,KAAK,CAAC,iBAAiB,CACvB2J,SAAS,CAAC,qGAAqG,CAChH,CAAC,cACFnQ,IAAA,CAAChE,WAAW,EAAAuE,QAAA,cACVL,KAAA,CAAC5E,IAAI,EAACwT,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAxO,QAAA,eACzBP,IAAA,CAAC/D,UAAU,EACTgH,EAAE,CAAE,CACFwF,QAAQ,CAAE,UAAU,CACpBuH,KAAK,CAAE,CAAC,CACRD,GAAG,CAAE,CACP,CAAE,CACF,aAAW,OAAO,CAClBE,OAAO,CAAEA,CAAA,GAAM,CACbjK,2BAA2B,CAAC,KAAK,CAAC,CAClCM,iBAAiB,CAAC,KAAK,CAAC,CAC1B,CAAE,CAAA/F,QAAA,cAEFP,IAAA,CAAC/C,SAAS,GAAE,CAAC,CACH,CAAC,cACb+C,IAAA,CAAC1E,IAAI,EAAC2T,IAAI,MAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAA5O,QAAA,cACtBP,IAAA,CAAC3E,SAAS,EACR4L,IAAI,CAAC,OAAO,CACZJ,KAAK,CAAC,sBAAsB,CAC5BuJ,OAAO,MACPlJ,KAAK,CAAEjB,gBAAgB,CAACZ,KAAM,CAC9BpC,EAAE,CAAE,CAAEsM,KAAK,CAAE,MAAO,CAAE,CACtBV,QAAQ,CAAEzE,4BAA6B,CACxC,CAAC,CACE,CAAC,cACPpK,IAAA,CAAC1E,IAAI,EAAC2T,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAAA5O,QAAA,cACxBP,IAAA,CAAC3E,SAAS,EACR4L,IAAI,CAAC,SAAS,CACdJ,KAAK,CAAC,oBAAoB,CAC1BK,KAAK,CAAEjB,gBAAgB,CAACd,OAAQ,CAChCiL,OAAO,MACPnN,EAAE,CAAE,CAAEsM,KAAK,CAAE,MAAO,CAAE,CACtBV,QAAQ,CAAEzE,4BAA6B,CACxC,CAAC,CACE,CAAC,EACH,CAAC,CACI,CAAC,cACdlK,KAAA,CAAC1E,MAAM,EACLyU,OAAO,CAAE5F,oBAAqB,CAC9BpJ,KAAK,CAAC,SAAS,CACf0N,OAAO,CAAC,UAAU,CAClB0B,QAAQ,CAAE7N,kBAAmB,CAC7BS,EAAE,CAAE,CACFqN,MAAM,CAAE,MAAM,CACdnP,eAAe,CAAE,SAAS,CAC1BF,KAAK,CAAE,OAAO,CACd,SAAS,CAAE,CACTA,KAAK,CAAE,SACT,CACF,CAAE,CAAAV,QAAA,eAEFP,IAAA,SACEuQ,KAAK,CAAE,CACLC,WAAW,CAAE,MACf,CAAE,CAAAjQ,QAAA,CAEDiC,kBAAkB,CACf,WAAW,CACX,gBAAgB,CAChB,CAAC,CACNA,kBAAkB,cACjBxC,IAAA,CAAC3D,gBAAgB,EAACqS,IAAI,CAAE,EAAG,CAACzN,KAAK,CAAC,SAAS,CAAE,CAAC,cAE9CjB,IAAA,CAAC9C,QAAQ,GAAE,CACZ,EACK,CAAC,cAETgD,KAAA,CAACzE,MAAM,EACL0K,IAAI,CAAEmG,wBAAyB,CAC/BmE,OAAO,CAAEA,CAAA,GAAM,CACblE,2BAA2B,CAAC,KAAK,CAAC,CACpC,CAAE,CAAAhM,QAAA,eAEFP,IAAA,CAACtE,WAAW,EAACuF,KAAK,CAAC,SAAS,CAAAV,QAAA,CAAC,kBAE7B,CAAa,CAAC,cAEdP,IAAA,CAACrE,aAAa,EAAA4E,QAAA,cACZP,IAAA,CAAC1E,IAAI,EACH2H,EAAE,CAAE,CACFmL,OAAO,CAAE,MACX,CAAE,CACFU,SAAS,MACTC,OAAO,CAAE,CAAE,CAAAxO,QAAA,CAEV8D,WAAW,CAACqM,GAAG,CAACC,KAAA,EAAkB,IAAjB,CAAEhK,QAAS,CAAC,CAAAgK,KAAA,CAC5B,GAAI,CAAA/J,IAAI,CAAGyG,gBAAgB,CAAC1G,QAAQ,CAAC,CACrC,mBACE3G,IAAA,CAAC1E,IAAI,EACH2T,IAAI,MACJC,EAAE,CAAE,CAAE,CACNY,EAAE,CAAE,CAAE,CACNX,EAAE,CAAE,CAAE,CACNyB,EAAE,CAAE,CAAE,CACN3N,EAAE,CAAE,CACFmL,OAAO,CAAE,MAAM,CACfC,cAAc,CAAE,QAAQ,CACxBW,SAAS,CAAE,KACb,CAAE,CAAAzO,QAAA,cAGFP,IAAA,CAACS,gBAAgB,EACf+F,KAAK,CAAEG,QAAS,CAChB1D,EAAE,CAAE,CACF,uBAAuB,CAAE,CACvBD,QAAQ,CAAE,MACZ,CACF,CAAE,CAAAzC,QAAA,cAEFP,IAAA,CAACxE,MAAM,EACLmT,OAAO,CAAC,UAAU,CAClB1L,EAAE,CAAE,CACFhC,KAAK,CAAE,uBAAuB,CAC9B4P,WAAW,CACT,uBAAuB,CACzBrB,MAAM,CAAE,MAAM,CACdsB,OAAO,CAAE,WACX,CAAE,CACFb,OAAO,CAAEA,CAAA,GAAM,CACb/J,mBAAmB,CAAE2D,QAAQ,GAAM,CACjC,GAAGA,QAAQ,CACXzE,QAAQ,CAAEuB,QACZ,CAAC,CAAC,CAAC,CACH4F,2BAA2B,CAAC,KAAK,CAAC,CAClCjG,iBAAiB,CAAC,IAAI,CAAC,CACzB,CAAE,CAAA/F,QAAA,CAEDqG,IAAI,CACC,CAAC,CACO,CAAC,EA9BdD,QA+BD,CAAC,CAEX,CAAC,CAAC,CACE,CAAC,CACM,CAAC,cAChB3G,IAAA,CAACpE,aAAa,GAAgB,CAAC,EACzB,CAAC,EACL,CAAC,CACH,CACP,CAEA8J,wBAAwB,eACvB1F,IAAA,CAAC1E,IAAI,EACH2T,IAAI,MACJC,EAAE,CAAE,EAAG,CACPC,EAAE,CAAE,EAAG,CACPlM,EAAE,CAAE,CAAE+L,SAAS,CAAE,MAAM,CAAEJ,YAAY,CAAE,MAAO,CAAE,CAAArO,QAAA,cAEhDL,KAAA,CAACnE,IAAI,EAAAwE,QAAA,eACHP,IAAA,CAAC7D,UAAU,EACTqK,KAAK,CAAC,iBAAiB,CACvB2J,SAAS,CAAC,6MAA6M,CACxN,CAAC,cACFnQ,IAAA,CAAChE,WAAW,EAAAuE,QAAA,cACVL,KAAA,CAAC5E,IAAI,EAACwT,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAxO,QAAA,eACzBP,IAAA,CAAC/D,UAAU,EACTgH,EAAE,CAAE,CACFwF,QAAQ,CAAE,UAAU,CACpBuH,KAAK,CAAE,CAAC,CACRD,GAAG,CAAE,CACP,CAAE,CACF,aAAW,OAAO,CAClBE,OAAO,CAAEA,CAAA,GAAM,CACbtK,2BAA2B,CAAC,KAAK,CAAC,CACpC,CAAE,CAAApF,QAAA,cAEFP,IAAA,CAAC/C,SAAS,GAAE,CAAC,CACH,CAAC,cACb+C,IAAA,CAAC1E,IAAI,EAAC2T,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAAA5O,QAAA,cACxBP,IAAA,CAAC3E,SAAS,EACR4L,IAAI,CAAC,OAAO,CACZJ,KAAK,CAAC,sBAAsB,CAC5BK,KAAK,CAAEtB,gBAAgB,CAACP,KAAM,CAC9B+K,OAAO,MACPnN,EAAE,CAAE,CAAEsM,KAAK,CAAE,MAAO,CAAE,CACtBV,QAAQ,CAAEjF,4BAA6B,CACxC,CAAC,CACE,CAAC,cACP1J,KAAA,CAAC5E,IAAI,EAAC2T,IAAI,MAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAA5O,QAAA,eACtBP,IAAA,CAACzE,MAAM,EACLgV,KAAK,CAAE,CACLhB,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MACV,CAAE,CACFY,OAAO,MACPW,GAAG,CAAEnL,gBAAgB,CAACH,IAAK,CAC5B,CAAC,cACFzF,IAAA,CAACd,aAAa,EACZ8R,QAAQ,CAAElH,+BAAgC,CAC3C,CAAC,EACE,CAAC,cACP9J,IAAA,CAAC1E,IAAI,EAAC2T,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAAA5O,QAAA,cACxBP,IAAA,CAAC3E,SAAS,EACR4L,IAAI,CAAC,SAAS,CACdJ,KAAK,CAAC,oBAAoB,CAC1BK,KAAK,CAAEtB,gBAAgB,CAACT,OAAQ,CAChCiL,OAAO,MACPnN,EAAE,CAAE,CAAEsM,KAAK,CAAE,MAAO,CAAE,CACtBV,QAAQ,CAAEjF,4BAA6B,CACxC,CAAC,CACE,CAAC,EACH,CAAC,CACI,CAAC,cACd1J,KAAA,CAAC1E,MAAM,EACLyU,OAAO,CAAEjG,oBAAqB,CAC9B/I,KAAK,CAAC,SAAS,CACf0N,OAAO,CAAC,UAAU,CAClB0B,QAAQ,CAAE/N,kBAAmB,CAC7BW,EAAE,CAAE,CACFqN,MAAM,CAAE,MAAM,CACdnP,eAAe,CAAE,SAAS,CAC1BF,KAAK,CAAE,OAAO,CACd,SAAS,CAAE,CACTA,KAAK,CAAE,SACT,CACF,CAAE,CAAAV,QAAA,eAEFP,IAAA,SACEuQ,KAAK,CAAE,CACLC,WAAW,CAAE,MACf,CAAE,CAAAjQ,QAAA,CAED+B,kBAAkB,CACf,WAAW,CACX,gBAAgB,CAChB,CAAC,CACNA,kBAAkB,cACjBtC,IAAA,CAAC3D,gBAAgB,EAACqS,IAAI,CAAE,EAAG,CAACzN,KAAK,CAAC,SAAS,CAAE,CAAC,cAE9CjB,IAAA,CAAC9C,QAAQ,GAAE,CACZ,EACK,CAAC,EACL,CAAC,CACH,CACP,cAEDgD,KAAA,CAACnE,IAAI,EACHkH,EAAE,CAAE,CACFyM,UAAU,CACR,+CAA+C,CACjDC,MAAM,CAAE,qBAAqB,CAC7BC,SAAS,CAAE,qCAAqC,CAChDH,YAAY,CAAE,MAAM,CACpBwB,QAAQ,CAAE,QAAQ,CAClBxI,QAAQ,CAAE,UAAU,CACpB,WAAW,CAAE,CACXgF,OAAO,CAAE,IAAI,CACbhF,QAAQ,CAAE,UAAU,CACpBsH,GAAG,CAAE,CAAC,CACNmB,IAAI,CAAE,CAAC,CACPlB,KAAK,CAAE,CAAC,CACRR,MAAM,CAAE,KAAK,CACbE,UAAU,CACR,0CACJ,CACF,CAAE,CAAAnP,QAAA,eAEFP,IAAA,CAAC7D,UAAU,EACTqK,KAAK,cACHtG,KAAA,CAAChE,GAAG,EACF+G,EAAE,CAAE,CACFmL,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBG,GAAG,CAAE,CACP,CAAE,CAAAlO,QAAA,eAEFP,IAAA,CAAC9D,GAAG,EACF+G,EAAE,CAAE,CACFsM,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,EAAE,CACVC,YAAY,CAAE,MAAM,CACpBC,UAAU,CACR,2CAA2C,CAC7CtB,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBD,cAAc,CAAE,QAAQ,CACxBuB,SAAS,CAAE,oCACb,CAAE,CAAArP,QAAA,cAEFP,IAAA,CAAChD,UAAU,EACTiG,EAAE,CAAE,CAAEhC,KAAK,CAAE,OAAO,CAAE+B,QAAQ,CAAE,EAAG,CAAE,CACtC,CAAC,CACC,CAAC,cACNhD,IAAA,CAAC7B,UAAU,EACTwQ,OAAO,CAAC,IAAI,CACZ1L,EAAE,CAAE,CAAEkO,UAAU,CAAE,GAAG,CAAElQ,KAAK,CAAE,SAAU,CAAE,CAAAV,QAAA,CAC3C,qBAED,CAAY,CAAC,EACV,CACN,CACD4P,SAAS,cACPnQ,IAAA,CAAC7B,UAAU,EACTwQ,OAAO,CAAC,OAAO,CACf1L,EAAE,CAAE,CACFhC,KAAK,CAAE,gBAAgB,CACvB4O,EAAE,CAAE,CAAC,CACLuB,UAAU,CAAE,GAAG,CACfpO,QAAQ,CAAE,MAAM,CAChB4L,YAAY,CAAE,MAChB,CAAE,CAAArO,QAAA,CACH,oJAID,CAAY,CACb,CACD0C,EAAE,CAAE,CAAEoO,EAAE,CAAE,CAAE,CAAE,CACf,CAAC,cACFrR,IAAA,CAAChE,WAAW,EAACiH,EAAE,CAAE,CAAEqO,EAAE,CAAE,CAAE,CAAE,CAAA/Q,QAAA,cACzBP,IAAA,CAAC1E,IAAI,EAACwT,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAxO,QAAA,cACzBP,IAAA,CAAC1E,IAAI,EAAC2T,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAAA5O,QAAA,cACxBP,IAAA,CAACxE,MAAM,EACLkT,IAAI,CAAC,OAAO,CACZ6C,SAAS,MACT5C,OAAO,CAAC,UAAU,CAClB1L,EAAE,CAAE,CACFsL,SAAS,CAAE,MAAM,CACjBmB,UAAU,CACR,+CAA+C,CACjDC,MAAM,CAAE,sBAAsB,CAC9BF,YAAY,CAAE,MAAM,CACpBxO,KAAK,CAAE,SAAS,CAChB+B,QAAQ,CAAE,MAAM,CAChBmO,UAAU,CAAE,KAAK,CACjBK,aAAa,CAAE,MAAM,CACrB/I,QAAQ,CAAE,UAAU,CACpBwI,QAAQ,CAAE,QAAQ,CAClB1D,UAAU,CACR,uCAAuC,CACzC,WAAW,CAAE,CACXE,OAAO,CAAE,IAAI,CACbhF,QAAQ,CAAE,UAAU,CACpBsH,GAAG,CAAE,CAAC,CACNmB,IAAI,CAAE,OAAO,CACb3B,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACdE,UAAU,CACR,2EAA2E,CAC7EnC,UAAU,CAAE,gBACd,CAAC,CACD,SAAS,CAAE,CACTmC,UAAU,CACR,+CAA+C,CACjDC,MAAM,CAAE,mBAAmB,CAC3B8B,SAAS,CAAE,8BAA8B,CACzC7B,SAAS,CACP,qCAAqC,CACvC,WAAW,CAAE,CACXsB,IAAI,CAAE,MACR,CACF,CAAC,CACD,UAAU,CAAE,CACVO,SAAS,CAAE,8BACb,CACF,CAAE,CACFxB,OAAO,CAAEA,CAAA,GAAMzD,qBAAqB,CAAC,CAAE,CAAAjM,QAAA,cAEvCL,KAAA,CAAChE,GAAG,EACF+G,EAAE,CAAE,CACFmL,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBG,GAAG,CAAE,GAAG,CACRhG,QAAQ,CAAE,UAAU,CACpBiJ,MAAM,CAAE,CACV,CAAE,CAAAnR,QAAA,eAEFP,IAAA,CAAC9D,GAAG,EACF+G,EAAE,CAAE,CACFsM,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,EAAE,CACVC,YAAY,CAAE,MAAM,CACpBC,UAAU,CACR,2CAA2C,CAC7CtB,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBD,cAAc,CAAE,QAAQ,CACxBuB,SAAS,CACP,oCAAoC,CACtCrC,UAAU,CAAE,eACd,CAAE,CAAAhN,QAAA,cAEFP,IAAA,CAAChD,UAAU,EACTiG,EAAE,CAAE,CAAED,QAAQ,CAAE,EAAE,CAAE/B,KAAK,CAAE,OAAQ,CAAE,CACtC,CAAC,CACC,CAAC,cACNf,KAAA,CAAChE,GAAG,EAAC+G,EAAE,CAAE,CAAE0O,SAAS,CAAE,MAAM,CAAEC,IAAI,CAAE,CAAE,CAAE,CAAArR,QAAA,eACtCP,IAAA,CAAC7B,UAAU,EACTwQ,OAAO,CAAC,IAAI,CACZ1L,EAAE,CAAE,CACFkO,UAAU,CAAE,GAAG,CACflQ,KAAK,CAAE,SAAS,CAChB+B,QAAQ,CAAE,MAAM,CAChBsM,EAAE,CAAE,GACN,CAAE,CAAA/O,QAAA,CACH,oBAED,CAAY,CAAC,cACbP,IAAA,CAAC7B,UAAU,EACTwQ,OAAO,CAAC,OAAO,CACf1L,EAAE,CAAE,CACFhC,KAAK,CAAE,gBAAgB,CACvB+B,QAAQ,CAAE,MAAM,CAChBoO,UAAU,CAAE,GACd,CAAE,CAAA7Q,QAAA,CACH,2BAED,CAAY,CAAC,EACV,CAAC,EACH,CAAC,CACA,CAAC,CACL,CAAC,CACH,CAAC,CACI,CAAC,EACV,CAAC,cAEPL,KAAA,CAACnE,IAAI,EACHkH,EAAE,CAAE,CACF+L,SAAS,CAAE,MAAM,CACjBU,UAAU,CACR,+CAA+C,CACjDC,MAAM,CAAE,qBAAqB,CAC7BC,SAAS,CAAE,sCAAsC,CACjDH,YAAY,CAAE,MAAM,CACpBwB,QAAQ,CAAE,QAAQ,CAClBxI,QAAQ,CAAE,UAAU,CACpB,WAAW,CAAE,CACXgF,OAAO,CAAE,IAAI,CACbhF,QAAQ,CAAE,UAAU,CACpBsH,GAAG,CAAE,CAAC,CACNmB,IAAI,CAAE,CAAC,CACPlB,KAAK,CAAE,CAAC,CACRR,MAAM,CAAE,KAAK,CACbE,UAAU,CACR,0CACJ,CACF,CAAE,CAAAnP,QAAA,eAEFP,IAAA,CAAC7D,UAAU,EACTqK,KAAK,cACHtG,KAAA,CAAChE,GAAG,EACF+G,EAAE,CAAE,CACFmL,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBG,GAAG,CAAE,CACP,CAAE,CAAAlO,QAAA,eAEFP,IAAA,CAAC9D,GAAG,EACF+G,EAAE,CAAE,CACFsM,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,EAAE,CACVC,YAAY,CAAE,MAAM,CACpBC,UAAU,CACR,2CAA2C,CAC7CtB,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBD,cAAc,CAAE,QAAQ,CACxBuB,SAAS,CACP,qCACJ,CAAE,CAAArP,QAAA,cAEFP,IAAA,CAAC9B,YAAY,EACX+E,EAAE,CAAE,CAAEhC,KAAK,CAAE,OAAO,CAAE+B,QAAQ,CAAE,EAAG,CAAE,CACtC,CAAC,CACC,CAAC,cACNhD,IAAA,CAAC7B,UAAU,EACTwQ,OAAO,CAAC,IAAI,CACZ1L,EAAE,CAAE,CAAEkO,UAAU,CAAE,GAAG,CAAElQ,KAAK,CAAE,SAAU,CAAE,CAAAV,QAAA,CAC3C,kBAED,CAAY,CAAC,EACV,CACN,CACD4P,SAAS,cACPnQ,IAAA,CAAC7B,UAAU,EACTwQ,OAAO,CAAC,OAAO,CACf1L,EAAE,CAAE,CACFhC,KAAK,CAAE,gBAAgB,CACvB4O,EAAE,CAAE,CAAC,CACLuB,UAAU,CAAE,GAAG,CACfpO,QAAQ,CAAE,MAAM,CAChB4L,YAAY,CAAE,MAChB,CAAE,CAAArO,QAAA,CACH,gKAKD,CAAY,CACb,CACD0C,EAAE,CAAE,CAAEoO,EAAE,CAAE,CAAE,CAAE,CACf,CAAC,cACFnR,KAAA,CAAClE,WAAW,EAACiH,EAAE,CAAE,CAAEqO,EAAE,CAAE,CAAE,CAAE,CAAA/Q,QAAA,eACzBP,IAAA,CAAC1E,IAAI,EAACwT,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAxO,QAAA,CACxB8D,WAAW,CAACqM,GAAG,CAACmB,KAAA,EAAyB,IAAxB,CAAElL,QAAQ,CAAE1F,KAAM,CAAC,CAAA4Q,KAAA,CACnC,GAAI,CAAAjL,IAAI,CAAGyG,gBAAgB,CAAC1G,QAAQ,CAAC,CACrC,mBACE3G,IAAA,CAAC1E,IAAI,EACH2T,IAAI,MACJC,EAAE,CAAE,CAAE,CACNY,EAAE,CAAE,CAAE,CACNX,EAAE,CAAE,GAAI,CACRyB,EAAE,CAAE,GAAI,CACR3N,EAAE,CAAE,CACFmL,OAAO,CAAE,MAAM,CACfC,cAAc,CAAE,QAClB,CAAE,CAAA9N,QAAA,cAGFP,IAAA,CAACS,gBAAgB,EACf+F,KAAK,CAAEG,QAAS,CAChB1D,EAAE,CAAE,CACF,uBAAuB,CAAE,CACvBD,QAAQ,CAAE,MACZ,CACF,CAAE,CAAAzC,QAAA,cAEFL,KAAA,CAAC1E,MAAM,EACLmT,OAAO,CAAC,UAAU,CAClB1L,EAAE,CAAE,CACFhC,KAAK,CAAE,uBAAuB,CAC9B4P,WAAW,CAAE,uBAAuB,CACpCrB,MAAM,CAAE,MAAM,CACdsB,OAAO,CAAE,WAAW,CACpBvC,SAAS,CAAE,OAAO,CAClBuD,QAAQ,CAAE,OAAO,CACjBlC,SAAS,CAAE,cAAc3O,KAAK,IAAI,CAClCsM,UAAU,CACR,uCAAuC,CACzCa,OAAO,CAAE,MAAM,CACfI,aAAa,CAAE,QAAQ,CACvBC,GAAG,CAAE,CAAC,CACNhG,QAAQ,CAAE,UAAU,CACpBwI,QAAQ,CAAE,QAAQ,CAClB,WAAW,CAAE,CACXxD,OAAO,CAAE,IAAI,CACbhF,QAAQ,CAAE,UAAU,CACpBsH,GAAG,CAAE,CAAC,CACNmB,IAAI,CAAE,CAAC,CACPlB,KAAK,CAAE,CAAC,CACRR,MAAM,CAAE,KAAK,CACbE,UAAU,CAAE,0BAA0BzO,KAAK,KAAKA,KAAK,KAAK,CAC1D8M,OAAO,CAAE,CAAC,CACVR,UAAU,CAAE,mBACd,CAAC,CACD,SAAS,CAAE,CACTmC,UAAU,CAAE,2BAA2BzO,KAAK,OAAOA,KAAK,KAAK,CAC7D0O,MAAM,CAAE,aAAa1O,KAAK,IAAI,CAC9BwQ,SAAS,CACP,8BAA8B,CAChC7B,SAAS,CAAE,eAAe3O,KAAK,IAAI,CACnC,WAAW,CAAE,CACX8M,OAAO,CAAE,CACX,CACF,CACF,CAAE,CACFkC,OAAO,CAAEA,CAAA,GACP1J,eAAe,CACb,qBAAqB,CACrBI,QAAQ,CACR1F,KACF,CACD,CAAAV,QAAA,eAEDP,IAAA,CAAC9D,GAAG,EAAC+G,EAAE,CAAE,CAAED,QAAQ,CAAE,EAAE,CAAEsM,EAAE,CAAE,GAAI,CAAE,CAAA/O,QAAA,CAChCqG,IAAI,CACF,CAAC,cACN5G,IAAA,CAAC7B,UAAU,EACTwQ,OAAO,CAAC,SAAS,CACjB1L,EAAE,CAAE,CACFkO,UAAU,CAAE,GAAG,CACfnO,QAAQ,CAAE,MAAM,CAChBwO,aAAa,CAAE,MAAM,CACrBzD,OAAO,CAAE,GACX,CAAE,CAAAxN,QAAA,CAEDoG,QAAQ,CACC,CAAC,EACP,CAAC,CACO,CAAC,EAxEdA,QAyED,CAAC,CAEX,CAAC,CAAC,CACE,CAAC,cAEPzG,KAAA,CAAChE,GAAG,EAAC+G,EAAE,CAAE,CAAE8O,EAAE,CAAE,CAAE,CAAE,CAAAxR,QAAA,eACjBL,KAAA,CAAChE,GAAG,EACF+G,EAAE,CAAE,CACFmL,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBG,GAAG,CAAE,CAAC,CACNa,EAAE,CAAE,CACN,CAAE,CAAA/O,QAAA,eAEFP,IAAA,CAAC9D,GAAG,EACF+G,EAAE,CAAE,CACFsM,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,EAAE,CACVC,YAAY,CAAE,KAAK,CACnBC,UAAU,CACR,2CAA2C,CAC7CtB,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBD,cAAc,CAAE,QAAQ,CACxBuB,SAAS,CAAE,oCACb,CAAE,CAAArP,QAAA,cAEFP,IAAA,CAACxC,SAAS,EACRyF,EAAE,CAAE,CAAEhC,KAAK,CAAE,OAAO,CAAE+B,QAAQ,CAAE,EAAG,CAAE,CACtC,CAAC,CACC,CAAC,cACNhD,IAAA,CAAC7B,UAAU,EACTwQ,OAAO,CAAC,IAAI,CACZ1L,EAAE,CAAE,CAAEkO,UAAU,CAAE,GAAG,CAAElQ,KAAK,CAAE,SAAU,CAAE,CAAAV,QAAA,CAC3C,qBAED,CAAY,CAAC,EACV,CAAC,cACNP,IAAA,CAAC7B,UAAU,EACTwQ,OAAO,CAAC,OAAO,CACf1L,EAAE,CAAE,CACFhC,KAAK,CAAE,gBAAgB,CACvBqO,EAAE,CAAE,CAAC,CACL8B,UAAU,CAAE,GAAG,CACfpO,QAAQ,CAAE,MACZ,CAAE,CAAAzC,QAAA,CACH,4EAGD,CAAY,CAAC,cACbP,IAAA,CAACtD,OAAO,EACNuG,EAAE,CAAE,CACF4N,WAAW,CAAE,0BAA0B,CACvCmB,WAAW,CAAE,KAAK,CAClBC,WAAW,CAAE,QACf,CAAE,CACH,CAAC,EACC,CAAC,cACNjS,IAAA,CAAC1E,IAAI,EAACwT,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAxO,QAAA,CACxBwG,UAAU,CAAC2J,GAAG,CAACwB,KAAA,EAAyB,IAAxB,CAAEvL,QAAQ,CAAE1F,KAAM,CAAC,CAAAiR,KAAA,CAClC,GAAI,CAAAtL,IAAI,CAAGyG,gBAAgB,CAAC1G,QAAQ,CAAC,CACrC,mBACE3G,IAAA,CAAC1E,IAAI,EACH2T,IAAI,MACJC,EAAE,CAAE,CAAE,CACNY,EAAE,CAAE,CAAE,CACNX,EAAE,CAAE,GAAI,CACRyB,EAAE,CAAE,GAAI,CACR3N,EAAE,CAAE,CACFmL,OAAO,CAAE,MAAM,CACfC,cAAc,CAAE,QAClB,CAAE,CAAA9N,QAAA,cAGFP,IAAA,CAACS,gBAAgB,EACf+F,KAAK,CAAEG,QAAS,CAChB1D,EAAE,CAAE,CACF,uBAAuB,CAAE,CACvBD,QAAQ,CAAE,MACZ,CACF,CAAE,CAAAzC,QAAA,cAEFL,KAAA,CAAC1E,MAAM,EACLmT,OAAO,CAAC,UAAU,CAClB1L,EAAE,CAAE,CACFhC,KAAK,CAAE,uBAAuB,CAC9B4P,WAAW,CAAE,uBAAuB,CACpCrB,MAAM,CAAE,MAAM,CACdsB,OAAO,CAAE,WAAW,CACpBvC,SAAS,CAAE,OAAO,CAClBuD,QAAQ,CAAE,OAAO,CACjBlC,SAAS,CAAE,cAAc3O,KAAK,IAAI,CAClCsM,UAAU,CACR,uCAAuC,CACzCa,OAAO,CAAE,MAAM,CACfI,aAAa,CAAE,QAAQ,CACvBC,GAAG,CAAE,CAAC,CACNhG,QAAQ,CAAE,UAAU,CACpBwI,QAAQ,CAAE,QAAQ,CAClB,WAAW,CAAE,CACXxD,OAAO,CAAE,IAAI,CACbhF,QAAQ,CAAE,UAAU,CACpBsH,GAAG,CAAE,CAAC,CACNmB,IAAI,CAAE,CAAC,CACPlB,KAAK,CAAE,CAAC,CACRR,MAAM,CAAE,KAAK,CACbE,UAAU,CAAE,0BAA0BzO,KAAK,KAAKA,KAAK,KAAK,CAC1D8M,OAAO,CAAE,CAAC,CACVR,UAAU,CAAE,mBACd,CAAC,CACD,SAAS,CAAE,CACTmC,UAAU,CAAE,2BAA2BzO,KAAK,OAAOA,KAAK,KAAK,CAC7D0O,MAAM,CAAE,aAAa1O,KAAK,IAAI,CAC9BwQ,SAAS,CACP,8BAA8B,CAChC7B,SAAS,CAAE,eAAe3O,KAAK,IAAI,CACnC,WAAW,CAAE,CACX8M,OAAO,CAAE,CACX,CACF,CACF,CAAE,CACFkC,OAAO,CAAEA,CAAA,GAAM,CACbzG,iBAAiB,CAAC,IAAI,CAAC,CACvB,GAAI7C,QAAQ,GAAK,OAAO,CAAE,CACxB8C,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CAAC,IAAM,IAAI9C,QAAQ,GAAK,OAAO,CAAE,CAC/B+C,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CAAC,IAAM,IAAI/C,QAAQ,GAAK,UAAU,CAAE,CAClCgD,qBAAqB,CAAC,IAAI,CAAC,CAC7B,CACF,CAAE,CAAApJ,QAAA,eAEFP,IAAA,CAAC9D,GAAG,EAAC+G,EAAE,CAAE,CAAED,QAAQ,CAAE,EAAE,CAAEsM,EAAE,CAAE,GAAI,CAAE,CAAA/O,QAAA,CAChCqG,IAAI,CACF,CAAC,cACN5G,IAAA,CAAC7B,UAAU,EACTwQ,OAAO,CAAC,SAAS,CACjB1L,EAAE,CAAE,CACFkO,UAAU,CAAE,GAAG,CACfnO,QAAQ,CAAE,MAAM,CAChBwO,aAAa,CAAE,MAAM,CACrBzD,OAAO,CAAE,GACX,CAAE,CAAAxN,QAAA,CAEDoG,QAAQ,CACC,CAAC,EACP,CAAC,CACO,CAAC,EA3EdA,QA4ED,CAAC,CAEX,CAAC,CAAC,CACE,CAAC,EACI,CAAC,EACV,CAAC,EACH,CAAC,cAEPzG,KAAA,CAACzE,MAAM,EACL0K,IAAI,CAAE6F,gBAAiB,CACvByE,OAAO,CAAE3E,qBAAsB,CAAAvL,QAAA,eAE/BP,IAAA,CAACtE,WAAW,EAACuF,KAAK,CAAC,SAAS,CAAAV,QAAA,CAAC,yBAE7B,CAAa,CAAC,cAEdP,IAAA,QACEuQ,KAAK,CAAE,CACLnC,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBD,cAAc,CAAE,QAClB,CAAE,CAAA9N,QAAA,cAEFL,KAAA,QAAAK,QAAA,eACEP,IAAA,CAACzE,MAAM,EACLgV,KAAK,CAAE,CACLhB,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MACV,CAAE,CACFuB,GAAG,CAAExL,aAAa,CAACE,IAAK,CACxB0M,GAAG,CAAC,oBAAoB,CACzB,CAAC,cACFnS,IAAA,CAACd,aAAa,EACZ8R,QAAQ,CAAEjF,2BAA4B,CACtCnD,KAAK,CACHrD,aAAa,CAACE,IAAI,GAAK,IAAI,EAC3BuC,gBAAgB,CAAC+B,KAClB,CACDqI,UAAU,CACR7M,aAAa,CAACE,IAAI,GAAK,IAAI,CACvB,mBAAmB,CACnB,EACL,CACF,CAAC,EACC,CAAC,CACH,CAAC,cAENvF,KAAA,CAACvE,aAAa,EAAA4E,QAAA,eACZP,IAAA,CAAC3E,SAAS,EACRgX,SAAS,MACT/B,MAAM,CAAC,OAAO,CACdrJ,IAAI,CAAC,OAAO,CACZJ,KAAK,CAAC,OAAO,CACbyL,IAAI,CAAC,MAAM,CACXf,SAAS,MACTgB,QAAQ,MACRtR,KAAK,CAAC,SAAS,CACfiG,KAAK,CAAE3B,aAAa,CAACF,KAAM,CAC3BwJ,QAAQ,CAAEhH,yBAA0B,CACpCe,KAAK,CACHrD,aAAa,CAACF,KAAK,CAACgC,IAAI,CAAC,CAAC,GAAK,EAAE,EACjCW,gBAAgB,CAACxB,KAClB,CACD4L,UAAU,CACR7M,aAAa,CAACF,KAAK,CAACgC,IAAI,CAAC,CAAC,GAAK,EAAE,CAC7B,mBAAmB,CACnB,EACL,CACF,CAAC,cAEFrH,IAAA,CAAC3E,SAAS,EACR4L,IAAI,CAAC,SAAS,CACdqJ,MAAM,CAAC,OAAO,CACdzJ,KAAK,CAAC,WAAW,CACjByL,IAAI,CAAC,KAAK,CACVf,SAAS,MACTgB,QAAQ,MACRrL,KAAK,CAAE3B,aAAa,CAACJ,OAAQ,CAC7B0J,QAAQ,CAAEhH,yBAA0B,CACpCe,KAAK,CACHrD,aAAa,CAACJ,OAAO,CAACkC,IAAI,CAAC,CAAC,GAAK,EAAE,EACnCW,gBAAgB,CAACyE,OAClB,CACD2F,UAAU,CACR7M,aAAa,CAACJ,OAAO,CAACkC,IAAI,CAAC,CAAC,GAAK,EAAE,CAC/B,iBAAiB,CACjB,EACL,CACF,CAAC,cAEFnH,KAAA,CAAChE,GAAG,EACF2T,EAAE,CAAE,CAAE,CACNR,CAAC,CAAE,CAAE,CACLpM,EAAE,CAAE,CACF9B,eAAe,CAAE,SAAS,CAC1BsO,YAAY,CAAE,KAChB,CAAE,CAAAlP,QAAA,eAEFP,IAAA,CAAC7B,UAAU,EAACwQ,OAAO,CAAC,WAAW,CAAC1N,KAAK,CAAC,aAAa,CAAAV,QAAA,CAAC,oCAEpD,CAAY,CAAC,cACbP,IAAA,CAAC7B,UAAU,EAACwQ,OAAO,CAAC,OAAO,CAAC1N,KAAK,CAAC,eAAe,CAAAV,QAAA,CAAC,2FAGlD,CAAY,CAAC,EACV,CAAC,EACO,CAAC,cAChBL,KAAA,CAACtE,aAAa,EAAA2E,QAAA,eACZP,IAAA,CAACxE,MAAM,EAACyU,OAAO,CAAEnE,qBAAsB,CAAAvL,QAAA,CAAC,QAAM,CAAQ,CAAC,cACvDP,IAAA,CAACxE,MAAM,EACLyU,OAAO,CAAEpE,oBAAqB,CAC9BwE,QAAQ,CAAE,CAAC3D,WAAY,CAAAnM,QAAA,CACxB,MAED,CAAQ,CAAC,EACI,CAAC,EACV,CAAC,cACTL,KAAA,CAACzE,MAAM,EAAC0K,IAAI,CAAEA,IAAK,CAACsK,OAAO,CAAEhF,WAAY,CAAAlL,QAAA,eACvCP,IAAA,CAACtE,WAAW,EAAA6E,QAAA,CAAC,yBAAuB,CAAa,CAAC,cAClDL,KAAA,CAACvE,aAAa,EAAA4E,QAAA,eACZP,IAAA,CAAC3E,SAAS,EACR4L,IAAI,CAAC,OAAO,CACZoL,SAAS,MACT/B,MAAM,CAAC,OAAO,CACdzJ,KAAK,CAAC,OAAO,CACbyL,IAAI,CAAC,KAAK,CACVf,SAAS,MACTgB,QAAQ,MACRrL,KAAK,CAAElC,aAAa,CAACK,KAAM,CAC3BwJ,QAAQ,CAAEjH,yBAA0B,CACpCwK,UAAU,CACRpN,aAAa,CAACK,KAAK,GAAK,EAAE,CAAG,mBAAmB,CAAG,EACpD,CACF,CAAC,cAEFrF,IAAA,CAAC3E,SAAS,EACR4L,IAAI,CAAC,SAAS,CACdqJ,MAAM,CAAC,OAAO,CACdlN,EAAE,CAAC,SAAS,CACZyD,KAAK,CAAC,KAAK,CACXyL,IAAI,CAAC,KAAK,CACVf,SAAS,MACTgB,QAAQ,MACRrL,KAAK,CAAElC,aAAa,CAACG,OAAQ,CAC7B0J,QAAQ,CAAEjH,yBAA0B,CACpCwK,UAAU,CACRpN,aAAa,CAACG,OAAO,GAAK,EAAE,CAAG,iBAAiB,CAAG,EACpD,CACF,CAAC,cAEFjF,KAAA,CAAChE,GAAG,EACF2T,EAAE,CAAE,CAAE,CACNR,CAAC,CAAE,CAAE,CACLpM,EAAE,CAAE,CACF9B,eAAe,CAAE,SAAS,CAC1BsO,YAAY,CAAE,KAChB,CAAE,CAAAlP,QAAA,eAEFP,IAAA,CAAC7B,UAAU,EAACwQ,OAAO,CAAC,WAAW,CAAC1N,KAAK,CAAC,aAAa,CAAAV,QAAA,CAAC,mCAEpD,CAAY,CAAC,cACbP,IAAA,CAAC7B,UAAU,EAACwQ,OAAO,CAAC,OAAO,CAAC1N,KAAK,CAAC,eAAe,CAAAV,QAAA,CAAC,uFAGlD,CAAY,CAAC,cACbP,IAAA,CAAC7B,UAAU,EAACwQ,OAAO,CAAC,OAAO,CAAC1N,KAAK,CAAC,eAAe,CAAAV,QAAA,CAAC,wHAIlD,CAAY,CAAC,cACbP,IAAA,CAAC7B,UAAU,EAACwQ,OAAO,CAAC,OAAO,CAAC1N,KAAK,CAAC,eAAe,CAAAV,QAAA,CAAC,gFAGlD,CAAY,CAAC,EACV,CAAC,EACO,CAAC,cAChBL,KAAA,CAACtE,aAAa,EAAA2E,QAAA,eACZP,IAAA,CAACxE,MAAM,EAACyU,OAAO,CAAExE,WAAY,CAAAlL,QAAA,CAAC,QAAM,CAAQ,CAAC,cAC7CP,IAAA,CAACxE,MAAM,EACLyU,OAAO,CAAEvE,UAAW,CACpB2E,QAAQ,CACNrL,aAAa,CAACK,KAAK,GAAK,EAAE,EAC1BL,aAAa,CAACG,OAAO,GAAK,EAC3B,CAAA5E,QAAA,CACF,MAED,CAAQ,CAAC,EACI,CAAC,EACV,CAAC,cACTP,IAAA,CAACZ,eAAe,EACdqK,kBAAkB,CAAEA,kBAAmB,CACvCyC,eAAe,CAAEA,eAAgB,CACjCpG,EAAE,CAAEtE,OAAO,CAAC4B,EAAG,CACfiJ,cAAc,CAAEA,cAAe,CAC/B5K,YAAY,CAAEA,YAAa,CAC3B+Q,mBAAmB,CAAEA,CAAA,GAAMhJ,iBAAiB,CAAC,IAAI,CAAE,CACpD,CAAC,cACFxJ,IAAA,CAACX,eAAe,EACdqK,kBAAkB,CAAEA,kBAAmB,CACvCyC,eAAe,CAAEA,eAAgB,CACjCrG,EAAE,CAAEtE,OAAO,CAAC4B,EAAG,CACfiJ,cAAc,CAAEA,cAAe,CAC/B5K,YAAY,CAAEA,YAAa,CAC3B+Q,mBAAmB,CAAEA,CAAA,GAAMhJ,iBAAiB,CAAC,IAAI,CAAE,CACpD,CAAC,cACFxJ,IAAA,CAACV,kBAAkB,EACjBqK,qBAAqB,CAAEA,qBAAsB,CAC7CyC,kBAAkB,CAAEA,kBAAmB,CACvCtG,EAAE,CAAEtE,OAAO,CAAC4B,EAAG,CACfiJ,cAAc,CAAEA,cAAe,CAC/B5K,YAAY,CAAEA,YAAa,CAC3B+Q,mBAAmB,CAAEA,CAAA,GAAMhJ,iBAAiB,CAAC,IAAI,CAAE,CACpD,CAAC,cACFxJ,IAAA,CAAC1E,IAAI,EAAC2T,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAAA5O,QAAA,cACxBL,KAAA,CAACnE,IAAI,EACHkH,EAAE,CAAE,CACFmL,OAAO,CAAEpM,SAAS,CAAG,MAAM,CAAG,MAAM,CACpCgN,SAAS,CAAE,MACb,CAAE,CAAAzO,QAAA,eAEFL,KAAA,CAAClE,WAAW,EAAAuE,QAAA,eACVP,IAAA,CAAC/D,UAAU,EACTgH,EAAE,CAAE,CACFwF,QAAQ,CAAE,UAAU,CACpBuH,KAAK,CAAE,CAAC,CACRD,GAAG,CAAE,CACP,CAAE,CACF,aAAW,OAAO,CAClBE,OAAO,CAAEA,CAAA,GAAM,CACbhO,YAAY,CAAC,KAAK,CAAC,CACnB0J,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAE,OAAO,CAAC,CACrD,CAAE,CAAArL,QAAA,cAEFP,IAAA,CAAC/C,SAAS,GAAE,CAAC,CACH,CAAC,cACb+C,IAAA,CAAC7B,UAAU,EAACsU,YAAY,MAAC9D,OAAO,CAAC,IAAI,CAAC+D,SAAS,CAAC,KAAK,CAAAnS,QAAA,CAAC,0BAEtD,CAAY,CAAC,cACbL,KAAA,CAAC5D,IAAI,EAAAiE,QAAA,eACHL,KAAA,CAAC3D,QAAQ,EAAAgE,QAAA,eACPP,IAAA,CAACxD,YAAY,EAAA+D,QAAA,cACXP,IAAA,CAAC1C,sBAAsB,EACrB2F,EAAE,CAAE,CACFD,QAAQ,CAAE,MACZ,CAAE,CACH,CAAC,CACU,CAAC,cACfhD,IAAA,CAACvD,YAAY,EAAA8D,QAAA,cACXP,IAAA,CAAC7B,UAAU,EACT8E,EAAE,CAAE,CACFD,QAAQ,CAAE,MACZ,CAAE,CAAAzC,QAAA,CACH,iDAED,CAAY,CAAC,CACD,CAAC,EACP,CAAC,cACXL,KAAA,CAAC3D,QAAQ,EAAAgE,QAAA,eACPP,IAAA,CAACxD,YAAY,EAAA+D,QAAA,cACXP,IAAA,CAAC1C,sBAAsB,EACrB2F,EAAE,CAAE,CACFD,QAAQ,CAAE,MACZ,CAAE,CACH,CAAC,CACU,CAAC,cACfhD,IAAA,CAACvD,YAAY,EAAA8D,QAAA,cACXP,IAAA,CAAC7B,UAAU,EACT8E,EAAE,CAAE,CACFD,QAAQ,CAAE,MACZ,CAAE,CAAAzC,QAAA,CACH,wCAED,CAAY,CAAC,CACD,CAAC,EACP,CAAC,cACXL,KAAA,CAAC3D,QAAQ,EAAAgE,QAAA,eACPP,IAAA,CAACxD,YAAY,EAAA+D,QAAA,cACXP,IAAA,CAAC1C,sBAAsB,EACrB2F,EAAE,CAAE,CACFD,QAAQ,CAAE,MACZ,CAAE,CACH,CAAC,CACU,CAAC,cACfhD,IAAA,CAACvD,YAAY,EAAA8D,QAAA,cACXP,IAAA,CAAC7B,UAAU,EACT8E,EAAE,CAAE,CACFD,QAAQ,CAAE,MACZ,CAAE,CAAAzC,QAAA,CACH,4CAED,CAAY,CAAC,CACD,CAAC,EACP,CAAC,cACXL,KAAA,CAAC3D,QAAQ,EAAAgE,QAAA,eACPP,IAAA,CAACxD,YAAY,EAAA+D,QAAA,cACXP,IAAA,CAAC1C,sBAAsB,EACrB2F,EAAE,CAAE,CACFD,QAAQ,CAAE,MACZ,CAAE,CACH,CAAC,CACU,CAAC,cACfhD,IAAA,CAACvD,YAAY,EAAA8D,QAAA,cACXP,IAAA,CAAC7B,UAAU,EACT8E,EAAE,CAAE,CACFD,QAAQ,CAAE,MACZ,CAAE,CAAAzC,QAAA,CACH,8CAED,CAAY,CAAC,CACD,CAAC,EACP,CAAC,EACP,CAAC,cACPP,IAAA,CAACxE,MAAM,EACLmT,OAAO,CAAC,WAAW,CACnBsB,OAAO,CAAEA,CAAA,GAAMzD,qBAAqB,CAAC,CAAE,CAAAjM,QAAA,CACxC,aAED,CAAQ,CAAC,EACE,CAAC,cACdP,IAAA,CAAC9D,GAAG,EACF+G,EAAE,CAAE,CACF0P,SAAS,CAAE,OAAO,CAClBpD,KAAK,CAAE,KAAK,CACZnB,OAAO,CAAE,CACPc,EAAE,CAAE,MAAM,CACVY,EAAE,CAAE,OACN,CACF,CAAE,CAAAvP,QAAA,cAEFP,IAAA,QAAK+Q,GAAG,CAAC,+aAA+a,CAAE,CAAC,CACxb,CAAC,EACF,CAAC,CACH,CAAC,EACH,CACP,EACG,CAAC,CACN,CAACnP,QAAQ,eACR1B,KAAA,CAAC5E,IAAI,EAAC2T,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAA5O,QAAA,EAEtBmB,SAAS,GAAK,CAAC,eACd1B,IAAA,CAAC9D,GAAG,EACF+G,EAAE,CAAE,CACF,mDAAmD,CAAE,CACnD2P,SAAS,CAAE,OAAO,CAClBrD,KAAK,CAAE,OAAO,CACdsD,SAAS,CAAE,MAAM,CACjBpK,QAAQ,CAAE,OAAO,CACjBuH,KAAK,CAAE,MAAM,CACbD,GAAG,CAAE,OAAO,CACZ2B,MAAM,CAAE,IACV,CAAC,CACD,2BAA2B,CAAE,CAC3BtD,OAAO,CAAE,MACX,CACF,CAAE,CAAA7N,QAAA,cAEFP,IAAA,CAAC3B,cAAc,EACbmI,KAAK,CAAC,SAAS,CACf2J,SAAS,CAAC,sBAAsB,CAChCvL,WAAW,CAAEA,WAAY,CACzBE,WAAW,CAAEA,WAAY,CACzB5B,IAAI,CAAEA,IAAK,CACXO,OAAO,CAAEA,OAAQ,CAClB,CAAC,CACC,CACN,CACA/B,SAAS,GAAK,CAAC,eACd1B,IAAA,CAAC9D,GAAG,EACF+G,EAAE,CAAE,CACF,mDAAmD,CAAE,CACnD2P,SAAS,CAAE,OAAO,CAClBrD,KAAK,CAAE,OAAO,CACdsD,SAAS,CAAE,MAAM,CACjBpK,QAAQ,CAAE,OAAO,CACjBuH,KAAK,CAAE,MAAM,CACbD,GAAG,CAAE,OAAO,CACZ2B,MAAM,CAAE,IACV,CAAC,CACD,2BAA2B,CAAE,CAC3BtD,OAAO,CAAE,MAAQ;AACnB,CACF,CAAE,CAAA7N,QAAA,cAEFL,KAAA,CAAChE,GAAG,EAAAqE,QAAA,eAEFP,IAAA,CAAC5B,iBAAiB,EAChBoI,KAAK,CAAC,cAAc,CACpB2J,SAAS,CAAC,4BAA4B,CACtCmC,IAAI,CAAC,aAAa,CAClBQ,IAAI,CAAElO,WAAW,CAAC8L,GAAG,CACnBqC,KAAA,EAAiD,IAAhD,CAAEvM,KAAK,CAAEiG,OAAO,CAAE1J,QAAQ,CAAEK,EAAE,CAAE4P,SAAU,CAAC,CAAAD,KAAA,CAC1C,GAAI,CAAAE,KAAK,CACT,GAAI,CAAAhS,KAAK,CACT,OAAQ8B,QAAQ,EACd,IAAK,SAAS,CACZkQ,KAAK,cAAGjT,IAAA,CAACtC,KAAK,GAAE,CAAC,CACjBuD,KAAK,CAAG,SAAS,CACjB,MACF,IAAK,QAAQ,CACXgS,KAAK,cAAGjT,IAAA,CAAC3C,UAAU,GAAE,CAAC,CACtB4D,KAAK,CAAG,SAAS,CACjB,MACF,IAAK,WAAW,CACdgS,KAAK,cAAGjT,IAAA,CAAC7C,aAAa,GAAE,CAAC,CACzB8D,KAAK,CAAG,SAAS,CACjB,MACF,IAAK,UAAU,CACbgS,KAAK,cAAGjT,IAAA,CAAC5C,YAAY,GAAE,CAAC,CACxB6D,KAAK,CAAG,SAAS,CACjB,MACF,IAAK,UAAU,CACbgS,KAAK,cAAGjT,IAAA,CAACzC,YAAY,GAAE,CAAC,CACxB0D,KAAK,CAAG,SAAS,CACjB,MACF,IAAK,QAAQ,CACXgS,KAAK,cAAGjT,IAAA,CAACK,UAAU,EAACuG,IAAI,CAAC,YAAY,CAAE,CAAC,CACxC3F,KAAK,CAAG,SAAS,CACjB,MACF,IAAK,aAAa,CAChBgS,KAAK,cAAGjT,IAAA,CAACxC,SAAS,GAAE,CAAC,CACrByD,KAAK,CAAG,SAAS,CACjB,MACF,QACEgS,KAAK,CAAG,IAAI,CACZhS,KAAK,CAAG,SAAS,CACrB,CACA,MAAO,CACL6E,EAAE,CAAE1C,EAAE,CACNiC,KAAK,CAAEmB,KAAK,CACZrB,OAAO,CAAEsH,OAAO,CAChBnH,KAAK,CAAErE,KAAK,CACZwE,IAAI,CAAEwN,KAAK,CACX/N,SAAS,CAAE8N,SAAS,CACpB5N,QAAQ,CAAErC,QACZ,CAAC,CACH,CACF,CAAE,CACFmQ,QAAQ,CAAEpI,sBAAuB,CACjCqI,MAAM,CAAEhJ,2BAA4B,CACrC,CAAC,cAEFnK,IAAA,CAAC5B,iBAAiB,EAChBoI,KAAK,CAAC,cAAc,CACpB2J,SAAS,CAAC,4BAA4B,CACtCmC,IAAI,CAAC,aAAa,CAClBQ,IAAI,CAAEhO,WAAW,CAAC4L,GAAG,CAAE1H,IAAI,EAAK,CAC9B,MAAO,CACLlD,EAAE,CAAEkD,IAAI,CAAC5F,EAAE,CACX8B,SAAS,CAAE8D,IAAI,CAACgK,SAAS,CACzB3N,KAAK,CAAE2D,IAAI,CAACxC,KAAK,CACjBrB,OAAO,CAAE6D,IAAI,CAACyD,OAAO,CACrBhH,IAAI,CAAEuD,IAAI,CAACpC,IACb,CAAC,CACH,CAAC,CAAE,CACHsM,QAAQ,CAAErI,sBAAuB,CACjCsI,MAAM,CAAEpK,2BAA4B,CACrC,CAAC,cAEF/I,IAAA,CAAC5B,iBAAiB,EAChBoI,KAAK,CAAC,eAAe,CACrB2J,SAAS,CAAC,6BAA6B,CACvCmC,IAAI,CAAC,cAAc,CACnBQ,IAAI,CACF,EAAAzR,iBAAA,CAAAG,OAAO,CAAC4R,QAAQ,UAAA/R,iBAAA,iBAAhBA,iBAAA,CACIgS,MAAM,CACL9J,OAAO,EACNA,OAAO,CAACxG,QAAQ,GAAK,OAAO,EAC5BwG,OAAO,CAACxG,QAAQ,GAAK,aAAa,EAClCwG,OAAO,CAACxG,QAAQ,GAAK,OAAO,EAC5BwG,OAAO,CAACxG,QAAQ,GAAK,OAAO,EAC5BwG,OAAO,CAACxG,QAAQ,GAAK,UACzB,CAAC,CACA2N,GAAG,CAAEnH,OAAO,EAAK,CAChB,GAAI,CAAA+J,SAAS,CACb,GAAI,CAAArS,KAAK,CACT,OAAQsI,OAAO,CAACxG,QAAQ,EACtB,IAAK,OAAO,CACZ,IAAK,aAAa,CAChBuQ,SAAS,CAAG,cAAc,CAC1BrS,KAAK,CAAG,SAAS,CACjB,MACF,IAAK,OAAO,CACZ,IAAK,OAAO,CACVqS,SAAS,CAAG,eAAe,CAC3BrS,KAAK,CAAG,SAAS,CACjB,MACF,IAAK,UAAU,CACbqS,SAAS,CAAG,iBAAiB,CAC7BrS,KAAK,CAAG,SAAS,CACjB,MACF,QACEqS,SAAS,CAAG,cAAc,CAC1BrS,KAAK,CAAG,SAAS,CACrB,CACA,MAAO,CACL6E,EAAE,CAAEyD,OAAO,CAACnG,EAAE,CACdiC,KAAK,CAAEkE,OAAO,CAAC/C,KAAK,EAAI+C,OAAO,CAACxG,QAAQ,CACxCoC,OAAO,CAAEoE,OAAO,CAACgK,WAAW,CAC5BjO,KAAK,CAAErE,KAAK,CACZwE,IAAI,CAAE6N,SAAS,CACflO,QAAQ,CAAEmE,OAAO,CAACxG,QACpB,CAAC,CACH,CAAC,CAAC,GAAI,EACT,CACDmQ,QAAQ,CAAEjK,mBAAoB,CAC9BkK,MAAM,CAAE7J,iBAAkB,CAC3B,CAAC,EACC,CAAC,CACH,CACN,EACG,CACP,CAEA1H,QAAQ,EAAI,CAACE,oBAAoB,eAChC9B,IAAA,CAACxE,MAAM,EACLgY,aAAa,MACbvS,KAAK,CAAC,SAAS,CACfgP,OAAO,CAAEA,CAAA,GAAMlO,uBAAuB,CAAE0R,IAAI,EAAK,CAACA,IAAI,CAAE,CACxD9E,OAAO,CAAC,WAAW,CACnB1L,EAAE,CAAE,CACFqN,MAAM,CAAE,iBAAiB,CACzBoB,MAAM,CAAE,IAAI,CACZjJ,QAAQ,CAAE,OAAO,CACjBuH,KAAK,CAAE,MAAM,CACb0D,MAAM,CAAE,MAAM,CACdjE,YAAY,CAAE,KAAK,CACnBD,MAAM,CAAE,MAAM,CACdsC,QAAQ,CAAE,KACZ,CAAE,CAAAvR,QAAA,cAEFP,IAAA,CAACvC,eAAe,GAAE,CAAC,CACb,CACT,cAEDuC,IAAA,CAACvE,MAAM,EAAC0K,IAAI,CAAErE,oBAAqB,CAAC6R,UAAU,MAAApT,QAAA,cAC5CL,KAAA,CAACvE,aAAa,EAAA4E,QAAA,eAEZP,IAAA,CAAC/D,UAAU,EACTgH,EAAE,CAAE,CACFwF,QAAQ,CAAE,OAAO,CACjBsH,GAAG,CAAE,EAAE,CACPC,KAAK,CAAE,EAAE,CACT0B,MAAM,CAAE,IAAI,CACZvQ,eAAe,CAAE,0BAA0B,CAC3C,SAAS,CAAE,CACTA,eAAe,CAAE,wBACnB,CACF,CAAE,CACF8O,OAAO,CAAEA,CAAA,GAAMlO,uBAAuB,CAAC,KAAK,CAAE,CAC9C,aAAW,OAAO,CAAAxB,QAAA,cAElBP,IAAA,CAAC/C,SAAS,GAAE,CAAC,CACH,CAAC,cACb+C,IAAA,CAAC3B,cAAc,EACbmI,KAAK,CAAC,SAAS,CACf2J,SAAS,CAAC,sBAAsB,CAChCvL,WAAW,CAAEA,WAAY,CACzBE,WAAW,CAAEA,WAAY,CACzB5B,IAAI,CAAEA,IAAK,CACXO,OAAO,CAAEA,OAAQ,CAClB,CAAC,cACFvD,KAAA,CAAChE,GAAG,EAAAqE,QAAA,eAEFP,IAAA,CAAC5B,iBAAiB,EAChBoI,KAAK,CAAC,cAAc,CACpB2J,SAAS,CAAC,4BAA4B,CACtCmC,IAAI,CAAC,aAAa,CAClBQ,IAAI,CAAElO,WAAW,CAAC8L,GAAG,CACnBkD,KAAA,EAAiD,IAAhD,CAAEpN,KAAK,CAAEiG,OAAO,CAAE1J,QAAQ,CAAEK,EAAE,CAAE4P,SAAU,CAAC,CAAAY,KAAA,CAC1C,GAAI,CAAAX,KAAK,CACT,GAAI,CAAAhS,KAAK,CACT,OAAQ8B,QAAQ,EACd,IAAK,SAAS,CACZkQ,KAAK,cAAGjT,IAAA,CAACtC,KAAK,GAAE,CAAC,CACjBuD,KAAK,CAAG,SAAS,CACjB,MACF,IAAK,QAAQ,CACb,IAAK,OAAO,CACVgS,KAAK,cAAGjT,IAAA,CAAC3C,UAAU,GAAE,CAAC,CACtB4D,KAAK,CAAG,SAAS,CACjB,MACF,IAAK,WAAW,CACdgS,KAAK,cAAGjT,IAAA,CAAC7C,aAAa,GAAE,CAAC,CACzB8D,KAAK,CAAG,SAAS,CACjB,MACF,IAAK,UAAU,CACbgS,KAAK,cAAGjT,IAAA,CAAC5C,YAAY,GAAE,CAAC,CACxB6D,KAAK,CAAG,SAAS,CACjB,MACF,IAAK,UAAU,CACbgS,KAAK,cAAGjT,IAAA,CAACzC,YAAY,GAAE,CAAC,CACxB0D,KAAK,CAAG,SAAS,CACjB,MACF,IAAK,QAAQ,CACXgS,KAAK,cAAGjT,IAAA,CAACJ,OAAO,EAACgH,IAAI,CAAC,YAAY,CAAE,CAAC,CACrC3F,KAAK,CAAG,SAAS,CACjB,MACF,QACEgS,KAAK,CAAG,IAAI,CACZhS,KAAK,CAAG,SAAS,CACrB,CACA,MAAO,CACL6E,EAAE,CAAE1C,EAAE,CACNiC,KAAK,CAAEmB,KAAK,CACZrB,OAAO,CAAEsH,OAAO,CAChBnH,KAAK,CAAErE,KAAK,CACZwE,IAAI,CAAEwN,KAAK,CACX/N,SAAS,CAAE8N,SAAS,CACpB5N,QAAQ,CAAErC,QACZ,CAAC,CACH,CACF,CAAE,CACFmQ,QAAQ,CAAEpI,sBAAuB,CACjCqI,MAAM,CAAEhJ,2BAA4B,CACpC0J,kBAAkB,CAAE9R,uBAAwB,CAC7C,CAAC,cAEF/B,IAAA,CAAC5B,iBAAiB,EAChBoI,KAAK,CAAC,cAAc,CACpB2J,SAAS,CAAC,4BAA4B,CACtCmC,IAAI,CAAC,aAAa,CAClBQ,IAAI,CAAEhO,WAAW,CAAC4L,GAAG,CAAE1H,IAAI,EAAK,CAC9B,MAAO,CACLlD,EAAE,CAAEkD,IAAI,CAAC5F,EAAE,CACX8B,SAAS,CAAE8D,IAAI,CAACgK,SAAS,CACzB3N,KAAK,CAAE2D,IAAI,CAACxC,KAAK,CACjBrB,OAAO,CAAE6D,IAAI,CAACyD,OAAO,CACrBhH,IAAI,CAAEuD,IAAI,CAACpC,IACb,CAAC,CACH,CAAC,CAAE,CACHsM,QAAQ,CAAErI,sBAAuB,CACjCsI,MAAM,CAAEpK,2BAA4B,CACpC8K,kBAAkB,CAAE9R,uBAAwB,CAC7C,CAAC,cAEF/B,IAAA,CAAC5B,iBAAiB,EAChBoI,KAAK,CAAC,eAAe,CACrB2J,SAAS,CAAC,6BAA6B,CACvCmC,IAAI,CAAC,cAAc,CACnBQ,IAAI,CACF,EAAAxR,kBAAA,CAAAE,OAAO,CAAC4R,QAAQ,UAAA9R,kBAAA,iBAAhBA,kBAAA,CACI+R,MAAM,CACL9J,OAAO,EACNA,OAAO,CAACxG,QAAQ,GAAK,OAAO,EAC5BwG,OAAO,CAACxG,QAAQ,GAAK,aAAa,EAClCwG,OAAO,CAACxG,QAAQ,GAAK,OAAO,EAC5BwG,OAAO,CAACxG,QAAQ,GAAK,OAAO,EAC5BwG,OAAO,CAACxG,QAAQ,GAAK,UACzB,CAAC,CACA2N,GAAG,CAAEnH,OAAO,EAAK,CAChB,GAAI,CAAA+J,SAAS,CACb,GAAI,CAAArS,KAAK,CACT,OAAQsI,OAAO,CAACxG,QAAQ,EACtB,IAAK,OAAO,CACZ,IAAK,aAAa,CAChBuQ,SAAS,CAAG,cAAc,CAC1BrS,KAAK,CAAG,SAAS,CACjB,MACF,IAAK,OAAO,CACZ,IAAK,OAAO,CACVqS,SAAS,CAAG,eAAe,CAC3BrS,KAAK,CAAG,SAAS,CACjB,MACF,IAAK,UAAU,CACbqS,SAAS,CAAG,iBAAiB,CAC7BrS,KAAK,CAAG,SAAS,CACjB,MACF,QACEqS,SAAS,CAAG,cAAc,CAC1BrS,KAAK,CAAG,SAAS,CACrB,CACA,MAAO,CACL6E,EAAE,CAAEyD,OAAO,CAACnG,EAAE,CACdiC,KAAK,CAAEkE,OAAO,CAAC/C,KAAK,EAAI+C,OAAO,CAACxG,QAAQ,CACxCoC,OAAO,CAAEoE,OAAO,CAACgK,WAAW,CAC5BjO,KAAK,CAAErE,KAAK,CACZwE,IAAI,CAAE6N,SAAS,CACflO,QAAQ,CAAEmE,OAAO,CAACxG,QAAQ,CAC1B+Q,WAAW,CAAEvK,OAAO,CAACgK,WACvB,CAAC,CACH,CAAC,CAAC,GAAI,EACT,CACDL,QAAQ,CAAEjK,mBAAoB,CAC9BkK,MAAM,CAAE7J,iBAAkB,CAC1BuK,kBAAkB,CAAE9R,uBAAwB,CAC7C,CAAC,EACC,CAAC,EACO,CAAC,CACV,CAAC,EACL,CAAC,CAGNL,SAAS,GAAK,CAAC,eACd1B,IAAA,CAAC9D,GAAG,EACF+G,EAAE,CAAE,CACF,2BAA2B,CAAE,CAC3BmL,OAAO,CAAE,OAAO,CAChByB,EAAE,CAAE,CAAC,CACLP,EAAE,CAAE,CACN,CAAC,CACD,2BAA2B,CAAE,CAC3BlB,OAAO,CAAE,MACX,CACF,CAAE,CAAA7N,QAAA,cAEFP,IAAA,CAAC1E,IAAI,EAACwT,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAxO,QAAA,cACzBP,IAAA,CAAC1E,IAAI,EAAC2T,IAAI,MAACC,EAAE,CAAE,EAAG,CAAA3O,QAAA,cAChBL,KAAA,CAAChE,GAAG,EAAAqE,QAAA,eAEFP,IAAA,CAAC5B,iBAAiB,EAChBoI,KAAK,CAAC,cAAc,CACpB2J,SAAS,CAAC,4BAA4B,CACtCmC,IAAI,CAAC,aAAa,CAClBQ,IAAI,CAAElO,WAAW,CAAC8L,GAAG,CACnBqD,KAAA,EAAiD,IAAhD,CAAEvN,KAAK,CAAEiG,OAAO,CAAE1J,QAAQ,CAAEK,EAAE,CAAE4P,SAAU,CAAC,CAAAe,KAAA,CAC1C,GAAI,CAAAd,KAAK,CACT,GAAI,CAAAhS,KAAK,CACT,OAAQ8B,QAAQ,EACd,IAAK,SAAS,CACZkQ,KAAK,cAAGjT,IAAA,CAACtC,KAAK,GAAE,CAAC,CACjBuD,KAAK,CAAG,SAAS,CACjB,MACF,IAAK,WAAW,CACdgS,KAAK,cAAGjT,IAAA,CAAC7C,aAAa,GAAE,CAAC,CACzB8D,KAAK,CAAG,SAAS,CACjB,MACF,IAAK,UAAU,CACbgS,KAAK,cAAGjT,IAAA,CAAC5C,YAAY,GAAE,CAAC,CACxB6D,KAAK,CAAG,SAAS,CACjB,MACF,IAAK,UAAU,CACbgS,KAAK,cAAGjT,IAAA,CAACzC,YAAY,GAAE,CAAC,CACxB0D,KAAK,CAAG,SAAS,CACjB,MACF,IAAK,QAAQ,CACXgS,KAAK,cAAGjT,IAAA,CAACJ,OAAO,EAACgH,IAAI,CAAC,YAAY,CAAE,CAAC,CACrC3F,KAAK,CAAG,SAAS,CACjB,MACF,IAAK,SAAS,CACZgS,KAAK,cAAGjT,IAAA,CAACnC,WAAW,GAAE,CAAC,CACvBoD,KAAK,CAAG,SAAS,CACjB,MACF,IAAK,QAAQ,CACXgS,KAAK,cAAGjT,IAAA,CAACJ,OAAO,EAACgH,IAAI,CAAC,YAAY,CAAE,CAAC,CACrC3F,KAAK,CAAG,SAAS,CACjB,MACF,IAAK,UAAU,CACbgS,KAAK,cAAGjT,IAAA,CAAC9B,YAAY,GAAE,CAAC,CACxB+C,KAAK,CAAG,SAAS,CACjB,MACF,IAAK,WAAW,CACdgS,KAAK,cAAGjT,IAAA,CAAClC,aAAa,GAAE,CAAC,CACzBmD,KAAK,CAAG,SAAS,CACjB,MACF,IAAK,QAAQ,CACXgS,KAAK,cAAGjT,IAAA,CAACjC,UAAU,GAAE,CAAC,CACtBkD,KAAK,CAAG,SAAS,CACjB,MACF,IAAK,QAAQ,CACXgS,KAAK,cAAGjT,IAAA,CAAC9B,YAAY,GAAE,CAAC,CACxB+C,KAAK,CAAG,SAAS,CACjB,MACF,IAAK,SAAS,CACZgS,KAAK,cAAGjT,IAAA,CAAC9B,YAAY,GAAE,CAAC,CACxB+C,KAAK,CAAG,SAAS,CACjB,MACF,IAAK,UAAU,CACbgS,KAAK,cAAGjT,IAAA,CAAChC,YAAY,GAAE,CAAC,CACxBiD,KAAK,CAAG,SAAS,CACjB,MACF,IAAK,UAAU,CACbgS,KAAK,cAAGjT,IAAA,CAACpC,YAAY,GAAE,CAAC,CACxBqD,KAAK,CAAG,SAAS,CACjB,MACF,IAAK,SAAS,CACZgS,KAAK,cAAGjT,IAAA,CAAC/B,WAAW,GAAE,CAAC,CACvBgD,KAAK,CAAG,SAAS,CACjB,MACF,IAAK,YAAY,CACfgS,KAAK,cAAGjT,IAAA,CAAC/B,WAAW,GAAE,CAAC,CACvBgD,KAAK,CAAG,SAAS,CACjB,MACF,IAAK,SAAS,CACZgS,KAAK,cAAGjT,IAAA,CAAC9B,YAAY,GAAE,CAAC,CACxB+C,KAAK,CAAG,SAAS,CACjB,MACF,IAAK,UAAU,CACbgS,KAAK,cAAGjT,IAAA,CAAC9B,YAAY,GAAE,CAAC,CACxB+C,KAAK,CAAG,SAAS,CACjB,MACF,IAAK,QAAQ,CACXgS,KAAK,cAAGjT,IAAA,CAAC3C,UAAU,GAAE,CAAC,CACtB4D,KAAK,CAAG,MAAM,CACd,MACF,IAAK,SAAS,CACZgS,KAAK,cAAGjT,IAAA,CAAC9B,YAAY,GAAE,CAAC,CACxB+C,KAAK,CAAG,SAAS,CACjB,MACF,QACEgS,KAAK,cAAGjT,IAAA,CAAC9B,YAAY,GAAE,CAAC,CACxB+C,KAAK,CAAG,SAAS,CACrB,CACA,MAAO,CACL6E,EAAE,CAAE1C,EAAE,CACNiC,KAAK,CAAEmB,KAAK,CACZrB,OAAO,CAAEsH,OAAO,CAChBnH,KAAK,CAAErE,KAAK,CACZwE,IAAI,CAAEwN,KAAK,CACX7N,QAAQ,CAAErC,QACZ,CAAC,CACH,CACF,CAAE,CACFmQ,QAAQ,CAAEpI,sBAAuB,CACjCqI,MAAM,CAAEhJ,2BAA4B,CACpC0J,kBAAkB,CAAE9R,uBAAwB,CAC7C,CAAC,cAGF/B,IAAA,CAAC5B,iBAAiB,EAChBoI,KAAK,CAAC,eAAe,CACrB2J,SAAS,CAAC,6BAA6B,CACvCmC,IAAI,CAAC,cAAc,CACnBQ,IAAI,CACF,EAAAvR,kBAAA,CAAAC,OAAO,CAAC4R,QAAQ,UAAA7R,kBAAA,iBAAhBA,kBAAA,CACI8R,MAAM,CAAE9J,OAAO,EACf,CACE,OAAO,CACP,OAAO,CACP,UAAU,CACV,aAAa,CACd,CAACyK,QAAQ,CAACzK,OAAO,CAACxG,QAAQ,CAC7B,CAAC,CACA2N,GAAG,CAAEnH,OAAO,EAAK,CAChB,GAAI,CAAA+J,SAAS,CACb,GAAI,CAAArS,KAAK,CACT,OAAQsI,OAAO,CAACxG,QAAQ,EACtB,IAAK,OAAO,CACZ,IAAK,OAAO,CACVuQ,SAAS,cAAGtT,IAAA,CAACrC,SAAS,GAAE,CAAC,CACzBsD,KAAK,CAAG,SAAS,CACjB,MACF,IAAK,UAAU,CACbqS,SAAS,cAAGtT,IAAA,CAACpC,YAAY,GAAE,CAAC,CAC5BqD,KAAK,CAAG,SAAS,CACjB,MACF,IAAK,aAAa,CAChBqS,SAAS,cAAGtT,IAAA,CAACxC,SAAS,GAAE,CAAC,CACzByD,KAAK,CAAG,SAAS,CACjB,MACF,QACEqS,SAAS,cAAGtT,IAAA,CAACxC,SAAS,GAAE,CAAC,CACzByD,KAAK,CAAG,SAAS,CACrB,CACA,MAAO,CACL6E,EAAE,CAAEyD,OAAO,CAACnG,EAAE,CACdiC,KAAK,CAAEkE,OAAO,CAAC/C,KAAK,EAAI+C,OAAO,CAACxG,QAAQ,CACxCoC,OAAO,CAAEoE,OAAO,CAACgK,WAAW,CAC5BjO,KAAK,CAAErE,KAAK,CACZwE,IAAI,CAAE6N,SAAS,CACflO,QAAQ,CAAEmE,OAAO,CAACxG,QACpB,CAAC,CACH,CAAC,CAAC,GAAI,EACT,CACDmQ,QAAQ,CAAEjK,mBAAoB,CAC9BkK,MAAM,CAAE7J,iBAAkB,CAC1BuK,kBAAkB,CAAE9R,uBAAwB,CAC7C,CAAC,EACC,CAAC,CACF,CAAC,CACH,CAAC,CACJ,CACN,EACD,CACH,EACQ,CAAC,CAEhB,CAAC,CAED,cAAe,CAAAX,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module"}