{"ast": null, "code": "import api from\"./Api\";import{BASE_URL}from\"./Context/config\";export const GetProfileFromSearch=async search=>{console.log(search);try{const response=await api.get(`${BASE_URL}/Search/Search`,{params:{search:search},headers:{\"Cache-Control\":\"no-cache\",Pragma:\"no-cache\",withCredentials:true}});return response;}catch(error){console.log(\"Error:\",error.message);}};export const PostSearch=async search=>{const data={UserId:search.UserId,Query:search.Query,Date:search.Date};try{var authToken=getAuthToken();const response=await api.post(`${BASE_URL}/Search/PostSearch`,data,{headers:{Authorization:`Bearer ${authToken}`,\"Content-Type\":\"application/json\"}});return response;}catch(error){return{error:error.message};}};export const GetSearchQueries=async()=>{try{var authToken=getAuthToken();const response=await api.get(`${BASE_URL}/Search/GetSearchQueries`,{headers:{Authorization:`Bearer ${authToken}`,\"Content-Type\":\"application/json\"}});return response;}catch(error){return{error:error.message};}};export function getAuthToken(){const cookies=document.cookie.split(\";\");for(let i=0;i<cookies.length;i++){const cookie=cookies[i].trim();if(cookie.startsWith(\"authToken=\")){return cookie.substring(\"authToken=\".length,cookie.length);}}return null;}", "map": {"version": 3, "names": ["api", "BASE_URL", "GetProfileFromSearch", "search", "console", "log", "response", "get", "params", "headers", "Pragma", "withCredentials", "error", "message", "PostSearch", "data", "UserId", "Query", "Date", "authToken", "getAuthToken", "post", "Authorization", "GetSearchQueries", "cookies", "document", "cookie", "split", "i", "length", "trim", "startsWith", "substring"], "sources": ["C:/Users/<USER>/Desktop/iDigitalX/ClientApp/src/SearchData.ts"], "sourcesContent": ["import api from \"./Api\";\r\nimport { BASE_URL } from \"./Context/config\";\r\n\r\nexport interface PostSearch {\r\n  UserId: number;\r\n  Query: string;\r\n  Date: string;\r\n}\r\n\r\nexport const GetProfileFromSearch = async (search: string) => {\r\n  console.log(search);\r\n  try {\r\n    const response = await api.get(`${BASE_URL}/Search/Search`, {\r\n      params: {\r\n        search: search,\r\n      },\r\n      headers: {\r\n        \"Cache-Control\": \"no-cache\",\r\n        Pragma: \"no-cache\",\r\n        withCredentials: true,\r\n      },\r\n    });\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.log(\"Error:\", error.message);\r\n  }\r\n};\r\n\r\nexport const PostSearch = async (search: PostSearch) => {\r\n  const data = {\r\n    UserId: search.UserId,\r\n    Query: search.Query,\r\n    Date: search.Date,\r\n  };\r\n\r\n  try {\r\n    var authToken = getAuthToken();\r\n    const response = await api.post(`${BASE_URL}/Search/PostSearch`, data, {\r\n      headers: {\r\n        Authorization: `Bearer ${authToken}`,\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n    return response;\r\n  } catch (error) {\r\n    return { error: error.message };\r\n  }\r\n};\r\n\r\nexport const GetSearchQueries = async () => {\r\n  try {\r\n    var authToken = getAuthToken();\r\n    const response = await api.get(`${BASE_URL}/Search/GetSearchQueries`, {\r\n      headers: {\r\n        Authorization: `Bearer ${authToken}`,\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response;\r\n  } catch (error) {\r\n    return { error: error.message };\r\n  }\r\n};\r\n\r\nexport function getAuthToken() {\r\n  const cookies = document.cookie.split(\";\");\r\n\r\n  for (let i = 0; i < cookies.length; i++) {\r\n    const cookie = cookies[i].trim();\r\n    if (cookie.startsWith(\"authToken=\")) {\r\n      return cookie.substring(\"authToken=\".length, cookie.length);\r\n    }\r\n  }\r\n\r\n  return null;\r\n}\r\n"], "mappings": "AAAA,MAAO,CAAAA,GAAG,KAAM,OAAO,CACvB,OAASC,QAAQ,KAAQ,kBAAkB,CAQ3C,MAAO,MAAM,CAAAC,oBAAoB,CAAG,KAAO,CAAAC,MAAc,EAAK,CAC5DC,OAAO,CAACC,GAAG,CAACF,MAAM,CAAC,CACnB,GAAI,CACF,KAAM,CAAAG,QAAQ,CAAG,KAAM,CAAAN,GAAG,CAACO,GAAG,CAAC,GAAGN,QAAQ,gBAAgB,CAAE,CAC1DO,MAAM,CAAE,CACNL,MAAM,CAAEA,MACV,CAAC,CACDM,OAAO,CAAE,CACP,eAAe,CAAE,UAAU,CAC3BC,MAAM,CAAE,UAAU,CAClBC,eAAe,CAAE,IACnB,CACF,CAAC,CAAC,CAEF,MAAO,CAAAL,QAAQ,CACjB,CAAE,MAAOM,KAAK,CAAE,CACdR,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAEO,KAAK,CAACC,OAAO,CAAC,CACtC,CACF,CAAC,CAED,MAAO,MAAM,CAAAC,UAAU,CAAG,KAAO,CAAAX,MAAkB,EAAK,CACtD,KAAM,CAAAY,IAAI,CAAG,CACXC,MAAM,CAAEb,MAAM,CAACa,MAAM,CACrBC,KAAK,CAAEd,MAAM,CAACc,KAAK,CACnBC,IAAI,CAAEf,MAAM,CAACe,IACf,CAAC,CAED,GAAI,CACF,GAAI,CAAAC,SAAS,CAAGC,YAAY,CAAC,CAAC,CAC9B,KAAM,CAAAd,QAAQ,CAAG,KAAM,CAAAN,GAAG,CAACqB,IAAI,CAAC,GAAGpB,QAAQ,oBAAoB,CAAEc,IAAI,CAAE,CACrEN,OAAO,CAAE,CACPa,aAAa,CAAE,UAAUH,SAAS,EAAE,CACpC,cAAc,CAAE,kBAClB,CACF,CAAC,CAAC,CACF,MAAO,CAAAb,QAAQ,CACjB,CAAE,MAAOM,KAAK,CAAE,CACd,MAAO,CAAEA,KAAK,CAAEA,KAAK,CAACC,OAAQ,CAAC,CACjC,CACF,CAAC,CAED,MAAO,MAAM,CAAAU,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CAC1C,GAAI,CACF,GAAI,CAAAJ,SAAS,CAAGC,YAAY,CAAC,CAAC,CAC9B,KAAM,CAAAd,QAAQ,CAAG,KAAM,CAAAN,GAAG,CAACO,GAAG,CAAC,GAAGN,QAAQ,0BAA0B,CAAE,CACpEQ,OAAO,CAAE,CACPa,aAAa,CAAE,UAAUH,SAAS,EAAE,CACpC,cAAc,CAAE,kBAClB,CACF,CAAC,CAAC,CAEF,MAAO,CAAAb,QAAQ,CACjB,CAAE,MAAOM,KAAK,CAAE,CACd,MAAO,CAAEA,KAAK,CAAEA,KAAK,CAACC,OAAQ,CAAC,CACjC,CACF,CAAC,CAED,MAAO,SAAS,CAAAO,YAAYA,CAAA,CAAG,CAC7B,KAAM,CAAAI,OAAO,CAAGC,QAAQ,CAACC,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAE1C,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGJ,OAAO,CAACK,MAAM,CAAED,CAAC,EAAE,CAAE,CACvC,KAAM,CAAAF,MAAM,CAAGF,OAAO,CAACI,CAAC,CAAC,CAACE,IAAI,CAAC,CAAC,CAChC,GAAIJ,MAAM,CAACK,UAAU,CAAC,YAAY,CAAC,CAAE,CACnC,MAAO,CAAAL,MAAM,CAACM,SAAS,CAAC,YAAY,CAACH,MAAM,CAAEH,MAAM,CAACG,MAAM,CAAC,CAC7D,CACF,CAEA,MAAO,KAAI,CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module"}