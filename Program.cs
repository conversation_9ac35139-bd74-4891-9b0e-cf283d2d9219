using Microsoft.EntityFrameworkCore;
using idigix.Models;
using idigix.Services;
using idigix.Services.interfaces;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using System.Net;
using System.Text.Json.Serialization;
using AspNetCoreRateLimit;
using System.IO.Compression;
using Microsoft.AspNetCore.ResponseCompression;

var builder = WebApplication.CreateBuilder(args);

builder.Services.AddMemoryCache();
builder.Services.Configure<IpRateLimitOptions>(builder.Configuration.GetSection("IpRateLimiting"));
builder.Services.Configure<IpRateLimitPolicies>(builder.Configuration.GetSection("IpRateLimitPolicies"));
builder.Services.AddSingleton<IIpPolicyStore, MemoryCacheIpPolicyStore>();
builder.Services.AddSingleton<IRateLimitCounterStore, MemoryCacheRateLimitCounterStore>();
builder.Services.AddSingleton<IProcessingStrategy, AsyncKeyLockProcessingStrategy>();
builder.Services.AddSingleton<IRateLimitConfiguration, RateLimitConfiguration>();
builder.Services.AddTransient<IEmailSender, EmailSender>();
builder.Services.AddScoped<IEmailTemplateService, EmailTemplateService>();
builder.Services.AddScoped<ICouponService, CouponService>();
builder.Services.AddScoped<IEncryptionService, EncryptionService>();
builder.Services.AddScoped<ITokenService, TokenService>();

var HOST = builder.Configuration["Host"];

builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowSpecificOrigins",
        builder =>
        {
            builder.WithOrigins("http://************:3000","https://idigics.com", "https://www.idigics.com", "http://localhost:3000", "https://localhost:3000")
                   .AllowAnyMethod()
                   .AllowAnyHeader();
        });
});


builder.Services.AddSession(options =>
{
    options.IdleTimeout = TimeSpan.FromSeconds(10);
    options.Cookie.HttpOnly = true;
    options.Cookie.IsEssential = true;
});

builder.WebHost.UseKestrel(options =>
{
    options.Listen(IPAddress.Any, 7120);
});

builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
}).AddJwtBearer(o =>
{
    o.TokenValidationParameters = new TokenValidationParameters
    {
        ValidIssuer = builder.Configuration["JwtSettings:Issuer"],
        ValidAudience = builder.Configuration["JwtSettings:Audience"],
        IssuerSigningKey = new SymmetricSecurityKey
        (Encoding.UTF8.GetBytes(builder.Configuration["JwtSettings:Secret"])),
        ValidateIssuer = true,
        ValidateAudience = true,
        ValidateLifetime = true,
        ValidateIssuerSigningKey = true
    };
});

builder.Services.AddResponseCaching(options =>
    {
        options.MaximumBodySize = 1024;
    });

builder.Services.AddAuthorization();

builder.Services.AddResponseCompression(options =>
{
    options.EnableForHttps = true;
    options.Providers.Add<BrotliCompressionProvider>();
    options.Providers.Add<GzipCompressionProvider>();
});

builder.Services.Configure<BrotliCompressionProviderOptions>(options =>
{
    options.Level = CompressionLevel.Fastest;
});

builder.Services.Configure<GzipCompressionProviderOptions>(options =>
{
    options.Level = CompressionLevel.SmallestSize;
});

builder.Services.AddControllersWithViews();
builder.Services.AddDbContext<dbContext>(options => options.UseSqlite(builder.Configuration.GetConnectionString("DefaultConnection")));
builder.Services.AddControllers().AddJsonOptions(x =>
                x.JsonSerializerOptions.ReferenceHandler = ReferenceHandler.IgnoreCycles);

builder.Host.ConfigureHostOptions(o => o.ShutdownTimeout = TimeSpan.FromSeconds(30));

// Build the application
var app = builder.Build();

// Enable HSTS (HTTP Strict Transport Security) only in non-development environments
if (!app.Environment.IsDevelopment())
{
    app.UseHsts(); // Helps in enforcing HTTPS usage for improved security
}

// Enable response compression to reduce bandwidth usage and improve performance
app.UseResponseCompression();

// Custom middleware for logging request processing time and IP address
app.Use(async (ctx, next) =>
{
    var start = DateTime.UtcNow; // Record the start time of request processing
    string ipAddress = ctx.Connection.RemoteIpAddress?.ToString(); // Retrieve client's IP address
    await next.Invoke(ctx); // Invoke the next middleware in the pipeline
    // Log request details including IP address, requested path, and processing time
    app.Logger.LogInformation($"IP address: {ipAddress} {ctx.Request.Path} : {(DateTime.UtcNow - start).TotalMilliseconds}");
});


// Enable IP rate limiting for preventing abuse and protecting against DDoS attacks
app.UseIpRateLimiting();

// Enable CORS (Cross-Origin Resource Sharing) to allow requests from React client
app.UseCors("AllowSpecificOrigins");

// Configure cookie policy for setting SameSite and Secure attributes
app.UseCookiePolicy(new CookiePolicyOptions
{
    MinimumSameSitePolicy = SameSiteMode.None, // Allows cross-site cookies
    Secure = CookieSecurePolicy.Always // Ensures cookies are only sent over HTTPS
});

// Serve static files (e.g., CSS, JavaScript, images)
app.UseStaticFiles();

// Enable endpoint routing
app.UseRouting();

// Authenticate users
app.UseAuthentication();

// Authorize access to resources based on user roles and policies
app.UseAuthorization();

// Enable response caching to cache responses for improved performance
app.UseResponseCaching();

// Map API controllers first
app.MapControllers();

// Map controller routes for handling HTTP requests
app.MapControllerRoute(
    name: "default",
    pattern: "{controller}/{action=Index}/{id?}");

// Serve a default file (e.g., index.html) for fallback requests
app.MapFallbackToFile("index.html");

// Run the application
app.Run();


