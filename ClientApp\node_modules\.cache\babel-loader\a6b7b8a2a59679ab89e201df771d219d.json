{"ast": null, "code": "import{useState,useEffect}from\"react\";import{TextField,Button,Dialog,DialogTitle,DialogContent,DialogActions,Box,Typography,Select,MenuItem,FormControl,InputLabel,Grid,Chip}from\"@mui/material\";import{CreateContact,EditContact}from\"../../../ContactData.ts\";import{toast}from\"react-toastify\";// Country codes data\nimport{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";const countryCodes=[{code:\"+1\",country:\"United States\",flag:\"https://flagcdn.com/w20/us.png\"},{code:\"+1\",country:\"Canada\",flag:\"https://flagcdn.com/w20/ca.png\"},{code:\"+44\",country:\"United Kingdom\",flag:\"https://flagcdn.com/w20/gb.png\"},{code:\"+49\",country:\"Germany\",flag:\"https://flagcdn.com/w20/de.png\"},{code:\"+33\",country:\"France\",flag:\"https://flagcdn.com/w20/fr.png\"},{code:\"+39\",country:\"Italy\",flag:\"https://flagcdn.com/w20/it.png\"},{code:\"+34\",country:\"Spain\",flag:\"https://flagcdn.com/w20/es.png\"},{code:\"+31\",country:\"Netherlands\",flag:\"https://flagcdn.com/w20/nl.png\"},{code:\"+41\",country:\"Switzerland\",flag:\"https://flagcdn.com/w20/ch.png\"},{code:\"+43\",country:\"Austria\",flag:\"https://flagcdn.com/w20/at.png\"},{code:\"+32\",country:\"Belgium\",flag:\"https://flagcdn.com/w20/be.png\"},{code:\"+45\",country:\"Denmark\",flag:\"https://flagcdn.com/w20/dk.png\"},{code:\"+46\",country:\"Sweden\",flag:\"https://flagcdn.com/w20/se.png\"},{code:\"+47\",country:\"Norway\",flag:\"https://flagcdn.com/w20/no.png\"},{code:\"+358\",country:\"Finland\",flag:\"https://flagcdn.com/w20/fi.png\"},{code:\"+91\",country:\"India\",flag:\"https://flagcdn.com/w20/in.png\"},{code:\"+86\",country:\"China\",flag:\"https://flagcdn.com/w20/cn.png\"},{code:\"+81\",country:\"Japan\",flag:\"https://flagcdn.com/w20/jp.png\"},{code:\"+82\",country:\"South Korea\",flag:\"https://flagcdn.com/w20/kr.png\"},{code:\"+65\",country:\"Singapore\",flag:\"https://flagcdn.com/w20/sg.png\"},{code:\"+60\",country:\"Malaysia\",flag:\"https://flagcdn.com/w20/my.png\"},{code:\"+66\",country:\"Thailand\",flag:\"https://flagcdn.com/w20/th.png\"},{code:\"+84\",country:\"Vietnam\",flag:\"https://flagcdn.com/w20/vn.png\"},{code:\"+63\",country:\"Philippines\",flag:\"https://flagcdn.com/w20/ph.png\"},{code:\"+62\",country:\"Indonesia\",flag:\"https://flagcdn.com/w20/id.png\"},{code:\"+61\",country:\"Australia\",flag:\"https://flagcdn.com/w20/au.png\"},{code:\"+64\",country:\"New Zealand\",flag:\"https://flagcdn.com/w20/nz.png\"},{code:\"+27\",country:\"South Africa\",flag:\"https://flagcdn.com/w20/za.png\"},{code:\"+20\",country:\"Egypt\",flag:\"https://flagcdn.com/w20/eg.png\"},{code:\"+216\",country:\"Tunisia\",flag:\"https://flagcdn.com/w20/tn.png\"},{code:\"+234\",country:\"Nigeria\",flag:\"https://flagcdn.com/w20/ng.png\"},{code:\"+254\",country:\"Kenya\",flag:\"https://flagcdn.com/w20/ke.png\"},{code:\"+971\",country:\"UAE\",flag:\"https://flagcdn.com/w20/ae.png\"},{code:\"+966\",country:\"Saudi Arabia\",flag:\"https://flagcdn.com/w20/sa.png\"},{code:\"+974\",country:\"Qatar\",flag:\"https://flagcdn.com/w20/qa.png\"},{code:\"+965\",country:\"Kuwait\",flag:\"https://flagcdn.com/w20/kw.png\"},{code:\"+973\",country:\"Bahrain\",flag:\"https://flagcdn.com/w20/bh.png\"},{code:\"+968\",country:\"Oman\",flag:\"https://flagcdn.com/w20/om.png\"},{code:\"+972\",country:\"Israel\",flag:\"https://flagcdn.com/w20/il.png\"},{code:\"+90\",country:\"Turkey\",flag:\"https://flagcdn.com/w20/tr.png\"},{code:\"+7\",country:\"Russia\",flag:\"https://flagcdn.com/w20/ru.png\"},{code:\"+380\",country:\"Ukraine\",flag:\"https://flagcdn.com/w20/ua.png\"},{code:\"+48\",country:\"Poland\",flag:\"https://flagcdn.com/w20/pl.png\"},{code:\"+420\",country:\"Czech Republic\",flag:\"https://flagcdn.com/w20/cz.png\"},{code:\"+36\",country:\"Hungary\",flag:\"https://flagcdn.com/w20/hu.png\"},{code:\"+40\",country:\"Romania\",flag:\"https://flagcdn.com/w20/ro.png\"},{code:\"+359\",country:\"Bulgaria\",flag:\"https://flagcdn.com/w20/bg.png\"},{code:\"+385\",country:\"Croatia\",flag:\"https://flagcdn.com/w20/hr.png\"},{code:\"+381\",country:\"Serbia\",flag:\"https://flagcdn.com/w20/rs.png\"},{code:\"+55\",country:\"Brazil\",flag:\"https://flagcdn.com/w20/br.png\"},{code:\"+52\",country:\"Mexico\",flag:\"https://flagcdn.com/w20/mx.png\"},{code:\"+54\",country:\"Argentina\",flag:\"https://flagcdn.com/w20/ar.png\"},{code:\"+56\",country:\"Chile\",flag:\"https://flagcdn.com/w20/cl.png\"},{code:\"+57\",country:\"Colombia\",flag:\"https://flagcdn.com/w20/co.png\"},{code:\"+51\",country:\"Peru\",flag:\"https://flagcdn.com/w20/pe.png\"},{code:\"+58\",country:\"Venezuela\",flag:\"https://flagcdn.com/w20/ve.png\"},{code:\"+593\",country:\"Ecuador\",flag:\"https://flagcdn.com/w20/ec.png\"},{code:\"+595\",country:\"Paraguay\",flag:\"https://flagcdn.com/w20/py.png\"},{code:\"+598\",country:\"Uruguay\",flag:\"https://flagcdn.com/w20/uy.png\"}];const WhatsAppLinkDialog=_ref=>{let{setOpenWhatsAppDialog,openWhatsAppDialog,Id,editingContact=null,fetchProfile,clearEditingContact}=_ref;const[whatsAppNumber,setWhatsAppNumber]=useState(\"\");const[contactName,setContactName]=useState(\"\");const[selectedCountryCode,setSelectedCountryCode]=useState(\"+1\");const[isLoading,setIsLoading]=useState(false);// Function to extract country code from a phone number\nconst extractCountryCode=phoneNumber=>{if(!phoneNumber||!phoneNumber.startsWith(\"+\")){return\"+1\";// Default to US\n}// Sort country codes by length (longest first) to match correctly\nconst sortedCodes=countryCodes.map(c=>c.code).filter((code,index,arr)=>arr.indexOf(code)===index)// Remove duplicates\n.sort((a,b)=>b.length-a.length);for(const code of sortedCodes){if(phoneNumber.startsWith(code)){return code;}}return\"+1\";// Default fallback\n};// Function to extract the number without country code\nconst extractNumberWithoutCode=(phoneNumber,countryCode)=>{if(!phoneNumber||!phoneNumber.startsWith(countryCode)){return\"\";}return phoneNumber.substring(countryCode.length).replace(/^\\s+/,\"\");};useEffect(()=>{if(editingContact){const fullNumber=editingContact.LinkUrl||\"\";const extractedCode=extractCountryCode(fullNumber);const numberWithoutCode=extractNumberWithoutCode(fullNumber,extractedCode);setSelectedCountryCode(extractedCode);setWhatsAppNumber(numberWithoutCode);setContactName(editingContact.Title||\"\");}else{setSelectedCountryCode(\"+1\");setWhatsAppNumber(\"\");setContactName(\"\");}},[editingContact,openWhatsAppDialog]);const handleContactNameChange=event=>{setContactName(event.target.value);};const validateWhatsAppNumber=(countryCode,phoneNumber)=>{if(!phoneNumber||phoneNumber.trim()===\"\"){return{isValid:false,error:\"Phone number is required\"};}if(!countryCode){return{isValid:false,error:\"Country code is required\"};}// Remove all spaces, dashes, parentheses, and other formatting from phone number\nconst cleanNumber=phoneNumber.replace(/[\\s\\-\\(\\)\\.]/g,\"\");// Check if it contains only digits after cleaning\nif(!/^\\d+$/.test(cleanNumber)){return{isValid:false,error:\"Phone number should contain only digits, spaces, dashes, or parentheses\"};}// Country-specific validation patterns (same as Phone)\nconst countryValidation={\"+1\":{// US/Canada\nminLength:10,maxLength:10,pattern:/^[2-9]\\d{9}$/,errorMsg:\"US/Canada numbers should be 10 digits starting with 2-9\"},\"+44\":{// UK\nminLength:10,maxLength:11,pattern:/^[1-9]\\d{9,10}$/,errorMsg:\"UK numbers should be 10-11 digits\"},\"+49\":{// Germany\nminLength:10,maxLength:12,pattern:/^[1-9]\\d{9,11}$/,errorMsg:\"German numbers should be 10-12 digits\"},\"+33\":{// France\nminLength:9,maxLength:9,pattern:/^[1-9]\\d{8}$/,errorMsg:\"French numbers should be 9 digits starting with 1-9\"},\"+39\":{// Italy\nminLength:9,maxLength:11,pattern:/^[0-9]\\d{8,10}$/,errorMsg:\"Italian numbers should be 9-11 digits\"},\"+34\":{// Spain\nminLength:9,maxLength:9,pattern:/^[6-9]\\d{8}$/,errorMsg:\"Spanish mobile numbers should be 9 digits starting with 6-9\"},\"+91\":{// India\nminLength:10,maxLength:10,pattern:/^[6-9]\\d{9}$/,errorMsg:\"Indian mobile numbers should be 10 digits starting with 6-9\"},\"+86\":{// China\nminLength:11,maxLength:11,pattern:/^1[3-9]\\d{9}$/,errorMsg:\"Chinese mobile numbers should be 11 digits starting with 13-19\"},\"+81\":{// Japan\nminLength:10,maxLength:11,pattern:/^[7-9]\\d{9,10}$/,errorMsg:\"Japanese mobile numbers should be 10-11 digits starting with 7-9\"},\"+82\":{// South Korea\nminLength:9,maxLength:10,pattern:/^1[0-9]\\d{7,8}$/,errorMsg:\"Korean mobile numbers should be 9-10 digits starting with 10-19\"},\"+971\":{// UAE\nminLength:9,maxLength:9,pattern:/^5\\d{8}$/,errorMsg:\"UAE mobile numbers should be 9 digits starting with 5\"},\"+966\":{// Saudi Arabia\nminLength:9,maxLength:9,pattern:/^5\\d{8}$/,errorMsg:\"Saudi mobile numbers should be 9 digits starting with 5\"},\"+55\":{// Brazil\nminLength:10,maxLength:11,pattern:/^[1-9]\\d{9,10}$/,errorMsg:\"Brazilian mobile numbers should be 10-11 digits\"},\"+52\":{// Mexico\nminLength:10,maxLength:10,pattern:/^[1-9]\\d{9}$/,errorMsg:\"Mexican mobile numbers should be 10 digits\"},\"+216\":{// Tunisia\nminLength:8,maxLength:8,pattern:/^[0-9]{8}$/,errorMsg:\"Tunisian mobile numbers should be exactly 8 digits\"}};const validation=countryValidation[countryCode];if(validation){// Check length\nif(cleanNumber.length<validation.minLength){return{isValid:false,error:`Number too short. ${validation.errorMsg}`};}if(cleanNumber.length>validation.maxLength){return{isValid:false,error:`Number too long. ${validation.errorMsg}`};}// Check pattern\nif(!validation.pattern.test(cleanNumber)){return{isValid:false,error:validation.errorMsg};}}else{// General validation for countries not specifically listed\nif(cleanNumber.length<7){return{isValid:false,error:\"Phone number too short (minimum 7 digits)\"};}if(cleanNumber.length>15){return{isValid:false,error:\"Phone number too long (maximum 15 digits)\"};}}return{isValid:true,error:null};};const isValidWhatsAppNumber=(countryCode,phoneNumber)=>{const validation=validateWhatsAppNumber(countryCode,phoneNumber);return validation.isValid;};const getValidationError=(countryCode,phoneNumber)=>{const validation=validateWhatsAppNumber(countryCode,phoneNumber);return validation.error;};const handleDone=async()=>{// Validate using separated country code and phone number\nconst validationError=getValidationError(selectedCountryCode,whatsAppNumber);if(validationError){toast.error(validationError,{position:\"top-center\",autoClose:3000});return;}// Combine country code with number for storage\nconst fullNumber=selectedCountryCode+whatsAppNumber.replace(/^\\s+/,\"\");if(!contactName.trim()){toast.error(\"Contact name is required\",{position:\"top-center\",autoClose:2000});return;}setIsLoading(true);let response;try{if(editingContact){response=await EditContact({Id:editingContact.Id,ContactInfo:fullNumber,Category:\"WhatsApp\",Title:contactName.trim(),isPublic:true});}else{response=await CreateContact({UserId:Id,ContactInfo:fullNumber,Category:\"WhatsApp\",Title:contactName.trim(),isPublic:true});}localStorage.setItem(\"isLinksCardVisible\",\"true\");setContactName(\"\");setWhatsAppNumber(\"\");setContactName(\"\");if(clearEditingContact)clearEditingContact();setOpenWhatsAppDialog(false);if(response){toast.success(editingContact?\"WhatsApp contact updated\":\"WhatsApp contact added\",{position:\"top-center\",autoClose:1000});if(fetchProfile)fetchProfile();}else{toast.error(\"Error while saving WhatsApp contact\",{position:\"top-center\",autoClose:1000});}}catch(error){toast.error(\"Error while saving WhatsApp contact\",{position:\"top-center\",autoClose:1000});}finally{setIsLoading(false);}};return/*#__PURE__*/_jsxs(Dialog,{open:openWhatsAppDialog,onClose:()=>{setWhatsAppNumber(\"\");setContactName(\"\");if(clearEditingContact)clearEditingContact();setOpenWhatsAppDialog(false);},children:[/*#__PURE__*/_jsx(DialogTitle,{children:editingContact?\"Edit WhatsApp Contact\":\"Add WhatsApp Contact\"}),/*#__PURE__*/_jsxs(DialogContent,{children:[/*#__PURE__*/_jsx(TextField,{name:\"contactName\",autoFocus:true,margin:\"dense\",label:\"Contact Name\",type:\"text\",fullWidth:true,required:true,value:contactName,onChange:handleContactNameChange,helperText:contactName===\"\"?\"Contact name is required\":\"\",sx:{mb:2}}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:2,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:4,children:/*#__PURE__*/_jsxs(FormControl,{fullWidth:true,margin:\"dense\",children:[/*#__PURE__*/_jsx(InputLabel,{children:\"Country\"}),/*#__PURE__*/_jsx(Select,{value:selectedCountryCode,onChange:e=>setSelectedCountryCode(e.target.value),label:\"Country\",sx:{\"& .MuiSelect-select\":{padding:{xs:\"12px 14px\",sm:\"16.5px 14px\"},fontSize:{xs:\"0.875rem\",sm:\"1rem\"}}},renderValue:value=>{const country=countryCodes.find(c=>c.code===value);return country?/*#__PURE__*/_jsxs(Box,{sx:{display:\"flex\",alignItems:\"center\",gap:{xs:0.5,sm:1},minWidth:0},children:[/*#__PURE__*/_jsx(\"img\",{src:country.flag,alt:country.country,style:{width:20,height:15,flexShrink:0}}),/*#__PURE__*/_jsx(\"span\",{style:{fontSize:\"inherit\",overflow:\"hidden\",textOverflow:\"ellipsis\",whiteSpace:\"nowrap\"},children:value})]}):value;},children:countryCodes.map((country,index)=>/*#__PURE__*/_jsx(MenuItem,{value:country.code,sx:{padding:{xs:\"8px 16px\",sm:\"6px 16px\"},minHeight:{xs:\"48px\",sm:\"auto\"}},children:/*#__PURE__*/_jsxs(Box,{sx:{display:\"flex\",alignItems:\"center\",gap:1,width:\"100%\",minWidth:0},children:[/*#__PURE__*/_jsx(\"img\",{src:country.flag,alt:country.country,style:{width:20,height:15,flexShrink:0}}),/*#__PURE__*/_jsx(\"span\",{style:{fontWeight:500,flexShrink:0},children:country.code}),/*#__PURE__*/_jsx(\"span\",{style:{fontSize:\"0.875rem\",color:\"#666\",overflow:\"hidden\",textOverflow:\"ellipsis\",whiteSpace:\"nowrap\",flex:1,minWidth:0},children:country.country})]})},`${country.code}-${index}`))})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:8,children:/*#__PURE__*/_jsx(TextField,{name:\"WhatsAppNumber\",margin:\"dense\",label:\"Phone Number\",type:\"tel\",fullWidth:true,required:true,value:whatsAppNumber,onChange:e=>setWhatsAppNumber(e.target.value.replace(/[^\\d\\s\\-\\(\\)]/g,\"\")),error:whatsAppNumber!==\"\"&&!isValidWhatsAppNumber(selectedCountryCode,whatsAppNumber),helperText:whatsAppNumber===\"\"?\"Phone number is required\":whatsAppNumber!==\"\"&&!isValidWhatsAppNumber(selectedCountryCode,whatsAppNumber)?getValidationError(selectedCountryCode,whatsAppNumber):\"✓ Valid WhatsApp number\",placeholder:\"************\",inputProps:{maxLength:15},sx:{\"& .MuiInputBase-input\":{fontSize:{xs:\"16px\",sm:\"1rem\"},// Prevents zoom on iOS\npadding:{xs:\"12px 14px\",sm:\"16.5px 14px\"}},\"& .MuiFormHelperText-root\":{fontSize:{xs:\"0.75rem\",sm:\"0.75rem\"},marginTop:{xs:\"4px\",sm:\"3px\"}}}})})]}),whatsAppNumber&&/*#__PURE__*/_jsx(Box,{sx:{mt:{xs:2,sm:1},mb:{xs:2,sm:1},display:\"flex\",justifyContent:{xs:\"center\",sm:\"flex-start\"}},children:/*#__PURE__*/_jsx(Chip,{label:`Full Number: ${selectedCountryCode} ${whatsAppNumber}`,variant:\"outlined\",size:\"small\",color:isValidWhatsAppNumber(selectedCountryCode,whatsAppNumber)?\"success\":\"default\",sx:{fontSize:{xs:\"0.75rem\",sm:\"0.8125rem\"},height:{xs:\"28px\",sm:\"24px\"},\"& .MuiChip-label\":{padding:{xs:\"0 8px\",sm:\"0 12px\"}}}})}),/*#__PURE__*/_jsxs(Box,{mt:2,p:2,sx:{backgroundColor:\"#f0f0f0\",borderRadius:\"5px\"},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",color:\"textPrimary\",children:\"How to Add WhatsApp Contact\"}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",color:\"textSecondary\",children:[\"\\u2022 \",/*#__PURE__*/_jsx(\"strong\",{children:\"Select your country:\"}),\" Choose from the dropdown list with flags\"]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",color:\"textSecondary\",children:[\"\\u2022 \",/*#__PURE__*/_jsx(\"strong\",{children:\"Enter phone number:\"}),\" Only digits, minimum 8 digits (e.g., 23456789)\"]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",color:\"textSecondary\",children:[\"\\u2022 \",/*#__PURE__*/_jsx(\"strong\",{children:\"No spaces or symbols:\"}),\" Just the numbers without any formatting\"]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",color:\"textSecondary\",children:[\"\\u2022 \",/*#__PURE__*/_jsx(\"strong\",{children:\"Preview:\"}),\" See the full international number below\"]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",color:\"textSecondary\",children:[\"\\u2022 \",/*#__PURE__*/_jsx(\"strong\",{children:\"Multiple contacts:\"}),\" Add different WhatsApp numbers with unique names\"]})]})]}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{onClick:()=>{setContactName(\"\");setWhatsAppNumber(\"\");setContactName(\"\");if(clearEditingContact)clearEditingContact();setOpenWhatsAppDialog(false);},children:\"Cancel\"}),/*#__PURE__*/_jsx(Button,{onClick:handleDone,disabled:whatsAppNumber===\"\"||!isValidWhatsAppNumber(selectedCountryCode,whatsAppNumber)||contactName.trim()===\"\"||isLoading,children:isLoading?\"Saving...\":editingContact?\"Update\":\"Add\"})]})]});};export default WhatsAppLinkDialog;", "map": {"version": 3, "names": ["useState", "useEffect", "TextField", "<PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Box", "Typography", "Select", "MenuItem", "FormControl", "InputLabel", "Grid", "Chip", "CreateContact", "EditContact", "toast", "jsx", "_jsx", "jsxs", "_jsxs", "countryCodes", "code", "country", "flag", "WhatsAppLinkDialog", "_ref", "setOpenWhatsAppDialog", "openWhatsAppDialog", "Id", "editingContact", "fetchProfile", "clearEditingContact", "whatsAppNumber", "setWhatsAppNumber", "contactName", "setContactName", "selectedCountryCode", "setSelectedCountryCode", "isLoading", "setIsLoading", "extractCountryCode", "phoneNumber", "startsWith", "sortedCodes", "map", "c", "filter", "index", "arr", "indexOf", "sort", "a", "b", "length", "extractNumberWithoutCode", "countryCode", "substring", "replace", "fullNumber", "LinkUrl", "extractedCode", "numberWithoutCode", "Title", "handleContactNameChange", "event", "target", "value", "validateWhatsAppNumber", "trim", "<PERSON><PERSON><PERSON><PERSON>", "error", "cleanNumber", "test", "countryValidation", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "pattern", "errorMsg", "validation", "isValidWhatsAppNumber", "getValidationError", "handleDone", "validationError", "position", "autoClose", "response", "ContactInfo", "Category", "isPublic", "UserId", "localStorage", "setItem", "success", "open", "onClose", "children", "name", "autoFocus", "margin", "label", "type", "fullWidth", "required", "onChange", "helperText", "sx", "mb", "container", "spacing", "item", "xs", "sm", "e", "padding", "fontSize", "renderValue", "find", "display", "alignItems", "gap", "min<PERSON><PERSON><PERSON>", "src", "alt", "style", "width", "height", "flexShrink", "overflow", "textOverflow", "whiteSpace", "minHeight", "fontWeight", "color", "flex", "placeholder", "inputProps", "marginTop", "mt", "justifyContent", "variant", "size", "p", "backgroundColor", "borderRadius", "onClick", "disabled"], "sources": ["C:/Users/<USER>/Desktop/iDigitalX/ClientApp/src/sections/@dashboard/Link/WhatsAppLinkDialog.js"], "sourcesContent": ["import { useState, useEffect } from \"react\";\r\nimport {\r\n  TextField,\r\n  Button,\r\n  Dialog,\r\n  DialogTitle,\r\n  DialogContent,\r\n  DialogActions,\r\n  Box,\r\n  Typography,\r\n  Select,\r\n  MenuItem,\r\n  FormControl,\r\n  InputLabel,\r\n  Grid,\r\n  Chip,\r\n} from \"@mui/material\";\r\n\r\nimport { CreateContact, EditContact } from \"../../../ContactData.ts\";\r\n\r\nimport { toast } from \"react-toastify\";\r\n\r\n// Country codes data\r\nconst countryCodes = [\r\n  {\r\n    code: \"+1\",\r\n    country: \"United States\",\r\n    flag: \"https://flagcdn.com/w20/us.png\",\r\n  },\r\n  { code: \"+1\", country: \"Canada\", flag: \"https://flagcdn.com/w20/ca.png\" },\r\n  {\r\n    code: \"+44\",\r\n    country: \"United Kingdom\",\r\n    flag: \"https://flagcdn.com/w20/gb.png\",\r\n  },\r\n  { code: \"+49\", country: \"Germany\", flag: \"https://flagcdn.com/w20/de.png\" },\r\n  { code: \"+33\", country: \"France\", flag: \"https://flagcdn.com/w20/fr.png\" },\r\n  { code: \"+39\", country: \"Italy\", flag: \"https://flagcdn.com/w20/it.png\" },\r\n  { code: \"+34\", country: \"Spain\", flag: \"https://flagcdn.com/w20/es.png\" },\r\n  {\r\n    code: \"+31\",\r\n    country: \"Netherlands\",\r\n    flag: \"https://flagcdn.com/w20/nl.png\",\r\n  },\r\n  {\r\n    code: \"+41\",\r\n    country: \"Switzerland\",\r\n    flag: \"https://flagcdn.com/w20/ch.png\",\r\n  },\r\n  { code: \"+43\", country: \"Austria\", flag: \"https://flagcdn.com/w20/at.png\" },\r\n  { code: \"+32\", country: \"Belgium\", flag: \"https://flagcdn.com/w20/be.png\" },\r\n  { code: \"+45\", country: \"Denmark\", flag: \"https://flagcdn.com/w20/dk.png\" },\r\n  { code: \"+46\", country: \"Sweden\", flag: \"https://flagcdn.com/w20/se.png\" },\r\n  { code: \"+47\", country: \"Norway\", flag: \"https://flagcdn.com/w20/no.png\" },\r\n  { code: \"+358\", country: \"Finland\", flag: \"https://flagcdn.com/w20/fi.png\" },\r\n  { code: \"+91\", country: \"India\", flag: \"https://flagcdn.com/w20/in.png\" },\r\n  { code: \"+86\", country: \"China\", flag: \"https://flagcdn.com/w20/cn.png\" },\r\n  { code: \"+81\", country: \"Japan\", flag: \"https://flagcdn.com/w20/jp.png\" },\r\n  {\r\n    code: \"+82\",\r\n    country: \"South Korea\",\r\n    flag: \"https://flagcdn.com/w20/kr.png\",\r\n  },\r\n  { code: \"+65\", country: \"Singapore\", flag: \"https://flagcdn.com/w20/sg.png\" },\r\n  { code: \"+60\", country: \"Malaysia\", flag: \"https://flagcdn.com/w20/my.png\" },\r\n  { code: \"+66\", country: \"Thailand\", flag: \"https://flagcdn.com/w20/th.png\" },\r\n  { code: \"+84\", country: \"Vietnam\", flag: \"https://flagcdn.com/w20/vn.png\" },\r\n  {\r\n    code: \"+63\",\r\n    country: \"Philippines\",\r\n    flag: \"https://flagcdn.com/w20/ph.png\",\r\n  },\r\n  { code: \"+62\", country: \"Indonesia\", flag: \"https://flagcdn.com/w20/id.png\" },\r\n  { code: \"+61\", country: \"Australia\", flag: \"https://flagcdn.com/w20/au.png\" },\r\n  {\r\n    code: \"+64\",\r\n    country: \"New Zealand\",\r\n    flag: \"https://flagcdn.com/w20/nz.png\",\r\n  },\r\n  {\r\n    code: \"+27\",\r\n    country: \"South Africa\",\r\n    flag: \"https://flagcdn.com/w20/za.png\",\r\n  },\r\n  { code: \"+20\", country: \"Egypt\", flag: \"https://flagcdn.com/w20/eg.png\" },\r\n  { code: \"+216\", country: \"Tunisia\", flag: \"https://flagcdn.com/w20/tn.png\" },\r\n  { code: \"+234\", country: \"Nigeria\", flag: \"https://flagcdn.com/w20/ng.png\" },\r\n  { code: \"+254\", country: \"Kenya\", flag: \"https://flagcdn.com/w20/ke.png\" },\r\n  { code: \"+971\", country: \"UAE\", flag: \"https://flagcdn.com/w20/ae.png\" },\r\n  {\r\n    code: \"+966\",\r\n    country: \"Saudi Arabia\",\r\n    flag: \"https://flagcdn.com/w20/sa.png\",\r\n  },\r\n  { code: \"+974\", country: \"Qatar\", flag: \"https://flagcdn.com/w20/qa.png\" },\r\n  { code: \"+965\", country: \"Kuwait\", flag: \"https://flagcdn.com/w20/kw.png\" },\r\n  { code: \"+973\", country: \"Bahrain\", flag: \"https://flagcdn.com/w20/bh.png\" },\r\n  { code: \"+968\", country: \"Oman\", flag: \"https://flagcdn.com/w20/om.png\" },\r\n  { code: \"+972\", country: \"Israel\", flag: \"https://flagcdn.com/w20/il.png\" },\r\n  { code: \"+90\", country: \"Turkey\", flag: \"https://flagcdn.com/w20/tr.png\" },\r\n  { code: \"+7\", country: \"Russia\", flag: \"https://flagcdn.com/w20/ru.png\" },\r\n  { code: \"+380\", country: \"Ukraine\", flag: \"https://flagcdn.com/w20/ua.png\" },\r\n  { code: \"+48\", country: \"Poland\", flag: \"https://flagcdn.com/w20/pl.png\" },\r\n  {\r\n    code: \"+420\",\r\n    country: \"Czech Republic\",\r\n    flag: \"https://flagcdn.com/w20/cz.png\",\r\n  },\r\n  { code: \"+36\", country: \"Hungary\", flag: \"https://flagcdn.com/w20/hu.png\" },\r\n  { code: \"+40\", country: \"Romania\", flag: \"https://flagcdn.com/w20/ro.png\" },\r\n  { code: \"+359\", country: \"Bulgaria\", flag: \"https://flagcdn.com/w20/bg.png\" },\r\n  { code: \"+385\", country: \"Croatia\", flag: \"https://flagcdn.com/w20/hr.png\" },\r\n  { code: \"+381\", country: \"Serbia\", flag: \"https://flagcdn.com/w20/rs.png\" },\r\n  { code: \"+55\", country: \"Brazil\", flag: \"https://flagcdn.com/w20/br.png\" },\r\n  { code: \"+52\", country: \"Mexico\", flag: \"https://flagcdn.com/w20/mx.png\" },\r\n  { code: \"+54\", country: \"Argentina\", flag: \"https://flagcdn.com/w20/ar.png\" },\r\n  { code: \"+56\", country: \"Chile\", flag: \"https://flagcdn.com/w20/cl.png\" },\r\n  { code: \"+57\", country: \"Colombia\", flag: \"https://flagcdn.com/w20/co.png\" },\r\n  { code: \"+51\", country: \"Peru\", flag: \"https://flagcdn.com/w20/pe.png\" },\r\n  { code: \"+58\", country: \"Venezuela\", flag: \"https://flagcdn.com/w20/ve.png\" },\r\n  { code: \"+593\", country: \"Ecuador\", flag: \"https://flagcdn.com/w20/ec.png\" },\r\n  { code: \"+595\", country: \"Paraguay\", flag: \"https://flagcdn.com/w20/py.png\" },\r\n  { code: \"+598\", country: \"Uruguay\", flag: \"https://flagcdn.com/w20/uy.png\" },\r\n];\r\n\r\nconst WhatsAppLinkDialog = ({\r\n  setOpenWhatsAppDialog,\r\n  openWhatsAppDialog,\r\n  Id,\r\n  editingContact = null,\r\n  fetchProfile,\r\n  clearEditingContact,\r\n}) => {\r\n  const [whatsAppNumber, setWhatsAppNumber] = useState(\"\");\r\n  const [contactName, setContactName] = useState(\"\");\r\n  const [selectedCountryCode, setSelectedCountryCode] = useState(\"+1\");\r\n  const [isLoading, setIsLoading] = useState(false);\r\n\r\n  // Function to extract country code from a phone number\r\n  const extractCountryCode = (phoneNumber) => {\r\n    if (!phoneNumber || !phoneNumber.startsWith(\"+\")) {\r\n      return \"+1\"; // Default to US\r\n    }\r\n\r\n    // Sort country codes by length (longest first) to match correctly\r\n    const sortedCodes = countryCodes\r\n      .map((c) => c.code)\r\n      .filter((code, index, arr) => arr.indexOf(code) === index) // Remove duplicates\r\n      .sort((a, b) => b.length - a.length);\r\n\r\n    for (const code of sortedCodes) {\r\n      if (phoneNumber.startsWith(code)) {\r\n        return code;\r\n      }\r\n    }\r\n\r\n    return \"+1\"; // Default fallback\r\n  };\r\n\r\n  // Function to extract the number without country code\r\n  const extractNumberWithoutCode = (phoneNumber, countryCode) => {\r\n    if (!phoneNumber || !phoneNumber.startsWith(countryCode)) {\r\n      return \"\";\r\n    }\r\n    return phoneNumber.substring(countryCode.length).replace(/^\\s+/, \"\");\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (editingContact) {\r\n      const fullNumber = editingContact.LinkUrl || \"\";\r\n      const extractedCode = extractCountryCode(fullNumber);\r\n      const numberWithoutCode = extractNumberWithoutCode(\r\n        fullNumber,\r\n        extractedCode\r\n      );\r\n\r\n      setSelectedCountryCode(extractedCode);\r\n      setWhatsAppNumber(numberWithoutCode);\r\n      setContactName(editingContact.Title || \"\");\r\n    } else {\r\n      setSelectedCountryCode(\"+1\");\r\n      setWhatsAppNumber(\"\");\r\n      setContactName(\"\");\r\n    }\r\n  }, [editingContact, openWhatsAppDialog]);\r\n\r\n  const handleContactNameChange = (event) => {\r\n    setContactName(event.target.value);\r\n  };\r\n\r\n  const validateWhatsAppNumber = (countryCode, phoneNumber) => {\r\n    if (!phoneNumber || phoneNumber.trim() === \"\") {\r\n      return { isValid: false, error: \"Phone number is required\" };\r\n    }\r\n\r\n    if (!countryCode) {\r\n      return { isValid: false, error: \"Country code is required\" };\r\n    }\r\n\r\n    // Remove all spaces, dashes, parentheses, and other formatting from phone number\r\n    const cleanNumber = phoneNumber.replace(/[\\s\\-\\(\\)\\.]/g, \"\");\r\n\r\n    // Check if it contains only digits after cleaning\r\n    if (!/^\\d+$/.test(cleanNumber)) {\r\n      return {\r\n        isValid: false,\r\n        error:\r\n          \"Phone number should contain only digits, spaces, dashes, or parentheses\",\r\n      };\r\n    }\r\n\r\n    // Country-specific validation patterns (same as Phone)\r\n    const countryValidation = {\r\n      \"+1\": {\r\n        // US/Canada\r\n        minLength: 10,\r\n        maxLength: 10,\r\n        pattern: /^[2-9]\\d{9}$/,\r\n        errorMsg: \"US/Canada numbers should be 10 digits starting with 2-9\",\r\n      },\r\n      \"+44\": {\r\n        // UK\r\n        minLength: 10,\r\n        maxLength: 11,\r\n        pattern: /^[1-9]\\d{9,10}$/,\r\n        errorMsg: \"UK numbers should be 10-11 digits\",\r\n      },\r\n      \"+49\": {\r\n        // Germany\r\n        minLength: 10,\r\n        maxLength: 12,\r\n        pattern: /^[1-9]\\d{9,11}$/,\r\n        errorMsg: \"German numbers should be 10-12 digits\",\r\n      },\r\n      \"+33\": {\r\n        // France\r\n        minLength: 9,\r\n        maxLength: 9,\r\n        pattern: /^[1-9]\\d{8}$/,\r\n        errorMsg: \"French numbers should be 9 digits starting with 1-9\",\r\n      },\r\n      \"+39\": {\r\n        // Italy\r\n        minLength: 9,\r\n        maxLength: 11,\r\n        pattern: /^[0-9]\\d{8,10}$/,\r\n        errorMsg: \"Italian numbers should be 9-11 digits\",\r\n      },\r\n      \"+34\": {\r\n        // Spain\r\n        minLength: 9,\r\n        maxLength: 9,\r\n        pattern: /^[6-9]\\d{8}$/,\r\n        errorMsg: \"Spanish mobile numbers should be 9 digits starting with 6-9\",\r\n      },\r\n      \"+91\": {\r\n        // India\r\n        minLength: 10,\r\n        maxLength: 10,\r\n        pattern: /^[6-9]\\d{9}$/,\r\n        errorMsg: \"Indian mobile numbers should be 10 digits starting with 6-9\",\r\n      },\r\n      \"+86\": {\r\n        // China\r\n        minLength: 11,\r\n        maxLength: 11,\r\n        pattern: /^1[3-9]\\d{9}$/,\r\n        errorMsg:\r\n          \"Chinese mobile numbers should be 11 digits starting with 13-19\",\r\n      },\r\n      \"+81\": {\r\n        // Japan\r\n        minLength: 10,\r\n        maxLength: 11,\r\n        pattern: /^[7-9]\\d{9,10}$/,\r\n        errorMsg:\r\n          \"Japanese mobile numbers should be 10-11 digits starting with 7-9\",\r\n      },\r\n      \"+82\": {\r\n        // South Korea\r\n        minLength: 9,\r\n        maxLength: 10,\r\n        pattern: /^1[0-9]\\d{7,8}$/,\r\n        errorMsg:\r\n          \"Korean mobile numbers should be 9-10 digits starting with 10-19\",\r\n      },\r\n      \"+971\": {\r\n        // UAE\r\n        minLength: 9,\r\n        maxLength: 9,\r\n        pattern: /^5\\d{8}$/,\r\n        errorMsg: \"UAE mobile numbers should be 9 digits starting with 5\",\r\n      },\r\n      \"+966\": {\r\n        // Saudi Arabia\r\n        minLength: 9,\r\n        maxLength: 9,\r\n        pattern: /^5\\d{8}$/,\r\n        errorMsg: \"Saudi mobile numbers should be 9 digits starting with 5\",\r\n      },\r\n      \"+55\": {\r\n        // Brazil\r\n        minLength: 10,\r\n        maxLength: 11,\r\n        pattern: /^[1-9]\\d{9,10}$/,\r\n        errorMsg: \"Brazilian mobile numbers should be 10-11 digits\",\r\n      },\r\n      \"+52\": {\r\n        // Mexico\r\n        minLength: 10,\r\n        maxLength: 10,\r\n        pattern: /^[1-9]\\d{9}$/,\r\n        errorMsg: \"Mexican mobile numbers should be 10 digits\",\r\n      },\r\n      \"+216\": {\r\n        // Tunisia\r\n        minLength: 8,\r\n        maxLength: 8,\r\n        pattern: /^[0-9]{8}$/,\r\n        errorMsg: \"Tunisian mobile numbers should be exactly 8 digits\",\r\n      },\r\n    };\r\n\r\n    const validation = countryValidation[countryCode];\r\n\r\n    if (validation) {\r\n      // Check length\r\n      if (cleanNumber.length < validation.minLength) {\r\n        return {\r\n          isValid: false,\r\n          error: `Number too short. ${validation.errorMsg}`,\r\n        };\r\n      }\r\n      if (cleanNumber.length > validation.maxLength) {\r\n        return {\r\n          isValid: false,\r\n          error: `Number too long. ${validation.errorMsg}`,\r\n        };\r\n      }\r\n\r\n      // Check pattern\r\n      if (!validation.pattern.test(cleanNumber)) {\r\n        return { isValid: false, error: validation.errorMsg };\r\n      }\r\n    } else {\r\n      // General validation for countries not specifically listed\r\n      if (cleanNumber.length < 7) {\r\n        return {\r\n          isValid: false,\r\n          error: \"Phone number too short (minimum 7 digits)\",\r\n        };\r\n      }\r\n      if (cleanNumber.length > 15) {\r\n        return {\r\n          isValid: false,\r\n          error: \"Phone number too long (maximum 15 digits)\",\r\n        };\r\n      }\r\n    }\r\n\r\n    return { isValid: true, error: null };\r\n  };\r\n\r\n  const isValidWhatsAppNumber = (countryCode, phoneNumber) => {\r\n    const validation = validateWhatsAppNumber(countryCode, phoneNumber);\r\n    return validation.isValid;\r\n  };\r\n\r\n  const getValidationError = (countryCode, phoneNumber) => {\r\n    const validation = validateWhatsAppNumber(countryCode, phoneNumber);\r\n    return validation.error;\r\n  };\r\n\r\n  const handleDone = async () => {\r\n    // Validate using separated country code and phone number\r\n    const validationError = getValidationError(\r\n      selectedCountryCode,\r\n      whatsAppNumber\r\n    );\r\n    if (validationError) {\r\n      toast.error(validationError, {\r\n        position: \"top-center\",\r\n        autoClose: 3000,\r\n      });\r\n      return;\r\n    }\r\n\r\n    // Combine country code with number for storage\r\n    const fullNumber = selectedCountryCode + whatsAppNumber.replace(/^\\s+/, \"\");\r\n\r\n    if (!contactName.trim()) {\r\n      toast.error(\"Contact name is required\", {\r\n        position: \"top-center\",\r\n        autoClose: 2000,\r\n      });\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n    let response;\r\n\r\n    try {\r\n      if (editingContact) {\r\n        response = await EditContact({\r\n          Id: editingContact.Id,\r\n          ContactInfo: fullNumber,\r\n          Category: \"WhatsApp\",\r\n          Title: contactName.trim(),\r\n          isPublic: true,\r\n        });\r\n      } else {\r\n        response = await CreateContact({\r\n          UserId: Id,\r\n          ContactInfo: fullNumber,\r\n          Category: \"WhatsApp\",\r\n          Title: contactName.trim(),\r\n          isPublic: true,\r\n        });\r\n      }\r\n\r\n      localStorage.setItem(\"isLinksCardVisible\", \"true\");\r\n\r\n      setContactName(\"\");\r\n      setWhatsAppNumber(\"\");\r\n      setContactName(\"\");\r\n      if (clearEditingContact) clearEditingContact();\r\n      setOpenWhatsAppDialog(false);\r\n\r\n      if (response) {\r\n        toast.success(\r\n          editingContact\r\n            ? \"WhatsApp contact updated\"\r\n            : \"WhatsApp contact added\",\r\n          {\r\n            position: \"top-center\",\r\n            autoClose: 1000,\r\n          }\r\n        );\r\n        if (fetchProfile) fetchProfile();\r\n      } else {\r\n        toast.error(\"Error while saving WhatsApp contact\", {\r\n          position: \"top-center\",\r\n          autoClose: 1000,\r\n        });\r\n      }\r\n    } catch (error) {\r\n      toast.error(\"Error while saving WhatsApp contact\", {\r\n        position: \"top-center\",\r\n        autoClose: 1000,\r\n      });\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Dialog\r\n      open={openWhatsAppDialog}\r\n      onClose={() => {\r\n        setWhatsAppNumber(\"\");\r\n        setContactName(\"\");\r\n        if (clearEditingContact) clearEditingContact();\r\n        setOpenWhatsAppDialog(false);\r\n      }}\r\n    >\r\n      <DialogTitle>\r\n        {editingContact ? \"Edit WhatsApp Contact\" : \"Add WhatsApp Contact\"}\r\n      </DialogTitle>\r\n      <DialogContent>\r\n        <TextField\r\n          name=\"contactName\"\r\n          autoFocus\r\n          margin=\"dense\"\r\n          label=\"Contact Name\"\r\n          type=\"text\"\r\n          fullWidth\r\n          required\r\n          value={contactName}\r\n          onChange={handleContactNameChange}\r\n          helperText={contactName === \"\" ? \"Contact name is required\" : \"\"}\r\n          sx={{ mb: 2 }}\r\n        />\r\n        <Grid container spacing={2}>\r\n          <Grid item xs={12} sm={4}>\r\n            <FormControl fullWidth margin=\"dense\">\r\n              <InputLabel>Country</InputLabel>\r\n              <Select\r\n                value={selectedCountryCode}\r\n                onChange={(e) => setSelectedCountryCode(e.target.value)}\r\n                label=\"Country\"\r\n                sx={{\r\n                  \"& .MuiSelect-select\": {\r\n                    padding: { xs: \"12px 14px\", sm: \"16.5px 14px\" },\r\n                    fontSize: { xs: \"0.875rem\", sm: \"1rem\" },\r\n                  },\r\n                }}\r\n                renderValue={(value) => {\r\n                  const country = countryCodes.find((c) => c.code === value);\r\n                  return country ? (\r\n                    <Box\r\n                      sx={{\r\n                        display: \"flex\",\r\n                        alignItems: \"center\",\r\n                        gap: { xs: 0.5, sm: 1 },\r\n                        minWidth: 0,\r\n                      }}\r\n                    >\r\n                      <img\r\n                        src={country.flag}\r\n                        alt={country.country}\r\n                        style={{\r\n                          width: 20,\r\n                          height: 15,\r\n                          flexShrink: 0,\r\n                        }}\r\n                      />\r\n                      <span\r\n                        style={{\r\n                          fontSize: \"inherit\",\r\n                          overflow: \"hidden\",\r\n                          textOverflow: \"ellipsis\",\r\n                          whiteSpace: \"nowrap\",\r\n                        }}\r\n                      >\r\n                        {value}\r\n                      </span>\r\n                    </Box>\r\n                  ) : (\r\n                    value\r\n                  );\r\n                }}\r\n              >\r\n                {countryCodes.map((country, index) => (\r\n                  <MenuItem\r\n                    key={`${country.code}-${index}`}\r\n                    value={country.code}\r\n                    sx={{\r\n                      padding: { xs: \"8px 16px\", sm: \"6px 16px\" },\r\n                      minHeight: { xs: \"48px\", sm: \"auto\" },\r\n                    }}\r\n                  >\r\n                    <Box\r\n                      sx={{\r\n                        display: \"flex\",\r\n                        alignItems: \"center\",\r\n                        gap: 1,\r\n                        width: \"100%\",\r\n                        minWidth: 0,\r\n                      }}\r\n                    >\r\n                      <img\r\n                        src={country.flag}\r\n                        alt={country.country}\r\n                        style={{\r\n                          width: 20,\r\n                          height: 15,\r\n                          flexShrink: 0,\r\n                        }}\r\n                      />\r\n                      <span\r\n                        style={{\r\n                          fontWeight: 500,\r\n                          flexShrink: 0,\r\n                        }}\r\n                      >\r\n                        {country.code}\r\n                      </span>\r\n                      <span\r\n                        style={{\r\n                          fontSize: \"0.875rem\",\r\n                          color: \"#666\",\r\n                          overflow: \"hidden\",\r\n                          textOverflow: \"ellipsis\",\r\n                          whiteSpace: \"nowrap\",\r\n                          flex: 1,\r\n                          minWidth: 0,\r\n                        }}\r\n                      >\r\n                        {country.country}\r\n                      </span>\r\n                    </Box>\r\n                  </MenuItem>\r\n                ))}\r\n              </Select>\r\n            </FormControl>\r\n          </Grid>\r\n          <Grid item xs={12} sm={8}>\r\n            <TextField\r\n              name=\"WhatsAppNumber\"\r\n              margin=\"dense\"\r\n              label=\"Phone Number\"\r\n              type=\"tel\"\r\n              fullWidth\r\n              required\r\n              value={whatsAppNumber}\r\n              onChange={(e) =>\r\n                setWhatsAppNumber(e.target.value.replace(/[^\\d\\s\\-\\(\\)]/g, \"\"))\r\n              }\r\n              error={\r\n                whatsAppNumber !== \"\" &&\r\n                !isValidWhatsAppNumber(selectedCountryCode, whatsAppNumber)\r\n              }\r\n              helperText={\r\n                whatsAppNumber === \"\"\r\n                  ? \"Phone number is required\"\r\n                  : whatsAppNumber !== \"\" &&\r\n                    !isValidWhatsAppNumber(selectedCountryCode, whatsAppNumber)\r\n                  ? getValidationError(selectedCountryCode, whatsAppNumber)\r\n                  : \"✓ Valid WhatsApp number\"\r\n              }\r\n              placeholder=\"************\"\r\n              inputProps={{\r\n                maxLength: 15,\r\n              }}\r\n              sx={{\r\n                \"& .MuiInputBase-input\": {\r\n                  fontSize: { xs: \"16px\", sm: \"1rem\" }, // Prevents zoom on iOS\r\n                  padding: { xs: \"12px 14px\", sm: \"16.5px 14px\" },\r\n                },\r\n                \"& .MuiFormHelperText-root\": {\r\n                  fontSize: { xs: \"0.75rem\", sm: \"0.75rem\" },\r\n                  marginTop: { xs: \"4px\", sm: \"3px\" },\r\n                },\r\n              }}\r\n            />\r\n          </Grid>\r\n        </Grid>\r\n\r\n        {/* Preview of full number */}\r\n        {whatsAppNumber && (\r\n          <Box\r\n            sx={{\r\n              mt: { xs: 2, sm: 1 },\r\n              mb: { xs: 2, sm: 1 },\r\n              display: \"flex\",\r\n              justifyContent: { xs: \"center\", sm: \"flex-start\" },\r\n            }}\r\n          >\r\n            <Chip\r\n              label={`Full Number: ${selectedCountryCode} ${whatsAppNumber}`}\r\n              variant=\"outlined\"\r\n              size=\"small\"\r\n              color={\r\n                isValidWhatsAppNumber(selectedCountryCode, whatsAppNumber)\r\n                  ? \"success\"\r\n                  : \"default\"\r\n              }\r\n              sx={{\r\n                fontSize: { xs: \"0.75rem\", sm: \"0.8125rem\" },\r\n                height: { xs: \"28px\", sm: \"24px\" },\r\n                \"& .MuiChip-label\": {\r\n                  padding: { xs: \"0 8px\", sm: \"0 12px\" },\r\n                },\r\n              }}\r\n            />\r\n          </Box>\r\n        )}\r\n        {/* Hints and Tips Section */}\r\n        <Box\r\n          mt={2}\r\n          p={2}\r\n          sx={{\r\n            backgroundColor: \"#f0f0f0\",\r\n            borderRadius: \"5px\",\r\n          }}\r\n        >\r\n          <Typography variant=\"subtitle1\" color=\"textPrimary\">\r\n            How to Add WhatsApp Contact\r\n          </Typography>\r\n          <Typography variant=\"body2\" color=\"textSecondary\">\r\n            • <strong>Select your country:</strong> Choose from the dropdown\r\n            list with flags\r\n          </Typography>\r\n          <Typography variant=\"body2\" color=\"textSecondary\">\r\n            • <strong>Enter phone number:</strong> Only digits, minimum 8 digits\r\n            (e.g., 23456789)\r\n          </Typography>\r\n          <Typography variant=\"body2\" color=\"textSecondary\">\r\n            • <strong>No spaces or symbols:</strong> Just the numbers without\r\n            any formatting\r\n          </Typography>\r\n          <Typography variant=\"body2\" color=\"textSecondary\">\r\n            • <strong>Preview:</strong> See the full international number below\r\n          </Typography>\r\n          <Typography variant=\"body2\" color=\"textSecondary\">\r\n            • <strong>Multiple contacts:</strong> Add different WhatsApp numbers\r\n            with unique names\r\n          </Typography>\r\n        </Box>\r\n      </DialogContent>\r\n      <DialogActions>\r\n        <Button\r\n          onClick={() => {\r\n            setContactName(\"\");\r\n            setWhatsAppNumber(\"\");\r\n            setContactName(\"\");\r\n            if (clearEditingContact) clearEditingContact();\r\n            setOpenWhatsAppDialog(false);\r\n          }}\r\n        >\r\n          Cancel\r\n        </Button>\r\n        <Button\r\n          onClick={handleDone}\r\n          disabled={\r\n            whatsAppNumber === \"\" ||\r\n            !isValidWhatsAppNumber(selectedCountryCode, whatsAppNumber) ||\r\n            contactName.trim() === \"\" ||\r\n            isLoading\r\n          }\r\n        >\r\n          {isLoading ? \"Saving...\" : editingContact ? \"Update\" : \"Add\"}\r\n        </Button>\r\n      </DialogActions>\r\n    </Dialog>\r\n  );\r\n};\r\n\r\nexport default WhatsAppLinkDialog;\r\n"], "mappings": "AAAA,OAASA,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAC3C,OACEC,SAAS,CACTC,MAAM,CACNC,MAAM,CACNC,WAAW,CACXC,aAAa,CACbC,aAAa,CACbC,GAAG,CACHC,UAAU,CACVC,MAAM,CACNC,QAAQ,CACRC,WAAW,CACXC,UAAU,CACVC,IAAI,CACJC,IAAI,KACC,eAAe,CAEtB,OAASC,aAAa,CAAEC,WAAW,KAAQ,yBAAyB,CAEpE,OAASC,KAAK,KAAQ,gBAAgB,CAEtC;AAAA,OAAAC,GAAA,IAAAC,IAAA,gCAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,YAAY,CAAG,CACnB,CACEC,IAAI,CAAE,IAAI,CACVC,OAAO,CAAE,eAAe,CACxBC,IAAI,CAAE,gCACR,CAAC,CACD,CAAEF,IAAI,CAAE,IAAI,CAAEC,OAAO,CAAE,QAAQ,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CACzE,CACEF,IAAI,CAAE,KAAK,CACXC,OAAO,CAAE,gBAAgB,CACzBC,IAAI,CAAE,gCACR,CAAC,CACD,CAAEF,IAAI,CAAE,KAAK,CAAEC,OAAO,CAAE,SAAS,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CAC3E,CAAEF,IAAI,CAAE,KAAK,CAAEC,OAAO,CAAE,QAAQ,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CAC1E,CAAEF,IAAI,CAAE,KAAK,CAAEC,OAAO,CAAE,OAAO,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CACzE,CAAEF,IAAI,CAAE,KAAK,CAAEC,OAAO,CAAE,OAAO,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CACzE,CACEF,IAAI,CAAE,KAAK,CACXC,OAAO,CAAE,aAAa,CACtBC,IAAI,CAAE,gCACR,CAAC,CACD,CACEF,IAAI,CAAE,KAAK,CACXC,OAAO,CAAE,aAAa,CACtBC,IAAI,CAAE,gCACR,CAAC,CACD,CAAEF,IAAI,CAAE,KAAK,CAAEC,OAAO,CAAE,SAAS,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CAC3E,CAAEF,IAAI,CAAE,KAAK,CAAEC,OAAO,CAAE,SAAS,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CAC3E,CAAEF,IAAI,CAAE,KAAK,CAAEC,OAAO,CAAE,SAAS,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CAC3E,CAAEF,IAAI,CAAE,KAAK,CAAEC,OAAO,CAAE,QAAQ,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CAC1E,CAAEF,IAAI,CAAE,KAAK,CAAEC,OAAO,CAAE,QAAQ,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CAC1E,CAAEF,IAAI,CAAE,MAAM,CAAEC,OAAO,CAAE,SAAS,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CAC5E,CAAEF,IAAI,CAAE,KAAK,CAAEC,OAAO,CAAE,OAAO,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CACzE,CAAEF,IAAI,CAAE,KAAK,CAAEC,OAAO,CAAE,OAAO,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CACzE,CAAEF,IAAI,CAAE,KAAK,CAAEC,OAAO,CAAE,OAAO,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CACzE,CACEF,IAAI,CAAE,KAAK,CACXC,OAAO,CAAE,aAAa,CACtBC,IAAI,CAAE,gCACR,CAAC,CACD,CAAEF,IAAI,CAAE,KAAK,CAAEC,OAAO,CAAE,WAAW,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CAC7E,CAAEF,IAAI,CAAE,KAAK,CAAEC,OAAO,CAAE,UAAU,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CAC5E,CAAEF,IAAI,CAAE,KAAK,CAAEC,OAAO,CAAE,UAAU,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CAC5E,CAAEF,IAAI,CAAE,KAAK,CAAEC,OAAO,CAAE,SAAS,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CAC3E,CACEF,IAAI,CAAE,KAAK,CACXC,OAAO,CAAE,aAAa,CACtBC,IAAI,CAAE,gCACR,CAAC,CACD,CAAEF,IAAI,CAAE,KAAK,CAAEC,OAAO,CAAE,WAAW,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CAC7E,CAAEF,IAAI,CAAE,KAAK,CAAEC,OAAO,CAAE,WAAW,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CAC7E,CACEF,IAAI,CAAE,KAAK,CACXC,OAAO,CAAE,aAAa,CACtBC,IAAI,CAAE,gCACR,CAAC,CACD,CACEF,IAAI,CAAE,KAAK,CACXC,OAAO,CAAE,cAAc,CACvBC,IAAI,CAAE,gCACR,CAAC,CACD,CAAEF,IAAI,CAAE,KAAK,CAAEC,OAAO,CAAE,OAAO,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CACzE,CAAEF,IAAI,CAAE,MAAM,CAAEC,OAAO,CAAE,SAAS,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CAC5E,CAAEF,IAAI,CAAE,MAAM,CAAEC,OAAO,CAAE,SAAS,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CAC5E,CAAEF,IAAI,CAAE,MAAM,CAAEC,OAAO,CAAE,OAAO,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CAC1E,CAAEF,IAAI,CAAE,MAAM,CAAEC,OAAO,CAAE,KAAK,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CACxE,CACEF,IAAI,CAAE,MAAM,CACZC,OAAO,CAAE,cAAc,CACvBC,IAAI,CAAE,gCACR,CAAC,CACD,CAAEF,IAAI,CAAE,MAAM,CAAEC,OAAO,CAAE,OAAO,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CAC1E,CAAEF,IAAI,CAAE,MAAM,CAAEC,OAAO,CAAE,QAAQ,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CAC3E,CAAEF,IAAI,CAAE,MAAM,CAAEC,OAAO,CAAE,SAAS,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CAC5E,CAAEF,IAAI,CAAE,MAAM,CAAEC,OAAO,CAAE,MAAM,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CACzE,CAAEF,IAAI,CAAE,MAAM,CAAEC,OAAO,CAAE,QAAQ,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CAC3E,CAAEF,IAAI,CAAE,KAAK,CAAEC,OAAO,CAAE,QAAQ,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CAC1E,CAAEF,IAAI,CAAE,IAAI,CAAEC,OAAO,CAAE,QAAQ,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CACzE,CAAEF,IAAI,CAAE,MAAM,CAAEC,OAAO,CAAE,SAAS,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CAC5E,CAAEF,IAAI,CAAE,KAAK,CAAEC,OAAO,CAAE,QAAQ,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CAC1E,CACEF,IAAI,CAAE,MAAM,CACZC,OAAO,CAAE,gBAAgB,CACzBC,IAAI,CAAE,gCACR,CAAC,CACD,CAAEF,IAAI,CAAE,KAAK,CAAEC,OAAO,CAAE,SAAS,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CAC3E,CAAEF,IAAI,CAAE,KAAK,CAAEC,OAAO,CAAE,SAAS,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CAC3E,CAAEF,IAAI,CAAE,MAAM,CAAEC,OAAO,CAAE,UAAU,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CAC7E,CAAEF,IAAI,CAAE,MAAM,CAAEC,OAAO,CAAE,SAAS,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CAC5E,CAAEF,IAAI,CAAE,MAAM,CAAEC,OAAO,CAAE,QAAQ,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CAC3E,CAAEF,IAAI,CAAE,KAAK,CAAEC,OAAO,CAAE,QAAQ,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CAC1E,CAAEF,IAAI,CAAE,KAAK,CAAEC,OAAO,CAAE,QAAQ,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CAC1E,CAAEF,IAAI,CAAE,KAAK,CAAEC,OAAO,CAAE,WAAW,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CAC7E,CAAEF,IAAI,CAAE,KAAK,CAAEC,OAAO,CAAE,OAAO,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CACzE,CAAEF,IAAI,CAAE,KAAK,CAAEC,OAAO,CAAE,UAAU,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CAC5E,CAAEF,IAAI,CAAE,KAAK,CAAEC,OAAO,CAAE,MAAM,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CACxE,CAAEF,IAAI,CAAE,KAAK,CAAEC,OAAO,CAAE,WAAW,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CAC7E,CAAEF,IAAI,CAAE,MAAM,CAAEC,OAAO,CAAE,SAAS,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CAC5E,CAAEF,IAAI,CAAE,MAAM,CAAEC,OAAO,CAAE,UAAU,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CAC7E,CAAEF,IAAI,CAAE,MAAM,CAAEC,OAAO,CAAE,SAAS,CAAEC,IAAI,CAAE,gCAAiC,CAAC,CAC7E,CAED,KAAM,CAAAC,kBAAkB,CAAGC,IAAA,EAOrB,IAPsB,CAC1BC,qBAAqB,CACrBC,kBAAkB,CAClBC,EAAE,CACFC,cAAc,CAAG,IAAI,CACrBC,YAAY,CACZC,mBACF,CAAC,CAAAN,IAAA,CACC,KAAM,CAACO,cAAc,CAAEC,iBAAiB,CAAC,CAAGpC,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAACqC,WAAW,CAAEC,cAAc,CAAC,CAAGtC,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACuC,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGxC,QAAQ,CAAC,IAAI,CAAC,CACpE,KAAM,CAACyC,SAAS,CAAEC,YAAY,CAAC,CAAG1C,QAAQ,CAAC,KAAK,CAAC,CAEjD;AACA,KAAM,CAAA2C,kBAAkB,CAAIC,WAAW,EAAK,CAC1C,GAAI,CAACA,WAAW,EAAI,CAACA,WAAW,CAACC,UAAU,CAAC,GAAG,CAAC,CAAE,CAChD,MAAO,IAAI,CAAE;AACf,CAEA;AACA,KAAM,CAAAC,WAAW,CAAGvB,YAAY,CAC7BwB,GAAG,CAAEC,CAAC,EAAKA,CAAC,CAACxB,IAAI,CAAC,CAClByB,MAAM,CAAC,CAACzB,IAAI,CAAE0B,KAAK,CAAEC,GAAG,GAAKA,GAAG,CAACC,OAAO,CAAC5B,IAAI,CAAC,GAAK0B,KAAK,CAAE;AAAA,CAC1DG,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKA,CAAC,CAACC,MAAM,CAAGF,CAAC,CAACE,MAAM,CAAC,CAEtC,IAAK,KAAM,CAAAhC,IAAI,GAAI,CAAAsB,WAAW,CAAE,CAC9B,GAAIF,WAAW,CAACC,UAAU,CAACrB,IAAI,CAAC,CAAE,CAChC,MAAO,CAAAA,IAAI,CACb,CACF,CAEA,MAAO,IAAI,CAAE;AACf,CAAC,CAED;AACA,KAAM,CAAAiC,wBAAwB,CAAGA,CAACb,WAAW,CAAEc,WAAW,GAAK,CAC7D,GAAI,CAACd,WAAW,EAAI,CAACA,WAAW,CAACC,UAAU,CAACa,WAAW,CAAC,CAAE,CACxD,MAAO,EAAE,CACX,CACA,MAAO,CAAAd,WAAW,CAACe,SAAS,CAACD,WAAW,CAACF,MAAM,CAAC,CAACI,OAAO,CAAC,MAAM,CAAE,EAAE,CAAC,CACtE,CAAC,CAED3D,SAAS,CAAC,IAAM,CACd,GAAI+B,cAAc,CAAE,CAClB,KAAM,CAAA6B,UAAU,CAAG7B,cAAc,CAAC8B,OAAO,EAAI,EAAE,CAC/C,KAAM,CAAAC,aAAa,CAAGpB,kBAAkB,CAACkB,UAAU,CAAC,CACpD,KAAM,CAAAG,iBAAiB,CAAGP,wBAAwB,CAChDI,UAAU,CACVE,aACF,CAAC,CAEDvB,sBAAsB,CAACuB,aAAa,CAAC,CACrC3B,iBAAiB,CAAC4B,iBAAiB,CAAC,CACpC1B,cAAc,CAACN,cAAc,CAACiC,KAAK,EAAI,EAAE,CAAC,CAC5C,CAAC,IAAM,CACLzB,sBAAsB,CAAC,IAAI,CAAC,CAC5BJ,iBAAiB,CAAC,EAAE,CAAC,CACrBE,cAAc,CAAC,EAAE,CAAC,CACpB,CACF,CAAC,CAAE,CAACN,cAAc,CAAEF,kBAAkB,CAAC,CAAC,CAExC,KAAM,CAAAoC,uBAAuB,CAAIC,KAAK,EAAK,CACzC7B,cAAc,CAAC6B,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC,CACpC,CAAC,CAED,KAAM,CAAAC,sBAAsB,CAAGA,CAACZ,WAAW,CAAEd,WAAW,GAAK,CAC3D,GAAI,CAACA,WAAW,EAAIA,WAAW,CAAC2B,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CAC7C,MAAO,CAAEC,OAAO,CAAE,KAAK,CAAEC,KAAK,CAAE,0BAA2B,CAAC,CAC9D,CAEA,GAAI,CAACf,WAAW,CAAE,CAChB,MAAO,CAAEc,OAAO,CAAE,KAAK,CAAEC,KAAK,CAAE,0BAA2B,CAAC,CAC9D,CAEA;AACA,KAAM,CAAAC,WAAW,CAAG9B,WAAW,CAACgB,OAAO,CAAC,eAAe,CAAE,EAAE,CAAC,CAE5D;AACA,GAAI,CAAC,OAAO,CAACe,IAAI,CAACD,WAAW,CAAC,CAAE,CAC9B,MAAO,CACLF,OAAO,CAAE,KAAK,CACdC,KAAK,CACH,yEACJ,CAAC,CACH,CAEA;AACA,KAAM,CAAAG,iBAAiB,CAAG,CACxB,IAAI,CAAE,CACJ;AACAC,SAAS,CAAE,EAAE,CACbC,SAAS,CAAE,EAAE,CACbC,OAAO,CAAE,cAAc,CACvBC,QAAQ,CAAE,yDACZ,CAAC,CACD,KAAK,CAAE,CACL;AACAH,SAAS,CAAE,EAAE,CACbC,SAAS,CAAE,EAAE,CACbC,OAAO,CAAE,iBAAiB,CAC1BC,QAAQ,CAAE,mCACZ,CAAC,CACD,KAAK,CAAE,CACL;AACAH,SAAS,CAAE,EAAE,CACbC,SAAS,CAAE,EAAE,CACbC,OAAO,CAAE,iBAAiB,CAC1BC,QAAQ,CAAE,uCACZ,CAAC,CACD,KAAK,CAAE,CACL;AACAH,SAAS,CAAE,CAAC,CACZC,SAAS,CAAE,CAAC,CACZC,OAAO,CAAE,cAAc,CACvBC,QAAQ,CAAE,qDACZ,CAAC,CACD,KAAK,CAAE,CACL;AACAH,SAAS,CAAE,CAAC,CACZC,SAAS,CAAE,EAAE,CACbC,OAAO,CAAE,iBAAiB,CAC1BC,QAAQ,CAAE,uCACZ,CAAC,CACD,KAAK,CAAE,CACL;AACAH,SAAS,CAAE,CAAC,CACZC,SAAS,CAAE,CAAC,CACZC,OAAO,CAAE,cAAc,CACvBC,QAAQ,CAAE,6DACZ,CAAC,CACD,KAAK,CAAE,CACL;AACAH,SAAS,CAAE,EAAE,CACbC,SAAS,CAAE,EAAE,CACbC,OAAO,CAAE,cAAc,CACvBC,QAAQ,CAAE,6DACZ,CAAC,CACD,KAAK,CAAE,CACL;AACAH,SAAS,CAAE,EAAE,CACbC,SAAS,CAAE,EAAE,CACbC,OAAO,CAAE,eAAe,CACxBC,QAAQ,CACN,gEACJ,CAAC,CACD,KAAK,CAAE,CACL;AACAH,SAAS,CAAE,EAAE,CACbC,SAAS,CAAE,EAAE,CACbC,OAAO,CAAE,iBAAiB,CAC1BC,QAAQ,CACN,kEACJ,CAAC,CACD,KAAK,CAAE,CACL;AACAH,SAAS,CAAE,CAAC,CACZC,SAAS,CAAE,EAAE,CACbC,OAAO,CAAE,iBAAiB,CAC1BC,QAAQ,CACN,iEACJ,CAAC,CACD,MAAM,CAAE,CACN;AACAH,SAAS,CAAE,CAAC,CACZC,SAAS,CAAE,CAAC,CACZC,OAAO,CAAE,UAAU,CACnBC,QAAQ,CAAE,uDACZ,CAAC,CACD,MAAM,CAAE,CACN;AACAH,SAAS,CAAE,CAAC,CACZC,SAAS,CAAE,CAAC,CACZC,OAAO,CAAE,UAAU,CACnBC,QAAQ,CAAE,yDACZ,CAAC,CACD,KAAK,CAAE,CACL;AACAH,SAAS,CAAE,EAAE,CACbC,SAAS,CAAE,EAAE,CACbC,OAAO,CAAE,iBAAiB,CAC1BC,QAAQ,CAAE,iDACZ,CAAC,CACD,KAAK,CAAE,CACL;AACAH,SAAS,CAAE,EAAE,CACbC,SAAS,CAAE,EAAE,CACbC,OAAO,CAAE,cAAc,CACvBC,QAAQ,CAAE,4CACZ,CAAC,CACD,MAAM,CAAE,CACN;AACAH,SAAS,CAAE,CAAC,CACZC,SAAS,CAAE,CAAC,CACZC,OAAO,CAAE,YAAY,CACrBC,QAAQ,CAAE,oDACZ,CACF,CAAC,CAED,KAAM,CAAAC,UAAU,CAAGL,iBAAiB,CAAClB,WAAW,CAAC,CAEjD,GAAIuB,UAAU,CAAE,CACd;AACA,GAAIP,WAAW,CAAClB,MAAM,CAAGyB,UAAU,CAACJ,SAAS,CAAE,CAC7C,MAAO,CACLL,OAAO,CAAE,KAAK,CACdC,KAAK,CAAE,qBAAqBQ,UAAU,CAACD,QAAQ,EACjD,CAAC,CACH,CACA,GAAIN,WAAW,CAAClB,MAAM,CAAGyB,UAAU,CAACH,SAAS,CAAE,CAC7C,MAAO,CACLN,OAAO,CAAE,KAAK,CACdC,KAAK,CAAE,oBAAoBQ,UAAU,CAACD,QAAQ,EAChD,CAAC,CACH,CAEA;AACA,GAAI,CAACC,UAAU,CAACF,OAAO,CAACJ,IAAI,CAACD,WAAW,CAAC,CAAE,CACzC,MAAO,CAAEF,OAAO,CAAE,KAAK,CAAEC,KAAK,CAAEQ,UAAU,CAACD,QAAS,CAAC,CACvD,CACF,CAAC,IAAM,CACL;AACA,GAAIN,WAAW,CAAClB,MAAM,CAAG,CAAC,CAAE,CAC1B,MAAO,CACLgB,OAAO,CAAE,KAAK,CACdC,KAAK,CAAE,2CACT,CAAC,CACH,CACA,GAAIC,WAAW,CAAClB,MAAM,CAAG,EAAE,CAAE,CAC3B,MAAO,CACLgB,OAAO,CAAE,KAAK,CACdC,KAAK,CAAE,2CACT,CAAC,CACH,CACF,CAEA,MAAO,CAAED,OAAO,CAAE,IAAI,CAAEC,KAAK,CAAE,IAAK,CAAC,CACvC,CAAC,CAED,KAAM,CAAAS,qBAAqB,CAAGA,CAACxB,WAAW,CAAEd,WAAW,GAAK,CAC1D,KAAM,CAAAqC,UAAU,CAAGX,sBAAsB,CAACZ,WAAW,CAAEd,WAAW,CAAC,CACnE,MAAO,CAAAqC,UAAU,CAACT,OAAO,CAC3B,CAAC,CAED,KAAM,CAAAW,kBAAkB,CAAGA,CAACzB,WAAW,CAAEd,WAAW,GAAK,CACvD,KAAM,CAAAqC,UAAU,CAAGX,sBAAsB,CAACZ,WAAW,CAAEd,WAAW,CAAC,CACnE,MAAO,CAAAqC,UAAU,CAACR,KAAK,CACzB,CAAC,CAED,KAAM,CAAAW,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7B;AACA,KAAM,CAAAC,eAAe,CAAGF,kBAAkB,CACxC5C,mBAAmB,CACnBJ,cACF,CAAC,CACD,GAAIkD,eAAe,CAAE,CACnBnE,KAAK,CAACuD,KAAK,CAACY,eAAe,CAAE,CAC3BC,QAAQ,CAAE,YAAY,CACtBC,SAAS,CAAE,IACb,CAAC,CAAC,CACF,OACF,CAEA;AACA,KAAM,CAAA1B,UAAU,CAAGtB,mBAAmB,CAAGJ,cAAc,CAACyB,OAAO,CAAC,MAAM,CAAE,EAAE,CAAC,CAE3E,GAAI,CAACvB,WAAW,CAACkC,IAAI,CAAC,CAAC,CAAE,CACvBrD,KAAK,CAACuD,KAAK,CAAC,0BAA0B,CAAE,CACtCa,QAAQ,CAAE,YAAY,CACtBC,SAAS,CAAE,IACb,CAAC,CAAC,CACF,OACF,CAEA7C,YAAY,CAAC,IAAI,CAAC,CAClB,GAAI,CAAA8C,QAAQ,CAEZ,GAAI,CACF,GAAIxD,cAAc,CAAE,CAClBwD,QAAQ,CAAG,KAAM,CAAAvE,WAAW,CAAC,CAC3Bc,EAAE,CAAEC,cAAc,CAACD,EAAE,CACrB0D,WAAW,CAAE5B,UAAU,CACvB6B,QAAQ,CAAE,UAAU,CACpBzB,KAAK,CAAE5B,WAAW,CAACkC,IAAI,CAAC,CAAC,CACzBoB,QAAQ,CAAE,IACZ,CAAC,CAAC,CACJ,CAAC,IAAM,CACLH,QAAQ,CAAG,KAAM,CAAAxE,aAAa,CAAC,CAC7B4E,MAAM,CAAE7D,EAAE,CACV0D,WAAW,CAAE5B,UAAU,CACvB6B,QAAQ,CAAE,UAAU,CACpBzB,KAAK,CAAE5B,WAAW,CAACkC,IAAI,CAAC,CAAC,CACzBoB,QAAQ,CAAE,IACZ,CAAC,CAAC,CACJ,CAEAE,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAE,MAAM,CAAC,CAElDxD,cAAc,CAAC,EAAE,CAAC,CAClBF,iBAAiB,CAAC,EAAE,CAAC,CACrBE,cAAc,CAAC,EAAE,CAAC,CAClB,GAAIJ,mBAAmB,CAAEA,mBAAmB,CAAC,CAAC,CAC9CL,qBAAqB,CAAC,KAAK,CAAC,CAE5B,GAAI2D,QAAQ,CAAE,CACZtE,KAAK,CAAC6E,OAAO,CACX/D,cAAc,CACV,0BAA0B,CAC1B,wBAAwB,CAC5B,CACEsD,QAAQ,CAAE,YAAY,CACtBC,SAAS,CAAE,IACb,CACF,CAAC,CACD,GAAItD,YAAY,CAAEA,YAAY,CAAC,CAAC,CAClC,CAAC,IAAM,CACLf,KAAK,CAACuD,KAAK,CAAC,qCAAqC,CAAE,CACjDa,QAAQ,CAAE,YAAY,CACtBC,SAAS,CAAE,IACb,CAAC,CAAC,CACJ,CACF,CAAE,MAAOd,KAAK,CAAE,CACdvD,KAAK,CAACuD,KAAK,CAAC,qCAAqC,CAAE,CACjDa,QAAQ,CAAE,YAAY,CACtBC,SAAS,CAAE,IACb,CAAC,CAAC,CACJ,CAAC,OAAS,CACR7C,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAED,mBACEpB,KAAA,CAAClB,MAAM,EACL4F,IAAI,CAAElE,kBAAmB,CACzBmE,OAAO,CAAEA,CAAA,GAAM,CACb7D,iBAAiB,CAAC,EAAE,CAAC,CACrBE,cAAc,CAAC,EAAE,CAAC,CAClB,GAAIJ,mBAAmB,CAAEA,mBAAmB,CAAC,CAAC,CAC9CL,qBAAqB,CAAC,KAAK,CAAC,CAC9B,CAAE,CAAAqE,QAAA,eAEF9E,IAAA,CAACf,WAAW,EAAA6F,QAAA,CACTlE,cAAc,CAAG,uBAAuB,CAAG,sBAAsB,CACvD,CAAC,cACdV,KAAA,CAAChB,aAAa,EAAA4F,QAAA,eACZ9E,IAAA,CAAClB,SAAS,EACRiG,IAAI,CAAC,aAAa,CAClBC,SAAS,MACTC,MAAM,CAAC,OAAO,CACdC,KAAK,CAAC,cAAc,CACpBC,IAAI,CAAC,MAAM,CACXC,SAAS,MACTC,QAAQ,MACRpC,KAAK,CAAEhC,WAAY,CACnBqE,QAAQ,CAAExC,uBAAwB,CAClCyC,UAAU,CAAEtE,WAAW,GAAK,EAAE,CAAG,0BAA0B,CAAG,EAAG,CACjEuE,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CACf,CAAC,cACFvF,KAAA,CAACR,IAAI,EAACgG,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAb,QAAA,eACzB9E,IAAA,CAACN,IAAI,EAACkG,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAhB,QAAA,cACvB5E,KAAA,CAACV,WAAW,EAAC4F,SAAS,MAACH,MAAM,CAAC,OAAO,CAAAH,QAAA,eACnC9E,IAAA,CAACP,UAAU,EAAAqF,QAAA,CAAC,SAAO,CAAY,CAAC,cAChC9E,IAAA,CAACV,MAAM,EACL2D,KAAK,CAAE9B,mBAAoB,CAC3BmE,QAAQ,CAAGS,CAAC,EAAK3E,sBAAsB,CAAC2E,CAAC,CAAC/C,MAAM,CAACC,KAAK,CAAE,CACxDiC,KAAK,CAAC,SAAS,CACfM,EAAE,CAAE,CACF,qBAAqB,CAAE,CACrBQ,OAAO,CAAE,CAAEH,EAAE,CAAE,WAAW,CAAEC,EAAE,CAAE,aAAc,CAAC,CAC/CG,QAAQ,CAAE,CAAEJ,EAAE,CAAE,UAAU,CAAEC,EAAE,CAAE,MAAO,CACzC,CACF,CAAE,CACFI,WAAW,CAAGjD,KAAK,EAAK,CACtB,KAAM,CAAA5C,OAAO,CAAGF,YAAY,CAACgG,IAAI,CAAEvE,CAAC,EAAKA,CAAC,CAACxB,IAAI,GAAK6C,KAAK,CAAC,CAC1D,MAAO,CAAA5C,OAAO,cACZH,KAAA,CAACd,GAAG,EACFoG,EAAE,CAAE,CACFY,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,GAAG,CAAE,CAAET,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,CAAE,CAAC,CACvBS,QAAQ,CAAE,CACZ,CAAE,CAAAzB,QAAA,eAEF9E,IAAA,QACEwG,GAAG,CAAEnG,OAAO,CAACC,IAAK,CAClBmG,GAAG,CAAEpG,OAAO,CAACA,OAAQ,CACrBqG,KAAK,CAAE,CACLC,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,EAAE,CACVC,UAAU,CAAE,CACd,CAAE,CACH,CAAC,cACF7G,IAAA,SACE0G,KAAK,CAAE,CACLT,QAAQ,CAAE,SAAS,CACnBa,QAAQ,CAAE,QAAQ,CAClBC,YAAY,CAAE,UAAU,CACxBC,UAAU,CAAE,QACd,CAAE,CAAAlC,QAAA,CAED7B,KAAK,CACF,CAAC,EACJ,CAAC,CAENA,KACD,CACH,CAAE,CAAA6B,QAAA,CAED3E,YAAY,CAACwB,GAAG,CAAC,CAACtB,OAAO,CAAEyB,KAAK,gBAC/B9B,IAAA,CAACT,QAAQ,EAEP0D,KAAK,CAAE5C,OAAO,CAACD,IAAK,CACpBoF,EAAE,CAAE,CACFQ,OAAO,CAAE,CAAEH,EAAE,CAAE,UAAU,CAAEC,EAAE,CAAE,UAAW,CAAC,CAC3CmB,SAAS,CAAE,CAAEpB,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,MAAO,CACtC,CAAE,CAAAhB,QAAA,cAEF5E,KAAA,CAACd,GAAG,EACFoG,EAAE,CAAE,CACFY,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,GAAG,CAAE,CAAC,CACNK,KAAK,CAAE,MAAM,CACbJ,QAAQ,CAAE,CACZ,CAAE,CAAAzB,QAAA,eAEF9E,IAAA,QACEwG,GAAG,CAAEnG,OAAO,CAACC,IAAK,CAClBmG,GAAG,CAAEpG,OAAO,CAACA,OAAQ,CACrBqG,KAAK,CAAE,CACLC,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,EAAE,CACVC,UAAU,CAAE,CACd,CAAE,CACH,CAAC,cACF7G,IAAA,SACE0G,KAAK,CAAE,CACLQ,UAAU,CAAE,GAAG,CACfL,UAAU,CAAE,CACd,CAAE,CAAA/B,QAAA,CAEDzE,OAAO,CAACD,IAAI,CACT,CAAC,cACPJ,IAAA,SACE0G,KAAK,CAAE,CACLT,QAAQ,CAAE,UAAU,CACpBkB,KAAK,CAAE,MAAM,CACbL,QAAQ,CAAE,QAAQ,CAClBC,YAAY,CAAE,UAAU,CACxBC,UAAU,CAAE,QAAQ,CACpBI,IAAI,CAAE,CAAC,CACPb,QAAQ,CAAE,CACZ,CAAE,CAAAzB,QAAA,CAEDzE,OAAO,CAACA,OAAO,CACZ,CAAC,EACJ,CAAC,EA9CD,GAAGA,OAAO,CAACD,IAAI,IAAI0B,KAAK,EA+CrB,CACX,CAAC,CACI,CAAC,EACE,CAAC,CACV,CAAC,cACP9B,IAAA,CAACN,IAAI,EAACkG,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAhB,QAAA,cACvB9E,IAAA,CAAClB,SAAS,EACRiG,IAAI,CAAC,gBAAgB,CACrBE,MAAM,CAAC,OAAO,CACdC,KAAK,CAAC,cAAc,CACpBC,IAAI,CAAC,KAAK,CACVC,SAAS,MACTC,QAAQ,MACRpC,KAAK,CAAElC,cAAe,CACtBuE,QAAQ,CAAGS,CAAC,EACV/E,iBAAiB,CAAC+E,CAAC,CAAC/C,MAAM,CAACC,KAAK,CAACT,OAAO,CAAC,gBAAgB,CAAE,EAAE,CAAC,CAC/D,CACDa,KAAK,CACHtC,cAAc,GAAK,EAAE,EACrB,CAAC+C,qBAAqB,CAAC3C,mBAAmB,CAAEJ,cAAc,CAC3D,CACDwE,UAAU,CACRxE,cAAc,GAAK,EAAE,CACjB,0BAA0B,CAC1BA,cAAc,GAAK,EAAE,EACrB,CAAC+C,qBAAqB,CAAC3C,mBAAmB,CAAEJ,cAAc,CAAC,CAC3DgD,kBAAkB,CAAC5C,mBAAmB,CAAEJ,cAAc,CAAC,CACvD,yBACL,CACDsG,WAAW,CAAC,cAAc,CAC1BC,UAAU,CAAE,CACV5D,SAAS,CAAE,EACb,CAAE,CACF8B,EAAE,CAAE,CACF,uBAAuB,CAAE,CACvBS,QAAQ,CAAE,CAAEJ,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,MAAO,CAAC,CAAE;AACtCE,OAAO,CAAE,CAAEH,EAAE,CAAE,WAAW,CAAEC,EAAE,CAAE,aAAc,CAChD,CAAC,CACD,2BAA2B,CAAE,CAC3BG,QAAQ,CAAE,CAAEJ,EAAE,CAAE,SAAS,CAAEC,EAAE,CAAE,SAAU,CAAC,CAC1CyB,SAAS,CAAE,CAAE1B,EAAE,CAAE,KAAK,CAAEC,EAAE,CAAE,KAAM,CACpC,CACF,CAAE,CACH,CAAC,CACE,CAAC,EACH,CAAC,CAGN/E,cAAc,eACbf,IAAA,CAACZ,GAAG,EACFoG,EAAE,CAAE,CACFgC,EAAE,CAAE,CAAE3B,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACpBL,EAAE,CAAE,CAAEI,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACpBM,OAAO,CAAE,MAAM,CACfqB,cAAc,CAAE,CAAE5B,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,YAAa,CACnD,CAAE,CAAAhB,QAAA,cAEF9E,IAAA,CAACL,IAAI,EACHuF,KAAK,CAAE,gBAAgB/D,mBAAmB,IAAIJ,cAAc,EAAG,CAC/D2G,OAAO,CAAC,UAAU,CAClBC,IAAI,CAAC,OAAO,CACZR,KAAK,CACHrD,qBAAqB,CAAC3C,mBAAmB,CAAEJ,cAAc,CAAC,CACtD,SAAS,CACT,SACL,CACDyE,EAAE,CAAE,CACFS,QAAQ,CAAE,CAAEJ,EAAE,CAAE,SAAS,CAAEC,EAAE,CAAE,WAAY,CAAC,CAC5Cc,MAAM,CAAE,CAAEf,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,MAAO,CAAC,CAClC,kBAAkB,CAAE,CAClBE,OAAO,CAAE,CAAEH,EAAE,CAAE,OAAO,CAAEC,EAAE,CAAE,QAAS,CACvC,CACF,CAAE,CACH,CAAC,CACC,CACN,cAED5F,KAAA,CAACd,GAAG,EACFoI,EAAE,CAAE,CAAE,CACNI,CAAC,CAAE,CAAE,CACLpC,EAAE,CAAE,CACFqC,eAAe,CAAE,SAAS,CAC1BC,YAAY,CAAE,KAChB,CAAE,CAAAhD,QAAA,eAEF9E,IAAA,CAACX,UAAU,EAACqI,OAAO,CAAC,WAAW,CAACP,KAAK,CAAC,aAAa,CAAArC,QAAA,CAAC,6BAEpD,CAAY,CAAC,cACb5E,KAAA,CAACb,UAAU,EAACqI,OAAO,CAAC,OAAO,CAACP,KAAK,CAAC,eAAe,CAAArC,QAAA,EAAC,SAC9C,cAAA9E,IAAA,WAAA8E,QAAA,CAAQ,sBAAoB,CAAQ,CAAC,4CAEzC,EAAY,CAAC,cACb5E,KAAA,CAACb,UAAU,EAACqI,OAAO,CAAC,OAAO,CAACP,KAAK,CAAC,eAAe,CAAArC,QAAA,EAAC,SAC9C,cAAA9E,IAAA,WAAA8E,QAAA,CAAQ,qBAAmB,CAAQ,CAAC,kDAExC,EAAY,CAAC,cACb5E,KAAA,CAACb,UAAU,EAACqI,OAAO,CAAC,OAAO,CAACP,KAAK,CAAC,eAAe,CAAArC,QAAA,EAAC,SAC9C,cAAA9E,IAAA,WAAA8E,QAAA,CAAQ,uBAAqB,CAAQ,CAAC,2CAE1C,EAAY,CAAC,cACb5E,KAAA,CAACb,UAAU,EAACqI,OAAO,CAAC,OAAO,CAACP,KAAK,CAAC,eAAe,CAAArC,QAAA,EAAC,SAC9C,cAAA9E,IAAA,WAAA8E,QAAA,CAAQ,UAAQ,CAAQ,CAAC,2CAC7B,EAAY,CAAC,cACb5E,KAAA,CAACb,UAAU,EAACqI,OAAO,CAAC,OAAO,CAACP,KAAK,CAAC,eAAe,CAAArC,QAAA,EAAC,SAC9C,cAAA9E,IAAA,WAAA8E,QAAA,CAAQ,oBAAkB,CAAQ,CAAC,oDAEvC,EAAY,CAAC,EACV,CAAC,EACO,CAAC,cAChB5E,KAAA,CAACf,aAAa,EAAA2F,QAAA,eACZ9E,IAAA,CAACjB,MAAM,EACLgJ,OAAO,CAAEA,CAAA,GAAM,CACb7G,cAAc,CAAC,EAAE,CAAC,CAClBF,iBAAiB,CAAC,EAAE,CAAC,CACrBE,cAAc,CAAC,EAAE,CAAC,CAClB,GAAIJ,mBAAmB,CAAEA,mBAAmB,CAAC,CAAC,CAC9CL,qBAAqB,CAAC,KAAK,CAAC,CAC9B,CAAE,CAAAqE,QAAA,CACH,QAED,CAAQ,CAAC,cACT9E,IAAA,CAACjB,MAAM,EACLgJ,OAAO,CAAE/D,UAAW,CACpBgE,QAAQ,CACNjH,cAAc,GAAK,EAAE,EACrB,CAAC+C,qBAAqB,CAAC3C,mBAAmB,CAAEJ,cAAc,CAAC,EAC3DE,WAAW,CAACkC,IAAI,CAAC,CAAC,GAAK,EAAE,EACzB9B,SACD,CAAAyD,QAAA,CAEAzD,SAAS,CAAG,WAAW,CAAGT,cAAc,CAAG,QAAQ,CAAG,KAAK,CACtD,CAAC,EACI,CAAC,EACV,CAAC,CAEb,CAAC,CAED,cAAe,CAAAL,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}