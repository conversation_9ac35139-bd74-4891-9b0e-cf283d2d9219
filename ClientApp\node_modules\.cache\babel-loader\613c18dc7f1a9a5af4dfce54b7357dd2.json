{"ast": null, "code": "import api from \"./Api\";\nimport { BASE_URL } from \"./Context/config\";\nexport const EditContact = async newContact => {\n  const data = {\n    Id: newContact.Id,\n    Category: newContact.Category,\n    ContactInfo: newContact.ContactInfo,\n    Title: newContact.Title,\n    isPublic: newContact.isPublic\n  };\n  if (newContact.isPublic === true) data.isPublic = true;else data.isPublic = false;\n  try {\n    const response = await api.put(`${BASE_URL}/Contact/EditContact`, data, {\n      headers: {\n        \"Content-Type\": \"application/json\"\n      }\n    });\n    return response;\n  } catch (error) {\n    console.log(\"Error:\", error.message);\n  }\n};\n_c = EditContact;\nexport const DeleteContact = async Id => {\n  try {\n    const response = await api.delete(`${BASE_URL}/Contact/DeleteContact/${Id}`, {\n      headers: {\n        \"Content-Type\": \"application/json\"\n      }\n    });\n    return response;\n  } catch (error) {\n    throw error;\n  }\n};\n_c2 = DeleteContact;\nexport const CreateContact = async newContact => {\n  const data = {\n    UserId: parseInt(newContact.UserId.toString()),\n    Category: newContact.Category || \"CvFile\",\n    ContactInfo: newContact.ContactInfo,\n    Title: newContact.Title,\n    isPublic: newContact.isPublic\n  };\n  if (newContact.isPublic === true) data.isPublic = true;else data.isPublic = false;\n  try {\n    const response = await api.post(`${BASE_URL}/Contact/CreateContact`, data, {\n      headers: {\n        \"Content-Type\": \"application/json\"\n      }\n    });\n    return response;\n  } catch (error) {\n    var _error$response, _error$response2;\n    console.error(\"Error creating contact:\", error);\n    console.error(\"Error response:\", (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data);\n    console.error(\"Error status:\", (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.status);\n    throw error;\n  }\n};\n_c3 = CreateContact;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"EditContact\");\n$RefreshReg$(_c2, \"DeleteContact\");\n$RefreshReg$(_c3, \"CreateContact\");", "map": {"version": 3, "names": ["api", "BASE_URL", "EditContact", "newContact", "data", "Id", "Category", "ContactInfo", "Title", "isPublic", "response", "put", "headers", "error", "console", "log", "message", "_c", "DeleteContact", "delete", "_c2", "CreateContact", "UserId", "parseInt", "toString", "post", "_error$response", "_error$response2", "status", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/iDigitalX/ClientApp/src/ContactData.ts"], "sourcesContent": ["import api from \"./Api\";\r\nimport { BASE_URL } from \"./Context/config\";\r\n\r\nexport interface PutContact {\r\n  Id: number;\r\n  Category: string;\r\n  ContactInfo: string;\r\n  Title?: string;\r\n  isPublic: boolean;\r\n}\r\n\r\nexport interface PostContact {\r\n  UserId: number;\r\n  ContactInfo: string;\r\n  Category: string;\r\n  Title?: string;\r\n  isPublic: boolean;\r\n}\r\n\r\nexport const EditContact = async (newContact: PutContact) => {\r\n  const data = {\r\n    Id: newContact.Id,\r\n    Category: newContact.Category,\r\n    ContactInfo: newContact.ContactInfo,\r\n    Title: newContact.Title,\r\n    isPublic: newContact.isPublic,\r\n  };\r\n\r\n  if (newContact.isPublic === true) data.isPublic = true;\r\n  else data.isPublic = false;\r\n\r\n  try {\r\n    const response = await api.put(`${BASE_URL}/Contact/EditContact`, data, {\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.log(\"Error:\", error.message);\r\n  }\r\n};\r\n\r\nexport const DeleteContact = async (Id: number) => {\r\n  try {\r\n    const response = await api.delete(\r\n      `${BASE_URL}/Contact/DeleteContact/${Id}`,\r\n      {\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n      }\r\n    );\r\n\r\n    return response;\r\n  } catch (error) {\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const CreateContact = async (newContact: PostContact) => {\r\n  const data = {\r\n    UserId: parseInt(newContact.UserId.toString()),\r\n    Category: newContact.Category || \"CvFile\",\r\n    ContactInfo: newContact.ContactInfo,\r\n    Title: newContact.Title,\r\n    isPublic: newContact.isPublic,\r\n  };\r\n\r\n  if (newContact.isPublic === true) data.isPublic = true;\r\n  else data.isPublic = false;\r\n\r\n  try {\r\n    const response = await api.post(`${BASE_URL}/Contact/CreateContact`, data, {\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Error creating contact:\", error);\r\n    console.error(\"Error response:\", error.response?.data);\r\n    console.error(\"Error status:\", error.response?.status);\r\n    throw error;\r\n  }\r\n};\r\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,OAAO;AACvB,SAASC,QAAQ,QAAQ,kBAAkB;AAkB3C,OAAO,MAAMC,WAAW,GAAG,MAAOC,UAAsB,IAAK;EAC3D,MAAMC,IAAI,GAAG;IACXC,EAAE,EAAEF,UAAU,CAACE,EAAE;IACjBC,QAAQ,EAAEH,UAAU,CAACG,QAAQ;IAC7BC,WAAW,EAAEJ,UAAU,CAACI,WAAW;IACnCC,KAAK,EAAEL,UAAU,CAACK,KAAK;IACvBC,QAAQ,EAAEN,UAAU,CAACM;EACvB,CAAC;EAED,IAAIN,UAAU,CAACM,QAAQ,KAAK,IAAI,EAAEL,IAAI,CAACK,QAAQ,GAAG,IAAI,CAAC,KAClDL,IAAI,CAACK,QAAQ,GAAG,KAAK;EAE1B,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMV,GAAG,CAACW,GAAG,CAAC,GAAGV,QAAQ,sBAAsB,EAAEG,IAAI,EAAE;MACtEQ,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IAEF,OAAOF,QAAQ;EACjB,CAAC,CAAC,OAAOG,KAAK,EAAE;IACdC,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEF,KAAK,CAACG,OAAO,CAAC;EACtC;AACF,CAAC;AAACC,EAAA,GAvBWf,WAAW;AAyBxB,OAAO,MAAMgB,aAAa,GAAG,MAAOb,EAAU,IAAK;EACjD,IAAI;IACF,MAAMK,QAAQ,GAAG,MAAMV,GAAG,CAACmB,MAAM,CAC/B,GAAGlB,QAAQ,0BAA0BI,EAAE,EAAE,EACzC;MACEO,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CACF,CAAC;IAED,OAAOF,QAAQ;EACjB,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,MAAMA,KAAK;EACb;AACF,CAAC;AAACO,GAAA,GAfWF,aAAa;AAiB1B,OAAO,MAAMG,aAAa,GAAG,MAAOlB,UAAuB,IAAK;EAC9D,MAAMC,IAAI,GAAG;IACXkB,MAAM,EAAEC,QAAQ,CAACpB,UAAU,CAACmB,MAAM,CAACE,QAAQ,CAAC,CAAC,CAAC;IAC9ClB,QAAQ,EAAEH,UAAU,CAACG,QAAQ,IAAI,QAAQ;IACzCC,WAAW,EAAEJ,UAAU,CAACI,WAAW;IACnCC,KAAK,EAAEL,UAAU,CAACK,KAAK;IACvBC,QAAQ,EAAEN,UAAU,CAACM;EACvB,CAAC;EAED,IAAIN,UAAU,CAACM,QAAQ,KAAK,IAAI,EAAEL,IAAI,CAACK,QAAQ,GAAG,IAAI,CAAC,KAClDL,IAAI,CAACK,QAAQ,GAAG,KAAK;EAE1B,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMV,GAAG,CAACyB,IAAI,CAAC,GAAGxB,QAAQ,wBAAwB,EAAEG,IAAI,EAAE;MACzEQ,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IACF,OAAOF,QAAQ;EACjB,CAAC,CAAC,OAAOG,KAAK,EAAE;IAAA,IAAAa,eAAA,EAAAC,gBAAA;IACdb,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IAC/CC,OAAO,CAACD,KAAK,CAAC,iBAAiB,GAAAa,eAAA,GAAEb,KAAK,CAACH,QAAQ,cAAAgB,eAAA,uBAAdA,eAAA,CAAgBtB,IAAI,CAAC;IACtDU,OAAO,CAACD,KAAK,CAAC,eAAe,GAAAc,gBAAA,GAAEd,KAAK,CAACH,QAAQ,cAAAiB,gBAAA,uBAAdA,gBAAA,CAAgBC,MAAM,CAAC;IACtD,MAAMf,KAAK;EACb;AACF,CAAC;AAACgB,GAAA,GAzBWR,aAAa;AAAA,IAAAJ,EAAA,EAAAG,GAAA,EAAAS,GAAA;AAAAC,YAAA,CAAAb,EAAA;AAAAa,YAAA,CAAAV,GAAA;AAAAU,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}