{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\iDigitalX\\\\ClientApp\\\\src\\\\sections\\\\auth\\\\signup\\\\PhotoSelector.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport { useRef } from \"react\";\nimport { Button, CircularProgress } from \"@mui/material\";\nimport PostAddIcon from \"@mui/icons-material/PostAdd\";\nimport { toast } from \"react-toastify\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PhotoSelector = _ref => {\n  _s();\n  let {\n    onSelect\n  } = _ref;\n  const fileInputRef = useRef(null);\n  const maxSize = 1024 * 1024;\n  const handleFileChange = event => {\n    const file = event.target.files[0];\n    if (file) {\n      // Validate file type\n      if (!file.type.startsWith(\"image/\")) {\n        toast.error(\"Please select a valid image file\", {\n          position: \"top-center\",\n          autoClose: 3000\n        });\n        return;\n      }\n\n      // Validate file size\n      if (file.size > maxSize) {\n        toast.error(\"File size exceeds the 1MB limit. Please select a smaller file.\", {\n          position: \"top-center\",\n          autoClose: 3000\n        });\n        return;\n      }\n      const reader = new FileReader();\n      reader.onload = () => {\n        try {\n          onSelect(reader.result);\n          toast.success(\"Image uploaded successfully\", {\n            position: \"top-center\",\n            autoClose: 1000\n          });\n        } catch (error) {\n          toast.error(\"Error processing image\", {\n            position: \"top-center\",\n            autoClose: 3000\n          });\n        }\n      };\n      reader.onerror = () => {\n        toast.error(\"Error reading file\", {\n          position: \"top-center\",\n          autoClose: 3000\n        });\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n  const handleButtonClick = () => {\n    fileInputRef.current.click();\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n      type: \"file\",\n      accept: \"image/*\",\n      ref: fileInputRef,\n      style: {\n        display: \"none\"\n      },\n      onChange: handleFileChange\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      variant: \"contained\",\n      onClick: handleButtonClick,\n      style: {\n        position: \"absolute\",\n        width: \"0px\",\n        height: \"0px\",\n        border: \"none\",\n        padding: \"0\",\n        fontSize: \"20px\",\n        backgroundColor: \"transparent\",\n        color: \"#ff715b\"\n      },\n      children: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"bi bi-plus-circle-fill\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(PhotoSelector, \"YQqvMxdmg33cmOXmQcOjJm+FLVI=\");\n_c = PhotoSelector;\nexport const FileSelector = _ref2 => {\n  _s2();\n  let {\n    onSelect,\n    isLoading = false\n  } = _ref2;\n  const fileInputRef = useRef(null);\n  const maxSize = 1024 * 1024;\n  const handleFileChange = event => {\n    const file = event.target.files[0];\n    if (file) {\n      if (file.type != \"application/pdf\") {\n        alert(\"Invalid file type. Only PDF are allowed.\");\n        return;\n      }\n      if (file.size > maxSize) {\n        alert(\"File size exceeds the 1MB limit. Please select a smaller file.\");\n        return;\n      }\n      const reader = new FileReader();\n      reader.onload = () => {\n        onSelect(reader.result);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n  const handleButtonClick = () => {\n    fileInputRef.current.click();\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n      type: \"file\",\n      accept: \"application/pdf\",\n      ref: fileInputRef,\n      style: {\n        display: \"none\"\n      },\n      onChange: handleFileChange\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      variant: \"contained\",\n      onClick: handleButtonClick,\n      color: \"primary\",\n      disabled: isLoading,\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          marginRight: \"10px\"\n        },\n        children: isLoading ? \"Uploading...\" : \"Upload\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this), isLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 20,\n        color: \"inherit\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(PostAddIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s2(FileSelector, \"YQqvMxdmg33cmOXmQcOjJm+FLVI=\");\n_c2 = FileSelector;\nexport default PhotoSelector;\nvar _c, _c2;\n$RefreshReg$(_c, \"PhotoSelector\");\n$RefreshReg$(_c2, \"FileSelector\");", "map": {"version": 3, "names": ["useRef", "<PERSON><PERSON>", "CircularProgress", "PostAddIcon", "toast", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PhotoSelector", "_ref", "_s", "onSelect", "fileInputRef", "maxSize", "handleFileChange", "event", "file", "target", "files", "type", "startsWith", "error", "position", "autoClose", "size", "reader", "FileReader", "onload", "result", "success", "onerror", "readAsDataURL", "handleButtonClick", "current", "click", "children", "accept", "ref", "style", "display", "onChange", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "onClick", "width", "height", "border", "padding", "fontSize", "backgroundColor", "color", "className", "_c", "FileSelector", "_ref2", "_s2", "isLoading", "alert", "disabled", "marginRight", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/iDigitalX/ClientApp/src/sections/auth/signup/PhotoSelector.js"], "sourcesContent": ["import { useRef } from \"react\";\r\nimport { <PERSON><PERSON>, CircularProgress } from \"@mui/material\";\r\nimport PostAddIcon from \"@mui/icons-material/PostAdd\";\r\nimport { toast } from \"react-toastify\";\r\n\r\nconst PhotoSelector = ({ onSelect }) => {\r\n  const fileInputRef = useRef(null);\r\n  const maxSize = 1024 * 1024;\r\n\r\n  const handleFileChange = (event) => {\r\n    const file = event.target.files[0];\r\n\r\n    if (file) {\r\n      // Validate file type\r\n      if (!file.type.startsWith(\"image/\")) {\r\n        toast.error(\"Please select a valid image file\", {\r\n          position: \"top-center\",\r\n          autoClose: 3000,\r\n        });\r\n        return;\r\n      }\r\n\r\n      // Validate file size\r\n      if (file.size > maxSize) {\r\n        toast.error(\r\n          \"File size exceeds the 1MB limit. Please select a smaller file.\",\r\n          {\r\n            position: \"top-center\",\r\n            autoClose: 3000,\r\n          }\r\n        );\r\n        return;\r\n      }\r\n\r\n      const reader = new FileReader();\r\n      reader.onload = () => {\r\n        try {\r\n          onSelect(reader.result);\r\n          toast.success(\"Image uploaded successfully\", {\r\n            position: \"top-center\",\r\n            autoClose: 1000,\r\n          });\r\n        } catch (error) {\r\n          toast.error(\"Error processing image\", {\r\n            position: \"top-center\",\r\n            autoClose: 3000,\r\n          });\r\n        }\r\n      };\r\n      reader.onerror = () => {\r\n        toast.error(\"Error reading file\", {\r\n          position: \"top-center\",\r\n          autoClose: 3000,\r\n        });\r\n      };\r\n      reader.readAsDataURL(file);\r\n    }\r\n  };\r\n\r\n  const handleButtonClick = () => {\r\n    fileInputRef.current.click();\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <input\r\n        type=\"file\"\r\n        accept=\"image/*\"\r\n        ref={fileInputRef}\r\n        style={{ display: \"none\" }}\r\n        onChange={handleFileChange}\r\n      />\r\n      <Button\r\n        variant=\"contained\"\r\n        onClick={handleButtonClick}\r\n        style={{\r\n          position: \"absolute\",\r\n          width: \"0px\",\r\n          height: \"0px\",\r\n          border: \"none\",\r\n          padding: \"0\",\r\n          fontSize: \"20px\",\r\n          backgroundColor: \"transparent\",\r\n          color: \"#ff715b\",\r\n        }}\r\n      >\r\n        <i className=\"bi bi-plus-circle-fill\"></i>\r\n      </Button>\r\n    </>\r\n  );\r\n};\r\n\r\nexport const FileSelector = ({ onSelect, isLoading = false }) => {\r\n  const fileInputRef = useRef(null);\r\n  const maxSize = 1024 * 1024;\r\n\r\n  const handleFileChange = (event) => {\r\n    const file = event.target.files[0];\r\n\r\n    if (file) {\r\n      if (file.type != \"application/pdf\") {\r\n        alert(\"Invalid file type. Only PDF are allowed.\");\r\n        return;\r\n      }\r\n\r\n      if (file.size > maxSize) {\r\n        alert(\"File size exceeds the 1MB limit. Please select a smaller file.\");\r\n        return;\r\n      }\r\n\r\n      const reader = new FileReader();\r\n      reader.onload = () => {\r\n        onSelect(reader.result);\r\n      };\r\n      reader.readAsDataURL(file);\r\n    }\r\n  };\r\n\r\n  const handleButtonClick = () => {\r\n    fileInputRef.current.click();\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <input\r\n        type=\"file\"\r\n        accept=\"application/pdf\"\r\n        ref={fileInputRef}\r\n        style={{ display: \"none\" }}\r\n        onChange={handleFileChange}\r\n      />\r\n      <Button\r\n        variant=\"contained\"\r\n        onClick={handleButtonClick}\r\n        color=\"primary\"\r\n        disabled={isLoading}\r\n      >\r\n        <span\r\n          style={{\r\n            marginRight: \"10px\",\r\n          }}\r\n        >\r\n          {isLoading ? \"Uploading...\" : \"Upload\"}\r\n        </span>\r\n        {isLoading ? (\r\n          <CircularProgress size={20} color=\"inherit\" />\r\n        ) : (\r\n          <PostAddIcon />\r\n        )}\r\n      </Button>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default PhotoSelector;\r\n"], "mappings": ";;;AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,SAASC,MAAM,EAAEC,gBAAgB,QAAQ,eAAe;AACxD,OAAOC,WAAW,MAAM,6BAA6B;AACrD,SAASC,KAAK,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEvC,MAAMC,aAAa,GAAGC,IAAA,IAAkB;EAAAC,EAAA;EAAA,IAAjB;IAAEC;EAAS,CAAC,GAAAF,IAAA;EACjC,MAAMG,YAAY,GAAGb,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMc,OAAO,GAAG,IAAI,GAAG,IAAI;EAE3B,MAAMC,gBAAgB,GAAIC,KAAK,IAAK;IAClC,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAElC,IAAIF,IAAI,EAAE;MACR;MACA,IAAI,CAACA,IAAI,CAACG,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;QACnCjB,KAAK,CAACkB,KAAK,CAAC,kCAAkC,EAAE;UAC9CC,QAAQ,EAAE,YAAY;UACtBC,SAAS,EAAE;QACb,CAAC,CAAC;QACF;MACF;;MAEA;MACA,IAAIP,IAAI,CAACQ,IAAI,GAAGX,OAAO,EAAE;QACvBV,KAAK,CAACkB,KAAK,CACT,gEAAgE,EAChE;UACEC,QAAQ,EAAE,YAAY;UACtBC,SAAS,EAAE;QACb,CACF,CAAC;QACD;MACF;MAEA,MAAME,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAG,MAAM;QACpB,IAAI;UACFhB,QAAQ,CAACc,MAAM,CAACG,MAAM,CAAC;UACvBzB,KAAK,CAAC0B,OAAO,CAAC,6BAA6B,EAAE;YAC3CP,QAAQ,EAAE,YAAY;YACtBC,SAAS,EAAE;UACb,CAAC,CAAC;QACJ,CAAC,CAAC,OAAOF,KAAK,EAAE;UACdlB,KAAK,CAACkB,KAAK,CAAC,wBAAwB,EAAE;YACpCC,QAAQ,EAAE,YAAY;YACtBC,SAAS,EAAE;UACb,CAAC,CAAC;QACJ;MACF,CAAC;MACDE,MAAM,CAACK,OAAO,GAAG,MAAM;QACrB3B,KAAK,CAACkB,KAAK,CAAC,oBAAoB,EAAE;UAChCC,QAAQ,EAAE,YAAY;UACtBC,SAAS,EAAE;QACb,CAAC,CAAC;MACJ,CAAC;MACDE,MAAM,CAACM,aAAa,CAACf,IAAI,CAAC;IAC5B;EACF,CAAC;EAED,MAAMgB,iBAAiB,GAAGA,CAAA,KAAM;IAC9BpB,YAAY,CAACqB,OAAO,CAACC,KAAK,CAAC,CAAC;EAC9B,CAAC;EAED,oBACE7B,OAAA,CAAAE,SAAA;IAAA4B,QAAA,gBACE9B,OAAA;MACEc,IAAI,EAAC,MAAM;MACXiB,MAAM,EAAC,SAAS;MAChBC,GAAG,EAAEzB,YAAa;MAClB0B,KAAK,EAAE;QAAEC,OAAO,EAAE;MAAO,CAAE;MAC3BC,QAAQ,EAAE1B;IAAiB;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC,eACFvC,OAAA,CAACL,MAAM;MACL6C,OAAO,EAAC,WAAW;MACnBC,OAAO,EAAEd,iBAAkB;MAC3BM,KAAK,EAAE;QACLhB,QAAQ,EAAE,UAAU;QACpByB,KAAK,EAAE,KAAK;QACZC,MAAM,EAAE,KAAK;QACbC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,GAAG;QACZC,QAAQ,EAAE,MAAM;QAChBC,eAAe,EAAE,aAAa;QAC9BC,KAAK,EAAE;MACT,CAAE;MAAAlB,QAAA,eAEF9B,OAAA;QAAGiD,SAAS,EAAC;MAAwB;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC;EAAA,eACT,CAAC;AAEP,CAAC;AAAClC,EAAA,CArFIF,aAAa;AAAA+C,EAAA,GAAb/C,aAAa;AAuFnB,OAAO,MAAMgD,YAAY,GAAGC,KAAA,IAAqC;EAAAC,GAAA;EAAA,IAApC;IAAE/C,QAAQ;IAAEgD,SAAS,GAAG;EAAM,CAAC,GAAAF,KAAA;EAC1D,MAAM7C,YAAY,GAAGb,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMc,OAAO,GAAG,IAAI,GAAG,IAAI;EAE3B,MAAMC,gBAAgB,GAAIC,KAAK,IAAK;IAClC,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAElC,IAAIF,IAAI,EAAE;MACR,IAAIA,IAAI,CAACG,IAAI,IAAI,iBAAiB,EAAE;QAClCyC,KAAK,CAAC,0CAA0C,CAAC;QACjD;MACF;MAEA,IAAI5C,IAAI,CAACQ,IAAI,GAAGX,OAAO,EAAE;QACvB+C,KAAK,CAAC,gEAAgE,CAAC;QACvE;MACF;MAEA,MAAMnC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAG,MAAM;QACpBhB,QAAQ,CAACc,MAAM,CAACG,MAAM,CAAC;MACzB,CAAC;MACDH,MAAM,CAACM,aAAa,CAACf,IAAI,CAAC;IAC5B;EACF,CAAC;EAED,MAAMgB,iBAAiB,GAAGA,CAAA,KAAM;IAC9BpB,YAAY,CAACqB,OAAO,CAACC,KAAK,CAAC,CAAC;EAC9B,CAAC;EAED,oBACE7B,OAAA,CAAAE,SAAA;IAAA4B,QAAA,gBACE9B,OAAA;MACEc,IAAI,EAAC,MAAM;MACXiB,MAAM,EAAC,iBAAiB;MACxBC,GAAG,EAAEzB,YAAa;MAClB0B,KAAK,EAAE;QAAEC,OAAO,EAAE;MAAO,CAAE;MAC3BC,QAAQ,EAAE1B;IAAiB;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC,eACFvC,OAAA,CAACL,MAAM;MACL6C,OAAO,EAAC,WAAW;MACnBC,OAAO,EAAEd,iBAAkB;MAC3BqB,KAAK,EAAC,SAAS;MACfQ,QAAQ,EAAEF,SAAU;MAAAxB,QAAA,gBAEpB9B,OAAA;QACEiC,KAAK,EAAE;UACLwB,WAAW,EAAE;QACf,CAAE;QAAA3B,QAAA,EAEDwB,SAAS,GAAG,cAAc,GAAG;MAAQ;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,EACNe,SAAS,gBACRtD,OAAA,CAACJ,gBAAgB;QAACuB,IAAI,EAAE,EAAG;QAAC6B,KAAK,EAAC;MAAS;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAE9CvC,OAAA,CAACH,WAAW;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CACf;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC;EAAA,eACT,CAAC;AAEP,CAAC;AAACc,GAAA,CA5DWF,YAAY;AAAAO,GAAA,GAAZP,YAAY;AA8DzB,eAAehD,aAAa;AAAC,IAAA+C,EAAA,EAAAQ,GAAA;AAAAC,YAAA,CAAAT,EAAA;AAAAS,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}