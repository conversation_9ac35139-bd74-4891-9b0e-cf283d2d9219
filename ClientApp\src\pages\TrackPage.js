import { useEffect, useState, useCallback } from "react";
import { Avatar, Typography, Paper, Box, IconButton } from "@mui/material";
import { ArrowBack } from "@mui/icons-material";
import { GetProfilesFromLink } from "../ProfileData.ts";
import { useNavigate } from "react-router-dom";
import { PROFILE_URL } from "../Context/config";
import useResponsive from "../hooks/useResponsive";
import { styled } from "@mui/material/styles";

// Styled components for the v3 design
const StyledContainer = styled(Box)(({ theme }) => ({
  display: "flex",
  flexDirection: "column",
  minHeight: "100vh",
  background: "linear-gradient(135deg, rgb(255, 113, 91), #f8e9e9)",
  padding: "1rem",
  gap: "1rem",
  fontFamily: "'Nunito', sans-serif",
  [theme.breakpoints.up("md")]: {
    padding: "1.5rem",
    gap: "1.5rem",
  },
}));

const StyledHeader = styled(Paper)(({ theme }) => ({
  height: "55px",
  backgroundColor: "rgba(255, 255, 255, 0.9)",
  backdropFilter: "blur(5px)",
  borderRadius: "12px",
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
  padding: "0 1rem",
  border: "1px solid rgba(255, 255, 255, 0.3)",
  boxShadow: "0 4px 15px rgba(100, 130, 140, 0.15)",
  [theme.breakpoints.up("md")]: {
    height: "60px",
    padding: "0 1.5rem",
  },
}));

const StyledProfileCard = styled(Paper)(({ theme }) => ({
  backgroundColor: "#FFFFFF",
  borderRadius: "20px",
  boxShadow: "0 6px 20px rgba(100, 130, 140, 0.15)",
  padding: "1rem",
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  textAlign: "center",
  [theme.breakpoints.up("md")]: {
    padding: "1.5rem",
    justifyContent: "center",
    height: "100%",
  },
}));

const StyledMetricsCard = styled(Paper)(({ theme }) => ({
  backgroundColor: "#FFFFFF",
  borderRadius: "20px",
  boxShadow: "0 6px 20px rgba(100, 130, 140, 0.15)",
  padding: "1rem",
  display: "flex",
  flexDirection: "column",
  [theme.breakpoints.up("md")]: {
    padding: "1.5rem",
  },
}));

const StyledQRCard = styled(Paper)(({ theme }) => ({
  backgroundColor: "#FFFFFF",
  borderRadius: "20px",
  boxShadow: "0 6px 20px rgba(100, 130, 140, 0.15)",
  padding: "0.8rem",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  [theme.breakpoints.up("md")]: {
    padding: "1rem",
  },
}));

export const Track = () => {
  const navigate = useNavigate();
  const isDesktop = useResponsive("up", "md");
  const [Account, setAccount] = useState({
    email: "",
    firstName: "",
    id: 0,
    lastName: "",
    category: "",
    profile: {
      birthDate: "",
      customLinks: null,
      gender: "",
      id: 0,
      isPremium: false,
      occupation: "",
      premium: null,
      profileCoverPicture: "",
      profilePicture: "",
      profilePictureFrame: 0,
      socialLinks: null,
      user: null,
      userId: 0,
      userName: "",
    },
    auth: {
      id: 0,
      password: "",
      user: null,
      userId: 8,
    },
    rate: 0,
    rateCount: 0,
    rate_Skill_QualityOfWork: 0,
    rate_Skill_CostEffectiveness: 0,
    rate_Skill_Timeliness: 0,
    rate_Skill_Communication: 0,
    rate_Skill_Agility: 0,
  });

  const Ratings = [
    {
      name: "Quality Of Work",
      score: Account.rate_Skill_QualityOfWork || 0,
      icon: "fas fa-star",
    },
    {
      name: "Cost Effectiveness",
      score: Account.rate_Skill_CostEffectiveness || 0,
      icon: "fas fa-hand-holding-usd",
    },
    {
      name: "Timeliness",
      score: Account.rate_Skill_Timeliness || 0,
      icon: "fas fa-hourglass-half",
    },
    {
      name: "Communication",
      score: Account.rate_Skill_Communication || 0,
      icon: "fas fa-comments",
    },
    {
      name: "Agility",
      score: Account.rate_Skill_Agility || 0,
      icon: "fas fa-rocket",
    },
  ];

  const fetchProfileData = useCallback(async () => {
    const currentPath = window.location.pathname;
    const searchQueryPart = currentPath.substring(
      currentPath.lastIndexOf("/") + 1
    );
    try {
      const response = await GetProfilesFromLink(searchQueryPart);
      setAccount(response.data);
    } catch (error) {
      navigate("/404");
    }
  }, [navigate]);

  useEffect(() => {
    fetchProfileData();
    // Execute fetch every minute
    const intervalId = setInterval(fetchProfileData, 60000);
    return () => clearInterval(intervalId);
  }, [fetchProfileData]);

  // Generate stars for rating display
  const generateStars = (rating) => {
    const stars = [];
    const maxStars = 5;
    for (let i = 1; i <= maxStars; i++) {
      const scoreDifference = rating - (i - 1);
      if (scoreDifference >= 0.75) {
        stars.push(<i key={i} className="fas fa-star" style={{ color: "#FFCA28", margin: "0 2px" }} />);
      } else if (scoreDifference >= 0.25) {
        stars.push(<i key={i} className="fas fa-star-half-alt" style={{ color: "#FFCA28", margin: "0 2px" }} />);
      } else {
        stars.push(<i key={i} className="far fa-star" style={{ color: "#BDBDBD", margin: "0 2px" }} />);
      }
    }
    return stars;
  };

  return (
    <StyledContainer>
      {/* Header */}
      <StyledHeader elevation={0}>
        <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
          <IconButton
            onClick={() => {
              // Try to go back in history, fallback to dashboard if no history
              if (window.history.length > 1) {
                navigate(-1);
              } else {
                navigate("/admin/User");
              }
            }}
            sx={{
              color: "#37474F",
              "&:hover": {
                backgroundColor: "rgba(55, 71, 79, 0.1)",
              },
            }}
          >
            <ArrowBack />
          </IconButton>
          <img
            src="/assets/idigics_logo.png"
            alt="iDigics Logo"
            onClick={() => navigate("/")}
            style={{
              height: isDesktop ? "33px" : "30px",
              width: "auto",
              cursor: "pointer",
              transition: "opacity 0.2s ease",
            }}
            onMouseEnter={(e) => e.target.style.opacity = "0.8"}
            onMouseLeave={(e) => e.target.style.opacity = "1"}
          />
        </Box>
        <Box sx={{ display: "flex", alignItems: "center" }}>
          {/* Clock or additional header content can go here */}
        </Box>
      </StyledHeader>

      {/* Main Content Grid */}
      <Box sx={{
        display: "grid",
        gridTemplateColumns: isDesktop ? "1fr 1.6fr" : "1fr",
        gap: isDesktop ? "1.5rem" : "1rem",
        flexGrow: 1,
        minHeight: 0
      }}>
        {/* Profile Card */}
        <StyledProfileCard elevation={0}>
          <Box sx={{
            position: "relative",
            width: isDesktop ? "clamp(110px, 15vh, 130px)" : "90px",
            height: isDesktop ? "clamp(110px, 15vh, 130px)" : "90px",
            marginBottom: isDesktop ? "clamp(1rem, 2vh, 1.5rem)" : "0.8rem"
          }}>
            <Avatar
              src={Account.profile.profilePicture}
              sx={{
                width: "100%",
                height: "100%",
                border: isDesktop ? "5px solid #FFFFFF" : "4px solid #FFFFFF",
                boxShadow: "0 4px 10px rgba(0,0,0,0.1)",
                fontSize: isDesktop ? "clamp(2.8rem, 5vh, 3.5rem)" : "2.2rem",
                fontWeight: 700,
                backgroundColor: "#78909C",
                color: "white"
              }}
            >
              {!Account.profile.profilePicture &&
                `${Account.firstName?.[0] || ''}${Account.lastName?.[0] || ''}`
              }
            </Avatar>
            <Box sx={{
              position: "absolute",
              inset: isDesktop ? "-6px" : "-4px",
              border: isDesktop ? "3px solid rgb(255, 113, 91)" : "2px solid rgb(255, 113, 91)",
              borderRadius: "50%",
              opacity: 0.6
            }} />
          </Box>

          <Typography sx={{
            fontSize: isDesktop ? "clamp(1.6rem, 3vh, 1.8rem)" : "1.4rem",
            fontWeight: 800,
            marginBottom: "0.1rem",
            color: "#37474F"
          }}>
            {Account.firstName} {Account.lastName}
          </Typography>

          <Typography sx={{
            fontSize: isDesktop ? "clamp(0.9rem, 1.8vh, 1rem)" : "0.85rem",
            fontWeight: 600,
            color: "rgb(255, 113, 91)",
            marginBottom: isDesktop ? "clamp(1.2rem, 2.5vh, 1.8rem)" : "1rem"
          }}>
            {Account.profile.occupation || "Freelancer"}
          </Typography>

          <Box sx={{ marginTop: isDesktop ? "clamp(0.8rem, 1.5vh, 1rem)" : "0.5rem" }}>
            <Typography sx={{
              fontSize: isDesktop ? "clamp(3.8rem, 7vh, 4.5rem)" : "3.2rem",
              fontWeight: 800,
              color: "rgb(255, 113, 91)",
              lineHeight: 1,
              marginBottom: "0.1rem"
            }}>
              {Account.rate?.toFixed(1) || "0.0"}
              <Typography component="span" sx={{
                fontSize: isDesktop ? "clamp(1.3rem, 2.5vh, 1.5rem)" : "1.1rem",
                fontWeight: 400,
                color: "#78909C"
              }}>
                /5
              </Typography>
            </Typography>

            <Box sx={{
              fontSize: isDesktop ? "clamp(1.4rem, 2.8vh, 1.6rem)" : "1.2rem",
              margin: isDesktop ? "clamp(0.4rem, 1vh, 0.6rem) 0" : "0.3rem 0"
            }}>
              {generateStars(Account.rate || 0)}
            </Box>

            <Typography sx={{
              fontSize: isDesktop ? "clamp(0.85rem, 1.7vh, 0.95rem)" : "0.8rem",
              color: "#78909C",
              fontWeight: 600
            }}>
              Based on {Account.rateCount || 0} Reviews
            </Typography>
          </Box>
        </StyledProfileCard>

        {/* Right Column - Metrics and QR */}
        <Box sx={{
          display: "flex",
          flexDirection: "column",
          gap: isDesktop ? "1.5rem" : "1rem",
          minHeight: 0
        }}>
          {/* Performance Metrics */}
          <StyledMetricsCard elevation={0} sx={{ flexGrow: 1 }}>
            <Typography sx={{
              fontSize: isDesktop ? "clamp(1.2rem, 2.5vh, 1.4rem)" : "1.1rem",
              fontWeight: 700,
              marginBottom: isDesktop ? "clamp(1.2rem, 2.5vh, 1.5rem)" : "1rem",
              color: "#37474F"
            }}>
              Performance Breakdown
            </Typography>

            <Box sx={{
              display: "flex",
              flexDirection: "column",
              gap: isDesktop ? "clamp(0.8rem, 1.5vh, 1rem)" : "0.6rem",
              flexGrow: 1,
              justifyContent: "space-evenly"
            }}>
              {Ratings.map((metric, index) => (
                <Box key={index} sx={{
                  display: "flex",
                  alignItems: "center",
                  gap: isDesktop ? "0.8rem" : "0.6rem"
                }}>
                  <Box sx={{
                    width: isDesktop ? "22px" : "20px",
                    fontSize: isDesktop ? "clamp(1rem, 1.8vh, 1.1rem)" : "1rem",
                    color: "rgb(255, 113, 91)",
                    textAlign: "center",
                    flexShrink: 0
                  }}>
                    <i className={metric.icon} />
                  </Box>

                  <Box sx={{
                    display: "flex",
                    alignItems: "center",
                    gap: isDesktop ? "0.8rem" : "0.6rem",
                    flex: 1,
                    minWidth: 0
                  }}>
                    <Typography sx={{
                      flexBasis: isDesktop ? "clamp(110px, 15vw, 130px)" : "100px",
                      fontWeight: 600,
                      fontSize: isDesktop ? "clamp(0.9rem, 1.7vh, 0.95rem)" : "0.85rem",
                      color: "#37474F",
                      whiteSpace: "nowrap",
                      overflow: "hidden",
                      textOverflow: "ellipsis"
                    }}>
                      {metric.name}
                    </Typography>

                    <Box sx={{
                      flexBasis: isDesktop ? "clamp(100px, 20vw, 180px)" : "80px",
                      height: isDesktop ? "11px" : "9px",
                      backgroundColor: "#ECEFF1",
                      borderRadius: isDesktop ? "6px" : "5px",
                      overflow: "hidden",
                      minWidth: isDesktop ? "60px" : "40px",
                      position: "relative"
                    }}>
                      <Box sx={{
                        height: "100%",
                        background: "linear-gradient(90deg, #FFCA28, #FFA000)",
                        borderRadius: isDesktop ? "6px" : "5px",
                        width: `${Math.min((metric.score / 5) * 100, 100)}%`,
                        transition: "width 1s cubic-bezier(0.34, 1.56, 0.64, 1)"
                      }} />
                    </Box>

                    <Typography sx={{
                      flexBasis: isDesktop ? "35px" : "30px",
                      fontWeight: 700,
                      fontSize: isDesktop ? "clamp(0.9rem, 1.8vh, 1rem)" : "0.85rem",
                      color: "#37474F",
                      textAlign: "right",
                      flexShrink: 0
                    }}>
                      {metric.score?.toFixed(1) || "0.0"}
                    </Typography>
                  </Box>
                </Box>
              ))}
            </Box>
          </StyledMetricsCard>

          {/* QR Code */}
          <StyledQRCard elevation={0}>
            <Box sx={{
              width: isDesktop ? "clamp(150px, 20vh, 180px)" : "130px",
              height: isDesktop ? "clamp(150px, 20vh, 180px)" : "130px",
              backgroundColor: "#fff",
              padding: isDesktop ? "8px" : "6px",
              borderRadius: "12px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center"
            }}>
              <canvas
                ref={(canvas) => {
                  if (canvas && Account.profile.userName) {
                    // Clear any existing content
                    const ctx = canvas.getContext("2d");
                    ctx.clearRect(0, 0, canvas.width, canvas.height);

                    import("qrcode").then((QRCode) => {
                      const qrSize = isDesktop ? 164 : 118;
                      canvas.width = qrSize;
                      canvas.height = qrSize;

                      QRCode.toCanvas(
                        canvas,
                        `${PROFILE_URL}/Profile/${Account.profile.userName}`,
                        {
                          width: qrSize,
                          margin: 1,
                          color: {
                            dark: "#2c3e50",
                            light: "#ffffff",
                          },
                          errorCorrectionLevel: 'M',
                          type: 'image/png',
                          quality: 0.92,
                          rendererOpts: {
                            quality: 0.92
                          }
                        },
                        (error) => {
                          if (error) {
                            console.error("QR Code generation error:", error);
                            return;
                          }

                          // Add logo overlay
                          const ctx = canvas.getContext("2d");
                          const logo = new Image();
                          logo.crossOrigin = "anonymous";
                          logo.onload = () => {
                            // Calculate logo size and position (center of QR code)
                            const logoSize = isDesktop ? 32 : 24;
                            const x = (canvas.width - logoSize) / 2;
                            const y = (canvas.height - logoSize) / 2;

                            // Draw white background circle for logo
                            ctx.fillStyle = "#ffffff";
                            ctx.beginPath();
                            ctx.arc(
                              x + logoSize / 2,
                              y + logoSize / 2,
                              logoSize / 2 + 2,
                              0,
                              2 * Math.PI
                            );
                            ctx.fill();

                            // Draw the logo
                            ctx.drawImage(logo, x, y, logoSize, logoSize);
                          };
                          logo.onerror = () => {
                            console.log("Logo not found, QR code generated without logo");
                          };
                          logo.src = "/assets/idigics_logo.png";
                        }
                      );
                    }).catch((error) => {
                      console.error("Failed to load QR code library:", error);
                    });
                  }
                }}
                style={{ borderRadius: "6px" }}
              />
            </Box>
          </StyledQRCard>
        </Box>
      </Box>
    </StyledContainer>
  );
};
