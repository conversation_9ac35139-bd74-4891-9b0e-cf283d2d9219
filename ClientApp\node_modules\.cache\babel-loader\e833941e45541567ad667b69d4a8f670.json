{"ast": null, "code": "import{useState,useEffect}from\"react\";import{<PERSON><PERSON>,DialogTitle,DialogContent,<PERSON>alog<PERSON>ctions,<PERSON><PERSON>ield,Button,Typography,Box}from\"@mui/material\";import{toast}from\"react-toastify\";import{CreateContact,EditContact}from\"../../../ContactData.ts\";import{useProfile}from\"../../../Context/ProfileContext\";import{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";const EmailLinkDialog=_ref=>{let{setOpenEmailDialog,openEmailDialog,Id,editingContact=null,clearEditingContact,EmailExist,existingEmail=\"\",existingName=\"\"}=_ref;const{fetchProfile}=useProfile();const[contactName,setContactName]=useState(existingName);const[email,setEmail]=useState(existingEmail);const[nameValidationError,setNameValidationError]=useState(\"\");const[isLoading,setIsLoading]=useState(false);useEffect(()=>{if(editingContact){// Pre-populate form with existing contact data when editing\nsetEmail(editingContact.LinkUrl||editingContact.ContactInfo||\"\");setContactName(editingContact.Title||\"\");}else{// Use existing props for backward compatibility or clear form for new contact\nsetEmail(existingEmail||\"\");setContactName(existingName||\"\");}setNameValidationError(\"\");},[editingContact,existingEmail,existingName,openEmailDialog]);const handleContactNameChange=event=>{setContactName(event.target.value);if(event.target.value.trim()!==\"\"){setNameValidationError(\"\");}};const handleEmailChange=event=>{setEmail(event.target.value);};const isValidEmail=input=>{// Regular expression pattern to match all email addresses\nconst emailPattern=/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/;// Check if the input matches email pattern\nconst isEmail=emailPattern.test(input);return isEmail;};const handleConfirm=async()=>{if(contactName.trim()===\"\"){setNameValidationError(\"Contact name is required\");return;}if(!isValidEmail(email)){toast.error(\"Invalid email format. Please use a valid email address\",{position:\"top-center\",autoClose:2000});return;}setIsLoading(true);let response;try{if(editingContact){response=await EditContact({Id:editingContact.Id,ContactInfo:email,Category:\"Email\",isPublic:true,Title:contactName.trim()});}else{response=await CreateContact({UserId:Id,Name:contactName,ContactInfo:email,Category:\"Email\",isPublic:true,Title:contactName.trim()});}setContactName(\"\");setEmail(\"\");setContactName(\"\");if(clearEditingContact)clearEditingContact();setOpenEmailDialog(false);if(response){toast.success(editingContact?\"Email contact updated\":\"Email contact added\",{position:\"top-center\",autoClose:1000});if(fetchProfile)fetchProfile();}else{toast.error(\"Error while saving email contact\",{position:\"top-center\",autoClose:1000});}}catch(error){console.error(\"Error saving email contact:\",error);toast.error(\"Error while saving email contact\",{position:\"top-center\",autoClose:1000});}finally{setIsLoading(false);}};return/*#__PURE__*/_jsxs(Dialog,{open:openEmailDialog,onClose:()=>{setEmail(\"\");setContactName(\"\");if(clearEditingContact)clearEditingContact();setOpenEmailDialog(false);},children:[/*#__PURE__*/_jsx(DialogTitle,{children:editingContact?\"Edit Email Contact\":\"Add Email Contact\"}),/*#__PURE__*/_jsxs(DialogContent,{children:[/*#__PURE__*/_jsx(TextField,{name:\"contactName\",autoFocus:true,margin:\"dense\",label:\"Contact Name\",type:\"text\",fullWidth:true,required:true,value:contactName,onChange:handleContactNameChange,error:nameValidationError!==\"\",helperText:contactName===\"\"?\"Contact name is required\":\"\",sx:{mb:2}}),/*#__PURE__*/_jsx(TextField,{name:\"Email\",margin:\"dense\",label:\"Email Address\",type:\"email\",fullWidth:true,required:true,value:email,onChange:handleEmailChange,helperText:email===\"\"?\"Email address is required\":\"\"}),/*#__PURE__*/_jsxs(Box,{mt:2,p:2,sx:{backgroundColor:\"#f0f0f0\",borderRadius:\"5px\"},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",color:\"textPrimary\",children:\"Tips for Adding Email Contact\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"textSecondary\",children:\"- Give your contact a descriptive name (e.g., \\\"Work Email\\\", \\\"Personal Email\\\")\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"textSecondary\",children:\"- Enter your complete email address (e.g., <EMAIL>)\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"textSecondary\",children:\"- All email providers are supported (Gmail, Yahoo, Outlook, etc.)\"})]})]}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{onClick:()=>{setEmail(\"\");setContactName(\"\");if(clearEditingContact)clearEditingContact();setOpenEmailDialog(false);},children:\"Cancel\"}),/*#__PURE__*/_jsx(Button,{onClick:handleConfirm,variant:\"contained\",disabled:isLoading,children:isLoading?\"Saving...\":editingContact?\"Update\":\"Add\"})]})]});};export default EmailLinkDialog;", "map": {"version": 3, "names": ["useState", "useEffect", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "<PERSON><PERSON>", "Typography", "Box", "toast", "CreateContact", "EditContact", "useProfile", "jsx", "_jsx", "jsxs", "_jsxs", "EmailLinkDialog", "_ref", "setOpenEmailDialog", "openEmailDialog", "Id", "editingContact", "clearEditingContact", "EmailExist", "existingEmail", "existingName", "fetchProfile", "contactName", "setContactName", "email", "setEmail", "nameValidationError", "setNameValidationError", "isLoading", "setIsLoading", "LinkUrl", "ContactInfo", "Title", "handleContactNameChange", "event", "target", "value", "trim", "handleEmailChange", "isValidEmail", "input", "emailPattern", "isEmail", "test", "handleConfirm", "error", "position", "autoClose", "response", "Category", "isPublic", "UserId", "Name", "success", "console", "open", "onClose", "children", "name", "autoFocus", "margin", "label", "type", "fullWidth", "required", "onChange", "helperText", "sx", "mb", "mt", "p", "backgroundColor", "borderRadius", "variant", "color", "onClick", "disabled"], "sources": ["C:/Users/<USER>/Desktop/iDigitalX/ClientApp/src/sections/@dashboard/Link/EmailLinkDialog.js"], "sourcesContent": ["import { useState, useEffect } from \"react\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  DialogTitle,\r\n  DialogContent,\r\n  <PERSON>alogA<PERSON>,\r\n  <PERSON><PERSON>ield,\r\n  Button,\r\n  Typography,\r\n  Box,\r\n} from \"@mui/material\";\r\nimport { toast } from \"react-toastify\";\r\nimport { CreateContact, EditContact } from \"../../../ContactData.ts\";\r\nimport { useProfile } from \"../../../Context/ProfileContext\";\r\n\r\nconst EmailLinkDialog = ({\r\n  setOpenEmailDialog,\r\n  openEmailDialog,\r\n  Id,\r\n  editingContact = null,\r\n  clearEditingContact,\r\n  EmailExist,\r\n  existingEmail = \"\",\r\n  existingName = \"\",\r\n}) => {\r\n  const { fetchProfile } = useProfile();\r\n  const [contactName, setContactName] = useState(existingName);\r\n  const [email, setEmail] = useState(existingEmail);\r\n  const [nameValidationError, setNameValidationError] = useState(\"\");\r\n  const [isLoading, setIsLoading] = useState(false);\r\n\r\n  useEffect(() => {\r\n    if (editingContact) {\r\n      // Pre-populate form with existing contact data when editing\r\n      setEmail(editingContact.LinkUrl || editingContact.ContactInfo || \"\");\r\n      setContactName(editingContact.Title || \"\");\r\n    } else {\r\n      // Use existing props for backward compatibility or clear form for new contact\r\n      setEmail(existingEmail || \"\");\r\n      setContactName(existingName || \"\");\r\n    }\r\n    setNameValidationError(\"\");\r\n  }, [editingContact, existingEmail, existingName, openEmailDialog]);\r\n\r\n  const handleContactNameChange = (event) => {\r\n    setContactName(event.target.value);\r\n    if (event.target.value.trim() !== \"\") {\r\n      setNameValidationError(\"\");\r\n    }\r\n  };\r\n\r\n  const handleEmailChange = (event) => {\r\n    setEmail(event.target.value);\r\n  };\r\n\r\n  const isValidEmail = (input) => {\r\n    // Regular expression pattern to match all email addresses\r\n    const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/;\r\n\r\n    // Check if the input matches email pattern\r\n    const isEmail = emailPattern.test(input);\r\n\r\n    return isEmail;\r\n  };\r\n\r\n  const handleConfirm = async () => {\r\n    if (contactName.trim() === \"\") {\r\n      setNameValidationError(\"Contact name is required\");\r\n      return;\r\n    }\r\n\r\n    if (!isValidEmail(email)) {\r\n      toast.error(\"Invalid email format. Please use a valid email address\", {\r\n        position: \"top-center\",\r\n        autoClose: 2000,\r\n      });\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n    let response;\r\n\r\n    try {\r\n      if (editingContact) {\r\n        response = await EditContact({\r\n          Id: editingContact.Id,\r\n          ContactInfo: email,\r\n          Category: \"Email\",\r\n          isPublic: true,\r\n          Title: contactName.trim(),\r\n        });\r\n      } else {\r\n        response = await CreateContact({\r\n          UserId: Id,\r\n          Name: contactName,\r\n          ContactInfo: email,\r\n          Category: \"Email\",\r\n          isPublic: true,\r\n          Title: contactName.trim(),\r\n        });\r\n      }\r\n\r\n      setContactName(\"\");\r\n      setEmail(\"\");\r\n      setContactName(\"\");\r\n      if (clearEditingContact) clearEditingContact();\r\n      setOpenEmailDialog(false);\r\n\r\n      if (response) {\r\n        toast.success(\r\n          editingContact ? \"Email contact updated\" : \"Email contact added\",\r\n          {\r\n            position: \"top-center\",\r\n            autoClose: 1000,\r\n          }\r\n        );\r\n        if (fetchProfile) fetchProfile();\r\n      } else {\r\n        toast.error(\"Error while saving email contact\", {\r\n          position: \"top-center\",\r\n          autoClose: 1000,\r\n        });\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error saving email contact:\", error);\r\n      toast.error(\"Error while saving email contact\", {\r\n        position: \"top-center\",\r\n        autoClose: 1000,\r\n      });\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Dialog\r\n      open={openEmailDialog}\r\n      onClose={() => {\r\n        setEmail(\"\");\r\n        setContactName(\"\");\r\n        if (clearEditingContact) clearEditingContact();\r\n        setOpenEmailDialog(false);\r\n      }}\r\n    >\r\n      <DialogTitle>\r\n        {editingContact ? \"Edit Email Contact\" : \"Add Email Contact\"}\r\n      </DialogTitle>\r\n      <DialogContent>\r\n        <TextField\r\n          name=\"contactName\"\r\n          autoFocus\r\n          margin=\"dense\"\r\n          label=\"Contact Name\"\r\n          type=\"text\"\r\n          fullWidth\r\n          required\r\n          value={contactName}\r\n          onChange={handleContactNameChange}\r\n          error={nameValidationError !== \"\"}\r\n          helperText={contactName === \"\" ? \"Contact name is required\" : \"\"}\r\n          sx={{ mb: 2 }}\r\n        />\r\n        <TextField\r\n          name=\"Email\"\r\n          margin=\"dense\"\r\n          label=\"Email Address\"\r\n          type=\"email\"\r\n          fullWidth\r\n          required\r\n          value={email}\r\n          onChange={handleEmailChange}\r\n          helperText={email === \"\" ? \"Email address is required\" : \"\"}\r\n        />\r\n        {/* Hints and Tips Section */}\r\n        <Box\r\n          mt={2}\r\n          p={2}\r\n          sx={{\r\n            backgroundColor: \"#f0f0f0\",\r\n            borderRadius: \"5px\",\r\n          }}\r\n        >\r\n          <Typography variant=\"subtitle1\" color=\"textPrimary\">\r\n            Tips for Adding Email Contact\r\n          </Typography>\r\n          <Typography variant=\"body2\" color=\"textSecondary\">\r\n            - Give your contact a descriptive name (e.g., \"Work Email\",\r\n            \"Personal Email\")\r\n          </Typography>\r\n          <Typography variant=\"body2\" color=\"textSecondary\">\r\n            - Enter your complete email address (e.g., <EMAIL>)\r\n          </Typography>\r\n          <Typography variant=\"body2\" color=\"textSecondary\">\r\n            - All email providers are supported (Gmail, Yahoo, Outlook, etc.)\r\n          </Typography>\r\n        </Box>\r\n      </DialogContent>\r\n      <DialogActions>\r\n        <Button\r\n          onClick={() => {\r\n            setEmail(\"\");\r\n            setContactName(\"\");\r\n            if (clearEditingContact) clearEditingContact();\r\n            setOpenEmailDialog(false);\r\n          }}\r\n        >\r\n          Cancel\r\n        </Button>\r\n        <Button\r\n          onClick={handleConfirm}\r\n          variant=\"contained\"\r\n          disabled={isLoading}\r\n        >\r\n          {isLoading ? \"Saving...\" : editingContact ? \"Update\" : \"Add\"}\r\n        </Button>\r\n      </DialogActions>\r\n    </Dialog>\r\n  );\r\n};\r\n\r\nexport default EmailLinkDialog;\r\n"], "mappings": "AAAA,OAASA,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAC3C,OACEC,MAAM,CACNC,WAAW,CACXC,aAAa,CACbC,aAAa,CACbC,SAAS,CACTC,MAAM,CACNC,UAAU,CACVC,GAAG,KACE,eAAe,CACtB,OAASC,KAAK,KAAQ,gBAAgB,CACtC,OAASC,aAAa,CAAEC,WAAW,KAAQ,yBAAyB,CACpE,OAASC,UAAU,KAAQ,iCAAiC,CAAC,OAAAC,GAAA,IAAAC,IAAA,gCAAAC,IAAA,IAAAC,KAAA,yBAE7D,KAAM,CAAAC,eAAe,CAAGC,IAAA,EASlB,IATmB,CACvBC,kBAAkB,CAClBC,eAAe,CACfC,EAAE,CACFC,cAAc,CAAG,IAAI,CACrBC,mBAAmB,CACnBC,UAAU,CACVC,aAAa,CAAG,EAAE,CAClBC,YAAY,CAAG,EACjB,CAAC,CAAAR,IAAA,CACC,KAAM,CAAES,YAAa,CAAC,CAAGf,UAAU,CAAC,CAAC,CACrC,KAAM,CAACgB,WAAW,CAAEC,cAAc,CAAC,CAAG9B,QAAQ,CAAC2B,YAAY,CAAC,CAC5D,KAAM,CAACI,KAAK,CAAEC,QAAQ,CAAC,CAAGhC,QAAQ,CAAC0B,aAAa,CAAC,CACjD,KAAM,CAACO,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGlC,QAAQ,CAAC,EAAE,CAAC,CAClE,KAAM,CAACmC,SAAS,CAAEC,YAAY,CAAC,CAAGpC,QAAQ,CAAC,KAAK,CAAC,CAEjDC,SAAS,CAAC,IAAM,CACd,GAAIsB,cAAc,CAAE,CAClB;AACAS,QAAQ,CAACT,cAAc,CAACc,OAAO,EAAId,cAAc,CAACe,WAAW,EAAI,EAAE,CAAC,CACpER,cAAc,CAACP,cAAc,CAACgB,KAAK,EAAI,EAAE,CAAC,CAC5C,CAAC,IAAM,CACL;AACAP,QAAQ,CAACN,aAAa,EAAI,EAAE,CAAC,CAC7BI,cAAc,CAACH,YAAY,EAAI,EAAE,CAAC,CACpC,CACAO,sBAAsB,CAAC,EAAE,CAAC,CAC5B,CAAC,CAAE,CAACX,cAAc,CAAEG,aAAa,CAAEC,YAAY,CAAEN,eAAe,CAAC,CAAC,CAElE,KAAM,CAAAmB,uBAAuB,CAAIC,KAAK,EAAK,CACzCX,cAAc,CAACW,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC,CAClC,GAAIF,KAAK,CAACC,MAAM,CAACC,KAAK,CAACC,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CACpCV,sBAAsB,CAAC,EAAE,CAAC,CAC5B,CACF,CAAC,CAED,KAAM,CAAAW,iBAAiB,CAAIJ,KAAK,EAAK,CACnCT,QAAQ,CAACS,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC,CAC9B,CAAC,CAED,KAAM,CAAAG,YAAY,CAAIC,KAAK,EAAK,CAC9B;AACA,KAAM,CAAAC,YAAY,CAAG,kDAAkD,CAEvE;AACA,KAAM,CAAAC,OAAO,CAAGD,YAAY,CAACE,IAAI,CAACH,KAAK,CAAC,CAExC,MAAO,CAAAE,OAAO,CAChB,CAAC,CAED,KAAM,CAAAE,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAItB,WAAW,CAACe,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CAC7BV,sBAAsB,CAAC,0BAA0B,CAAC,CAClD,OACF,CAEA,GAAI,CAACY,YAAY,CAACf,KAAK,CAAC,CAAE,CACxBrB,KAAK,CAAC0C,KAAK,CAAC,wDAAwD,CAAE,CACpEC,QAAQ,CAAE,YAAY,CACtBC,SAAS,CAAE,IACb,CAAC,CAAC,CACF,OACF,CAEAlB,YAAY,CAAC,IAAI,CAAC,CAClB,GAAI,CAAAmB,QAAQ,CAEZ,GAAI,CACF,GAAIhC,cAAc,CAAE,CAClBgC,QAAQ,CAAG,KAAM,CAAA3C,WAAW,CAAC,CAC3BU,EAAE,CAAEC,cAAc,CAACD,EAAE,CACrBgB,WAAW,CAAEP,KAAK,CAClByB,QAAQ,CAAE,OAAO,CACjBC,QAAQ,CAAE,IAAI,CACdlB,KAAK,CAAEV,WAAW,CAACe,IAAI,CAAC,CAC1B,CAAC,CAAC,CACJ,CAAC,IAAM,CACLW,QAAQ,CAAG,KAAM,CAAA5C,aAAa,CAAC,CAC7B+C,MAAM,CAAEpC,EAAE,CACVqC,IAAI,CAAE9B,WAAW,CACjBS,WAAW,CAAEP,KAAK,CAClByB,QAAQ,CAAE,OAAO,CACjBC,QAAQ,CAAE,IAAI,CACdlB,KAAK,CAAEV,WAAW,CAACe,IAAI,CAAC,CAC1B,CAAC,CAAC,CACJ,CAEAd,cAAc,CAAC,EAAE,CAAC,CAClBE,QAAQ,CAAC,EAAE,CAAC,CACZF,cAAc,CAAC,EAAE,CAAC,CAClB,GAAIN,mBAAmB,CAAEA,mBAAmB,CAAC,CAAC,CAC9CJ,kBAAkB,CAAC,KAAK,CAAC,CAEzB,GAAImC,QAAQ,CAAE,CACZ7C,KAAK,CAACkD,OAAO,CACXrC,cAAc,CAAG,uBAAuB,CAAG,qBAAqB,CAChE,CACE8B,QAAQ,CAAE,YAAY,CACtBC,SAAS,CAAE,IACb,CACF,CAAC,CACD,GAAI1B,YAAY,CAAEA,YAAY,CAAC,CAAC,CAClC,CAAC,IAAM,CACLlB,KAAK,CAAC0C,KAAK,CAAC,kCAAkC,CAAE,CAC9CC,QAAQ,CAAE,YAAY,CACtBC,SAAS,CAAE,IACb,CAAC,CAAC,CACJ,CACF,CAAE,MAAOF,KAAK,CAAE,CACdS,OAAO,CAACT,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACnD1C,KAAK,CAAC0C,KAAK,CAAC,kCAAkC,CAAE,CAC9CC,QAAQ,CAAE,YAAY,CACtBC,SAAS,CAAE,IACb,CAAC,CAAC,CACJ,CAAC,OAAS,CACRlB,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAED,mBACEnB,KAAA,CAACf,MAAM,EACL4D,IAAI,CAAEzC,eAAgB,CACtB0C,OAAO,CAAEA,CAAA,GAAM,CACb/B,QAAQ,CAAC,EAAE,CAAC,CACZF,cAAc,CAAC,EAAE,CAAC,CAClB,GAAIN,mBAAmB,CAAEA,mBAAmB,CAAC,CAAC,CAC9CJ,kBAAkB,CAAC,KAAK,CAAC,CAC3B,CAAE,CAAA4C,QAAA,eAEFjD,IAAA,CAACZ,WAAW,EAAA6D,QAAA,CACTzC,cAAc,CAAG,oBAAoB,CAAG,mBAAmB,CACjD,CAAC,cACdN,KAAA,CAACb,aAAa,EAAA4D,QAAA,eACZjD,IAAA,CAACT,SAAS,EACR2D,IAAI,CAAC,aAAa,CAClBC,SAAS,MACTC,MAAM,CAAC,OAAO,CACdC,KAAK,CAAC,cAAc,CACpBC,IAAI,CAAC,MAAM,CACXC,SAAS,MACTC,QAAQ,MACR5B,KAAK,CAAEd,WAAY,CACnB2C,QAAQ,CAAEhC,uBAAwB,CAClCY,KAAK,CAAEnB,mBAAmB,GAAK,EAAG,CAClCwC,UAAU,CAAE5C,WAAW,GAAK,EAAE,CAAG,0BAA0B,CAAG,EAAG,CACjE6C,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CACf,CAAC,cACF5D,IAAA,CAACT,SAAS,EACR2D,IAAI,CAAC,OAAO,CACZE,MAAM,CAAC,OAAO,CACdC,KAAK,CAAC,eAAe,CACrBC,IAAI,CAAC,OAAO,CACZC,SAAS,MACTC,QAAQ,MACR5B,KAAK,CAAEZ,KAAM,CACbyC,QAAQ,CAAE3B,iBAAkB,CAC5B4B,UAAU,CAAE1C,KAAK,GAAK,EAAE,CAAG,2BAA2B,CAAG,EAAG,CAC7D,CAAC,cAEFd,KAAA,CAACR,GAAG,EACFmE,EAAE,CAAE,CAAE,CACNC,CAAC,CAAE,CAAE,CACLH,EAAE,CAAE,CACFI,eAAe,CAAE,SAAS,CAC1BC,YAAY,CAAE,KAChB,CAAE,CAAAf,QAAA,eAEFjD,IAAA,CAACP,UAAU,EAACwE,OAAO,CAAC,WAAW,CAACC,KAAK,CAAC,aAAa,CAAAjB,QAAA,CAAC,+BAEpD,CAAY,CAAC,cACbjD,IAAA,CAACP,UAAU,EAACwE,OAAO,CAAC,OAAO,CAACC,KAAK,CAAC,eAAe,CAAAjB,QAAA,CAAC,mFAGlD,CAAY,CAAC,cACbjD,IAAA,CAACP,UAAU,EAACwE,OAAO,CAAC,OAAO,CAACC,KAAK,CAAC,eAAe,CAAAjB,QAAA,CAAC,8DAElD,CAAY,CAAC,cACbjD,IAAA,CAACP,UAAU,EAACwE,OAAO,CAAC,OAAO,CAACC,KAAK,CAAC,eAAe,CAAAjB,QAAA,CAAC,mEAElD,CAAY,CAAC,EACV,CAAC,EACO,CAAC,cAChB/C,KAAA,CAACZ,aAAa,EAAA2D,QAAA,eACZjD,IAAA,CAACR,MAAM,EACL2E,OAAO,CAAEA,CAAA,GAAM,CACblD,QAAQ,CAAC,EAAE,CAAC,CACZF,cAAc,CAAC,EAAE,CAAC,CAClB,GAAIN,mBAAmB,CAAEA,mBAAmB,CAAC,CAAC,CAC9CJ,kBAAkB,CAAC,KAAK,CAAC,CAC3B,CAAE,CAAA4C,QAAA,CACH,QAED,CAAQ,CAAC,cACTjD,IAAA,CAACR,MAAM,EACL2E,OAAO,CAAE/B,aAAc,CACvB6B,OAAO,CAAC,WAAW,CACnBG,QAAQ,CAAEhD,SAAU,CAAA6B,QAAA,CAEnB7B,SAAS,CAAG,WAAW,CAAGZ,cAAc,CAAG,QAAQ,CAAG,KAAK,CACtD,CAAC,EACI,CAAC,EACV,CAAC,CAEb,CAAC,CAED,cAAe,CAAAL,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module"}