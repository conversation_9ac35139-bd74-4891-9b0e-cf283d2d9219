{"ast": null, "code": "import api from \"./Api\";\nimport { BASE_URL } from \"./Context/config\";\nexport const GetProfileFromSearch = async search => {\n  console.log(search);\n  try {\n    const response = await api.get(`${BASE_URL}/Search/Search`, {\n      params: {\n        search: search\n      },\n      headers: {\n        \"Cache-Control\": \"no-cache\",\n        Pragma: \"no-cache\",\n        withCredentials: true\n      }\n    });\n    return response;\n  } catch (error) {\n    console.log(\"Error:\", error.message);\n  }\n};\n_c = GetProfileFromSearch;\nexport const PostSearch = async search => {\n  const data = {\n    UserId: search.UserId,\n    Query: search.Query,\n    Date: search.Date\n  };\n  try {\n    var authToken = getAuthToken();\n    const response = await api.post(`${BASE_URL}/Search/PostSearch`, data, {\n      headers: {\n        Authorization: `Bearer ${authToken}`,\n        \"Content-Type\": \"application/json\"\n      }\n    });\n    return response;\n  } catch (error) {\n    return {\n      error: error.message\n    };\n  }\n};\n_c2 = PostSearch;\nexport const GetSearchQueries = async () => {\n  try {\n    var authToken = getAuthToken();\n    const response = await api.get(`${BASE_URL}/Search/GetSearchQueries`, {\n      headers: {\n        Authorization: `Bearer ${authToken}`,\n        \"Content-Type\": \"application/json\"\n      }\n    });\n    return response;\n  } catch (error) {\n    return {\n      error: error.message\n    };\n  }\n};\n_c3 = GetSearchQueries;\nexport function getAuthToken() {\n  const cookies = document.cookie.split(\";\");\n  for (let i = 0; i < cookies.length; i++) {\n    const cookie = cookies[i].trim();\n    if (cookie.startsWith(\"authToken=\")) {\n      return cookie.substring(\"authToken=\".length, cookie.length);\n    }\n  }\n  return null;\n}\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"GetProfileFromSearch\");\n$RefreshReg$(_c2, \"PostSearch\");\n$RefreshReg$(_c3, \"GetSearchQueries\");", "map": {"version": 3, "names": ["api", "BASE_URL", "GetProfileFromSearch", "search", "console", "log", "response", "get", "params", "headers", "Pragma", "withCredentials", "error", "message", "_c", "PostSearch", "data", "UserId", "Query", "Date", "authToken", "getAuthToken", "post", "Authorization", "_c2", "GetSearchQueries", "_c3", "cookies", "document", "cookie", "split", "i", "length", "trim", "startsWith", "substring", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/iDigitalX/ClientApp/src/SearchData.ts"], "sourcesContent": ["import api from \"./Api\";\r\nimport { BASE_URL } from \"./Context/config\";\r\n\r\nexport interface PostSearch {\r\n  UserId: number;\r\n  Query: string;\r\n  Date: string;\r\n}\r\n\r\nexport const GetProfileFromSearch = async (search: string) => {\r\n  console.log(search);\r\n  try {\r\n    const response = await api.get(`${BASE_URL}/Search/Search`, {\r\n      params: {\r\n        search: search,\r\n      },\r\n      headers: {\r\n        \"Cache-Control\": \"no-cache\",\r\n        Pragma: \"no-cache\",\r\n        withCredentials: true,\r\n      },\r\n    });\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.log(\"Error:\", error.message);\r\n  }\r\n};\r\n\r\nexport const PostSearch = async (search: PostSearch) => {\r\n  const data = {\r\n    UserId: search.UserId,\r\n    Query: search.Query,\r\n    Date: search.Date,\r\n  };\r\n\r\n  try {\r\n    var authToken = getAuthToken();\r\n    const response = await api.post(`${BASE_URL}/Search/PostSearch`, data, {\r\n      headers: {\r\n        Authorization: `Bearer ${authToken}`,\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n    return response;\r\n  } catch (error) {\r\n    return { error: error.message };\r\n  }\r\n};\r\n\r\nexport const GetSearchQueries = async () => {\r\n  try {\r\n    var authToken = getAuthToken();\r\n    const response = await api.get(`${BASE_URL}/Search/GetSearchQueries`, {\r\n      headers: {\r\n        Authorization: `Bearer ${authToken}`,\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    return response;\r\n  } catch (error) {\r\n    return { error: error.message };\r\n  }\r\n};\r\n\r\nexport function getAuthToken() {\r\n  const cookies = document.cookie.split(\";\");\r\n\r\n  for (let i = 0; i < cookies.length; i++) {\r\n    const cookie = cookies[i].trim();\r\n    if (cookie.startsWith(\"authToken=\")) {\r\n      return cookie.substring(\"authToken=\".length, cookie.length);\r\n    }\r\n  }\r\n\r\n  return null;\r\n}\r\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,OAAO;AACvB,SAASC,QAAQ,QAAQ,kBAAkB;AAQ3C,OAAO,MAAMC,oBAAoB,GAAG,MAAOC,MAAc,IAAK;EAC5DC,OAAO,CAACC,GAAG,CAACF,MAAM,CAAC;EACnB,IAAI;IACF,MAAMG,QAAQ,GAAG,MAAMN,GAAG,CAACO,GAAG,CAAC,GAAGN,QAAQ,gBAAgB,EAAE;MAC1DO,MAAM,EAAE;QACNL,MAAM,EAAEA;MACV,CAAC;MACDM,OAAO,EAAE;QACP,eAAe,EAAE,UAAU;QAC3BC,MAAM,EAAE,UAAU;QAClBC,eAAe,EAAE;MACnB;IACF,CAAC,CAAC;IAEF,OAAOL,QAAQ;EACjB,CAAC,CAAC,OAAOM,KAAK,EAAE;IACdR,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEO,KAAK,CAACC,OAAO,CAAC;EACtC;AACF,CAAC;AAACC,EAAA,GAlBWZ,oBAAoB;AAoBjC,OAAO,MAAMa,UAAU,GAAG,MAAOZ,MAAkB,IAAK;EACtD,MAAMa,IAAI,GAAG;IACXC,MAAM,EAAEd,MAAM,CAACc,MAAM;IACrBC,KAAK,EAAEf,MAAM,CAACe,KAAK;IACnBC,IAAI,EAAEhB,MAAM,CAACgB;EACf,CAAC;EAED,IAAI;IACF,IAAIC,SAAS,GAAGC,YAAY,CAAC,CAAC;IAC9B,MAAMf,QAAQ,GAAG,MAAMN,GAAG,CAACsB,IAAI,CAAC,GAAGrB,QAAQ,oBAAoB,EAAEe,IAAI,EAAE;MACrEP,OAAO,EAAE;QACPc,aAAa,EAAE,UAAUH,SAAS,EAAE;QACpC,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IACF,OAAOd,QAAQ;EACjB,CAAC,CAAC,OAAOM,KAAK,EAAE;IACd,OAAO;MAAEA,KAAK,EAAEA,KAAK,CAACC;IAAQ,CAAC;EACjC;AACF,CAAC;AAACW,GAAA,GAnBWT,UAAU;AAqBvB,OAAO,MAAMU,gBAAgB,GAAG,MAAAA,CAAA,KAAY;EAC1C,IAAI;IACF,IAAIL,SAAS,GAAGC,YAAY,CAAC,CAAC;IAC9B,MAAMf,QAAQ,GAAG,MAAMN,GAAG,CAACO,GAAG,CAAC,GAAGN,QAAQ,0BAA0B,EAAE;MACpEQ,OAAO,EAAE;QACPc,aAAa,EAAE,UAAUH,SAAS,EAAE;QACpC,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IAEF,OAAOd,QAAQ;EACjB,CAAC,CAAC,OAAOM,KAAK,EAAE;IACd,OAAO;MAAEA,KAAK,EAAEA,KAAK,CAACC;IAAQ,CAAC;EACjC;AACF,CAAC;AAACa,GAAA,GAdWD,gBAAgB;AAgB7B,OAAO,SAASJ,YAAYA,CAAA,EAAG;EAC7B,MAAMM,OAAO,GAAGC,QAAQ,CAACC,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC;EAE1C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,OAAO,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;IACvC,MAAMF,MAAM,GAAGF,OAAO,CAACI,CAAC,CAAC,CAACE,IAAI,CAAC,CAAC;IAChC,IAAIJ,MAAM,CAACK,UAAU,CAAC,YAAY,CAAC,EAAE;MACnC,OAAOL,MAAM,CAACM,SAAS,CAAC,YAAY,CAACH,MAAM,EAAEH,MAAM,CAACG,MAAM,CAAC;IAC7D;EACF;EAEA,OAAO,IAAI;AACb;AAAC,IAAAlB,EAAA,EAAAU,GAAA,EAAAE,GAAA;AAAAU,YAAA,CAAAtB,EAAA;AAAAsB,YAAA,CAAAZ,GAAA;AAAAY,YAAA,CAAAV,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}