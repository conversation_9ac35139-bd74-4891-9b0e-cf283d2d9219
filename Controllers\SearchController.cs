using idigix.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using idigix.Services.interfaces;

namespace idigix.Controllers;
[Route("api/[controller]")]
[ApiController]
public class SearchController : ControllerBase
{
    private readonly IConfiguration _configuration;
    private readonly dbContext _cnx;
    private readonly ITokenService _tokenService;

    public SearchController(IConfiguration configuration, dbContext cnx, ITokenService tokenService)
    {
        _configuration = configuration;
        _cnx = cnx;
        _tokenService = tokenService;
    }

    [HttpGet("Search")]
    [Authorize]
    public async Task<IActionResult> Search([FromQuery] string search)
    {
        search = search.Replace(" ", "").ToLower();
        var userId = _tokenService.GetUserIdFromToken(Request.Headers["Authorization"].ToString().Replace("Bearer ", ""));

        var searchAccount = _cnx.Profile.Include(w => w.User).Where(w =>
            (
                w.User.FirstName.Contains(search)
                || w.User.LastName.Contains(search)
                || w.UserName.Contains(search)
                || (w.User.FirstName + w.User.LastName).ToLower().Contains(search)
                || (w.User.LastName + w.User.FirstName).ToLower().Contains(search)
            )

            &&
            w.isSearch == true
            &&

            w.UserId != userId
        ).OrderBy(w => w.User.Rate);

        if (searchAccount == null)
            return NotFound();

        return Ok(searchAccount);
    }
    [HttpPost("PostSearch")]
    [Authorize]
    public async Task<IActionResult> PostSearch([FromBody] postSearch search)
    {
        if (search == null || !ModelState.IsValid)
        {
            return BadRequest("Invalid search data.");
        }

        try
        {
            var newSearch = new Search
            {
                UserId = search.UserId,
                Query = search.Query,
                Date = search.Date,
            };

            _cnx.Search.Add(newSearch);

            await _cnx.SaveChangesAsync();

            return Ok();
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"An error occurred while saving Search Query: {ex.Message}");
        }
    }

    [HttpGet("GetSearchQueries")]
    [Authorize]
    public async Task<IActionResult> GetSearchQueries()
    {
        var userId = _tokenService.GetUserIdFromToken(Request.Headers["Authorization"].ToString().Replace("Bearer ", ""));

        try
        {
            var searchs = await _cnx.Search
                .Where(w => w.UserId == userId)
                .ToListAsync();

            return Ok(searchs);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"An error occurred while getting Search Queries: {ex.Message}");
        }
    }
}
