import PropTypes from "prop-types";
import { Box, Paper, Typography, Avatar } from "@mui/material";

import AutoFixHighIcon from "@mui/icons-material/AutoFixHigh";
import DeleteIcon from "@mui/icons-material/Delete";
import EmptyContent from "../Coupons/EmptyContent";

AppLinksByProfile.propTypes = {
  title: PropTypes.string,
  subheader: PropTypes.string,
  list: PropTypes.array.isRequired,
};

export default function AppLinksByProfile({
  type,
  list,
  onDelete,
  onEdit,
  ProfileCardVisible,
}) {
  const handleEdit = (link) => {
    if (ProfileCardVisible) ProfileCardVisible(false);
    onEdit(link);
  };
  return (
    <Box sx={{ width: "100%", maxWidth: "100%", overflow: "hidden" }}>
      {list.length == 0 ? (
        <EmptyContent
          description={`Looks like you have no ${type}  yet.`}
          img="/assets/illustrations/illustration_empty_content.svg"
        />
      ) : type == "socialLinks" ? (
        list.map((link, Id) => (
          <Paper
            variant="outlined"
            sx={{
              padding: "0.8rem 1.5rem",
              position: "relative",
              borderRadius: "10px",
              cursor: "pointer",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              margin: "0.6rem 0",
              marginTop: "15px",
              boxShadow: "0.1rem 0.2rem 0.2rem rgba(80, 40, 10, 0.2)",
              transition: "transform 0.3s ease-in-out",
              width: "100%",
              maxWidth: "100%",
              minWidth: 0,
              overflow: "hidden",
            }}
            key={Id}
          >
            {/* Left side: Icon and Title */}
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                flex: 1,
                minWidth: 0, // Allow text to truncate if needed
              }}
            >
              <Box
                color={link.Color}
                fontSize="18px"
                sx={{
                  marginRight: "12px",
                  flexShrink: 0, // Prevent icon from shrinking
                }}
              >
                {link.Icon}
              </Box>

              <Typography
                sx={{
                  color: "rgba(20, 43, 58, 1)",
                  fontWeight: 600,
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                  flex: 1,
                }}
              >
                {link.Title}
              </Typography>
            </Box>

            {/* Right side: Action buttons */}
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                flexShrink: 0, // Prevent buttons from shrinking
              }}
            >
              <a onClick={() => handleEdit(link)}>
                <AutoFixHighIcon
                  sx={{
                    marginRight: "10px",
                    cursor: "pointer",
                    "&:hover": {
                      color: "#ff715b",
                      fontSize: "27px",
                    },
                  }}
                />
              </a>
              <a onClick={() => onDelete(link.Id)}>
                <DeleteIcon
                  sx={{
                    cursor: "pointer",
                    "&:hover": {
                      color: "#ff715b",
                      fontSize: "27px",
                    },
                  }}
                />
              </a>
            </Box>
          </Paper>
        ))
      ) : (
        list.map((link, Id) => (
          <Paper
            sx={{
              padding: "0.8rem 1.5rem",
              position: "relative",
              borderRadius: "10px",
              cursor: "pointer",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              margin: "0.6rem 0",
              marginTop: "15px",
              boxShadow: "0.1rem 0.2rem 0.2rem rgba(80, 40, 10, 0.2)",
              transition: "transform 0.3s ease-in-out",
              width: "100%",
              maxWidth: "100%",
              minWidth: 0, // Allow flex items to shrink
              overflow: "hidden",
              "&::after": {
                content: '""',
                position: "absolute",
                top: 0,
                right: 0,
                bottom: 0,
                width: "5px",
                backgroundColor: "#ff715b",
                borderTopRightRadius: "10px",
                borderBottomRightRadius: "10px",
              },
            }}
            key={Id}
          >
            {/* Left side: Icon and Title */}
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                flex: 1,
                minWidth: 0, // Allow text to truncate if needed
              }}
            >
              {type === "contactLinks" ? (
                <Box
                  sx={{
                    width: "30px",
                    height: "30px",
                    borderRadius: "50%",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    backgroundColor: link.Color,
                    marginRight: "12px",
                    flexShrink: 0,
                    boxShadow: "0 0.4rem 0.8rem rgba(20, 43, 58, 0.25)",
                  }}
                >
                  <i
                    className={link.Icon}
                    style={{
                      color: "white",
                      fontSize: "14px",
                    }}
                  />
                </Box>
              ) : (
                <Avatar
                  style={{
                    width: "30px",
                    height: "30px",
                    borderRadius: "60%",
                    boxShadow: "0 0.4rem 0.8rem rgba(20, 43, 58, 0.25)",
                    marginRight: "12px",
                    flexShrink: 0, // Prevent avatar from shrinking
                  }}
                  src={link.Icon}
                  alt="User Profile Photo"
                />
              )}
              <Box sx={{ flex: 1, minWidth: 0 }}>
                <Typography
                  sx={{
                    color: "rgba(20, 43, 58, 1)",
                    fontWeight: 600,
                    fontSize: "15px",
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    whiteSpace: "nowrap",
                  }}
                >
                  {link.Title}
                </Typography>
                {type === "contactLinks" && link.ContactInfo && link.Name && (
                  <Typography
                    sx={{
                      color: "rgba(20, 43, 58, 0.7)",
                      fontSize: "13px",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      whiteSpace: "nowrap",
                    }}
                  >
                    {link.ContactInfo}
                  </Typography>
                )}
              </Box>
            </Box>

            {/* Right side: Action buttons */}
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                flexShrink: 0, // Prevent buttons from shrinking
              }}
            >
              <a onClick={() => handleEdit(link)}>
                <AutoFixHighIcon
                  sx={{
                    marginRight: "10px",
                    cursor: "pointer",
                    "&:hover": {
                      color: "#ff715b",
                      fontSize: "27px",
                    },
                  }}
                />
              </a>
              <a onClick={() => onDelete(link.Id)}>
                <DeleteIcon
                  sx={{
                    cursor: "pointer",
                    "&:hover": {
                      color: "#ff715b",
                      fontSize: "27px",
                    },
                  }}
                />
              </a>
            </Box>
          </Paper>
        ))
      )}
    </Box>
  );
}
