{"ast": null, "code": "import api from \"./Api\";\nimport { BASE_URL } from \"./Context/config\";\nimport Cookies from \"js-cookie\";\nconst handleRandomPhotoSelect = async () => {\n  const randomAvatarNumber = Math.floor(Math.random() * 24) + 1;\n  const randomAvatarUrl = `/assets/images/avatars/avatar_${randomAvatarNumber}.jpg`;\n  const response = await fetch(randomAvatarUrl);\n  const blob = await response.blob();\n  return new Promise(resolve => {\n    const reader = new FileReader();\n    reader.onloadend = () => {\n      resolve(reader.result);\n    };\n    reader.readAsDataURL(blob);\n  });\n};\nexport const Register = async user => {\n  const profilePicture = user.profilePicture || (await handleRandomPhotoSelect());\n  const data = {\n    Email: user.email,\n    FirstName: user.firstName,\n    LastName: user.lastName,\n    UserName: user.userName,\n    Password: user.password,\n    Gender: user.gender,\n    Category: user.categoryUser,\n    ContactInfo: user.contactInfo,\n    ContactCategory: user.contactCategory,\n    ProfilePicture: profilePicture\n  };\n  try {\n    const response = await api.post(`${BASE_URL}/Auth/Register`, data, {\n      headers: {\n        \"Content-Type\": \"application/json\"\n      }\n    });\n    localStorage.setItem(\"isEmailSent\", \"true\");\n    return response;\n  } catch (error) {\n    return {\n      error: error.response.data\n    };\n  }\n};\n_c = Register;\nexport const verifyEmail = async token => {\n  try {\n    const response = await api.get(`${BASE_URL}/Auth/VerifyEmail/${token}`);\n    return response;\n  } catch (error) {\n    console.error(\"Error while verifying email:\", error);\n    return {\n      error: \"An error occurred while verifying the email. Please try again later.\"\n    };\n  }\n};\nexport const resendVerificationEmail = async email => {\n  try {\n    const response = await api.post(`${BASE_URL}/Auth/ResendVerificationEmail`, JSON.stringify(email), {\n      headers: {\n        \"Content-Type\": \"application/json\"\n      }\n    });\n    return response;\n  } catch (error) {\n    console.error(\"Error while resending verification email:\", error);\n    if (error.response && error.response.data && error.response.data.error) {\n      return {\n        error: error.response.data.error\n      };\n    }\n    return {\n      error: \"An error occurred while resending verification email. Please try again later.\"\n    };\n  }\n};\nexport const ForgotPassword = async email => {\n  try {\n    const response = await api.post(`${BASE_URL}/Auth/ForgotPassword`, email, {\n      headers: {\n        \"Content-Type\": \"application/json\"\n      }\n    });\n    return response;\n  } catch (error) {\n    if (error.response) {\n      return error.response;\n    }\n    return {\n      error: \"An error occurred while forgot password. Please try again later.\"\n    };\n  }\n};\n_c2 = ForgotPassword;\nexport const ChangePassword = async password => {\n  try {\n    const response = await api.post(`${BASE_URL}/Auth/ChangePassword`, password, {\n      headers: {\n        \"Content-Type\": \"application/json\"\n      }\n    });\n    return response;\n  } catch (error) {\n    console.error(\"Error while changing password:\", error);\n    return {\n      error: \"An error occurred while changing password. Please try again later.\"\n    };\n  }\n};\n_c3 = ChangePassword;\nexport const VerifyPasswordChanging = async (confirmPassword, token) => {\n  try {\n    const response = await api.post(`${BASE_URL}/Auth/VerifyPasswordChanging/${token}`, confirmPassword, {\n      headers: {\n        \"Content-Type\": \"application/json\"\n      }\n    });\n    return response;\n  } catch (error) {\n    console.error(\"Error while verifying changing password:\", error);\n    if (error.response) {\n      return error.response;\n    }\n    return {\n      status: 500,\n      data: {\n        error: \"An error occurred while verifying changing password. Please try again later.\"\n      }\n    };\n  }\n};\n_c4 = VerifyPasswordChanging;\nexport const Login = async user => {\n  const data = {\n    Email: user.email,\n    Password: user.password\n  };\n  try {\n    const response = await api.post(`/Auth/Login`, data, {\n      headers: {\n        \"Content-Type\": \"application/json\"\n      }\n    });\n    const expiresIn = new Date(Date.parse(response.data.expiresIn));\n    expiresIn.setHours(expiresIn.getHours() + 1);\n    setAuthTokens(response.data.accessToken, response.data.refreshToken, expiresIn);\n    const expires = new Date(expiresIn);\n    Cookies.set(\"authToken\", response.data.accessToken, {\n      expires: expires,\n      path: \"/\",\n      ...(process.env.REACT_APP_ENV === \"production\" && {\n        domain: \"idigics.com\"\n      })\n    });\n    Cookies.set(\"refreshToken\", response.data.refreshToken, {\n      expires: expires,\n      path: \"/\",\n      ...(process.env.REACT_APP_ENV === \"production\" && {\n        domain: \"idigics.com\"\n      })\n    });\n    return response;\n  } catch (error) {\n    if (error.response && error.response.data && error.response.data.error) {\n      return {\n        error: error.response.data.error\n      };\n    }\n    return null; // Return null for other errors\n  }\n};\n_c5 = Login;\nexport const Logout = async () => {\n  const cookieOptions = {\n    path: \"/\",\n    ...(process.env.REACT_APP_ENV === \"production\" && {\n      domain: \"idigics.com\"\n    })\n  };\n  Cookies.remove(\"authToken\", cookieOptions);\n  Cookies.remove(\"refreshToken\", cookieOptions);\n};\n_c6 = Logout;\nexport const RefreshToken = async () => {\n  const data = {\n    refreshToken: getRefreshToken()\n  };\n  try {\n    const response = await api.post(`${BASE_URL}/Auth/RefreshToken`, data, {\n      headers: {\n        \"Content-Type\": \"application/json\"\n      }\n    });\n    setAuthTokens(response.data.accessToken, response.data.refreshToken, response.data.expiresIn);\n    return response;\n  } catch (error) {\n    return null;\n  }\n};\n_c7 = RefreshToken;\nexport function setAuthTokens(accessToken, refreshToken, expiresIn) {\n  const expires = new Date(expiresIn);\n  const domain = process.env.REACT_APP_ENV === \"production\" ? \"idigics.com\" : \"\";\n\n  // Set cookies with or without domain based on the environment\n  document.cookie = `authToken=${accessToken}; expires=${expires.toUTCString()}; path=/; domain=${domain}`;\n  document.cookie = `refreshToken=${refreshToken}; expires=${expires.toUTCString()}; path=/; domain=${domain}`;\n}\n\n// export function setAuthToken(token: string) {\n//     const expires = new Date();\n//     expires.setDate(expires.getDate() + 3);\n//     document.cookie = `authToken=${token}; expires=${expires.toUTCString()}; path=/`;\n// }\n\nexport function getRefreshToken() {\n  const cookies = document.cookie.split(\";\");\n  for (let i = 0; i < cookies.length; i++) {\n    const cookie = cookies[i].trim();\n    if (cookie.startsWith(\"refreshToken=\")) {\n      const refreshToken = cookie.substring(\"refreshToken=\".length, cookie.length);\n      if (refreshToken.length > 0) {\n        return refreshToken;\n      } else {\n        console.error(\"Refresh token found, but it's empty or malformed.\");\n        return null;\n      }\n    }\n  }\n  return null;\n}\nexport function checkAuthToken() {\n  const cookies = document.cookie.split(\";\");\n  for (let i = 0; i < cookies.length; i++) {\n    const cookie = cookies[i].trim();\n    if (cookie.startsWith(\"authToken=\")) {\n      return true;\n    }\n  }\n  return false;\n}\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"Register\");\n$RefreshReg$(_c2, \"ForgotPassword\");\n$RefreshReg$(_c3, \"ChangePassword\");\n$RefreshReg$(_c4, \"VerifyPasswordChanging\");\n$RefreshReg$(_c5, \"Login\");\n$RefreshReg$(_c6, \"Logout\");\n$RefreshReg$(_c7, \"RefreshToken\");", "map": {"version": 3, "names": ["api", "BASE_URL", "Cookies", "handleRandomPhotoSelect", "randomAvatarNumber", "Math", "floor", "random", "randomAvatarUrl", "response", "fetch", "blob", "Promise", "resolve", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "Register", "user", "profilePicture", "data", "Email", "email", "FirstName", "firstName", "LastName", "lastName", "UserName", "userName", "Password", "password", "Gender", "gender", "Category", "categoryUser", "ContactInfo", "contactInfo", "ContactCategory", "contactCategory", "ProfilePicture", "post", "headers", "localStorage", "setItem", "error", "_c", "verifyEmail", "token", "get", "console", "resendVerificationEmail", "JSON", "stringify", "ForgotPassword", "_c2", "ChangePassword", "_c3", "VerifyPasswordChanging", "confirmPassword", "status", "_c4", "<PERSON><PERSON>", "expiresIn", "Date", "parse", "setHours", "getHours", "setAuthTokens", "accessToken", "refreshToken", "expires", "set", "path", "process", "env", "REACT_APP_ENV", "domain", "_c5", "Logout", "cookieOptions", "remove", "_c6", "RefreshToken", "getRefreshToken", "_c7", "document", "cookie", "toUTCString", "cookies", "split", "i", "length", "trim", "startsWith", "substring", "checkAuthToken", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/iDigitalX/ClientApp/src/AuthenticationData.ts"], "sourcesContent": ["import api from \"./Api\";\r\nimport { BASE_URL } from \"./Context/config\";\r\nimport Cookies from \"js-cookie\";\r\n\r\nexport interface PostUserData {\r\n  email: string;\r\n  firstName: string;\r\n  lastName: string;\r\n  userName: string;\r\n  password: string;\r\n  gender: string;\r\n  contactInfo: string;\r\n  contactCategory: string;\r\n  categoryUser: string;\r\n  profilePicture: string;\r\n  profileCoverPicture: string;\r\n}\r\n\r\nexport interface LogUserData {\r\n  email: string;\r\n  password: string;\r\n}\r\n\r\nconst handleRandomPhotoSelect = async () => {\r\n  const randomAvatarNumber = Math.floor(Math.random() * 24) + 1;\r\n  const randomAvatarUrl = `/assets/images/avatars/avatar_${randomAvatarNumber}.jpg`;\r\n\r\n  const response = await fetch(randomAvatarUrl);\r\n  const blob = await response.blob();\r\n\r\n  return new Promise((resolve) => {\r\n    const reader = new FileReader();\r\n    reader.onloadend = () => {\r\n      resolve(reader.result);\r\n    };\r\n    reader.readAsDataURL(blob);\r\n  });\r\n};\r\n\r\nexport const Register = async (user: PostUserData) => {\r\n  const profilePicture =\r\n    user.profilePicture || (await handleRandomPhotoSelect());\r\n  const data = {\r\n    Email: user.email,\r\n    FirstName: user.firstName,\r\n    LastName: user.lastName,\r\n    UserName: user.userName,\r\n    Password: user.password,\r\n    Gender: user.gender,\r\n    Category: user.categoryUser,\r\n    ContactInfo: user.contactInfo,\r\n    ContactCategory: user.contactCategory,\r\n    ProfilePicture: profilePicture,\r\n  };\r\n\r\n  try {\r\n    const response = await api.post(`${BASE_URL}/Auth/Register`, data, {\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    localStorage.setItem(\"isEmailSent\", \"true\");\r\n\r\n    return response;\r\n  } catch (error) {\r\n    return { error: error.response.data };\r\n  }\r\n};\r\n\r\nexport const verifyEmail = async (token: string) => {\r\n  try {\r\n    const response = await api.get(`${BASE_URL}/Auth/VerifyEmail/${token}`);\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Error while verifying email:\", error);\r\n    return {\r\n      error:\r\n        \"An error occurred while verifying the email. Please try again later.\",\r\n    };\r\n  }\r\n};\r\n\r\nexport const resendVerificationEmail = async (email: string) => {\r\n  try {\r\n    const response = await api.post(\r\n      `${BASE_URL}/Auth/ResendVerificationEmail`,\r\n      JSON.stringify(email),\r\n      {\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n      }\r\n    );\r\n    return response;\r\n  } catch (error: any) {\r\n    console.error(\"Error while resending verification email:\", error);\r\n    if (error.response && error.response.data && error.response.data.error) {\r\n      return { error: error.response.data.error };\r\n    }\r\n    return {\r\n      error:\r\n        \"An error occurred while resending verification email. Please try again later.\",\r\n    };\r\n  }\r\n};\r\n\r\nexport const ForgotPassword = async (email: string) => {\r\n  try {\r\n    const response = await api.post(`${BASE_URL}/Auth/ForgotPassword`, email, {\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n    return response;\r\n  } catch (error: any) {\r\n    if (error.response) {\r\n      return error.response;\r\n    }\r\n    return {\r\n      error: \"An error occurred while forgot password. Please try again later.\",\r\n    };\r\n  }\r\n};\r\n\r\nexport const ChangePassword = async (password: string) => {\r\n  try {\r\n    const response = await api.post(\r\n      `${BASE_URL}/Auth/ChangePassword`,\r\n      password,\r\n      {\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n      }\r\n    );\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Error while changing password:\", error);\r\n    return {\r\n      error:\r\n        \"An error occurred while changing password. Please try again later.\",\r\n    };\r\n  }\r\n};\r\n\r\nexport const VerifyPasswordChanging = async (\r\n  confirmPassword: string,\r\n  token: string\r\n) => {\r\n  try {\r\n    const response = await api.post(\r\n      `${BASE_URL}/Auth/VerifyPasswordChanging/${token}`,\r\n      confirmPassword,\r\n      {\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n      }\r\n    );\r\n    return response;\r\n  } catch (error: any) {\r\n    console.error(\"Error while verifying changing password:\", error);\r\n    if (error.response) {\r\n      return error.response;\r\n    }\r\n    return {\r\n      status: 500,\r\n      data: {\r\n        error:\r\n          \"An error occurred while verifying changing password. Please try again later.\",\r\n      },\r\n    };\r\n  }\r\n};\r\n\r\nexport const Login = async (user) => {\r\n  const data = {\r\n    Email: user.email,\r\n    Password: user.password,\r\n  };\r\n\r\n  try {\r\n    const response = await api.post(`/Auth/Login`, data, {\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    const expiresIn = new Date(Date.parse(response.data.expiresIn));\r\n    expiresIn.setHours(expiresIn.getHours() + 1);\r\n\r\n    setAuthTokens(\r\n      response.data.accessToken,\r\n      response.data.refreshToken,\r\n      expiresIn\r\n    );\r\n\r\n    const expires = new Date(expiresIn);\r\n\r\n    Cookies.set(\"authToken\", response.data.accessToken, {\r\n      expires: expires,\r\n      path: \"/\",\r\n      ...(process.env.REACT_APP_ENV === \"production\" && {\r\n        domain: \"idigics.com\",\r\n      }),\r\n    });\r\n\r\n    Cookies.set(\"refreshToken\", response.data.refreshToken, {\r\n      expires: expires,\r\n      path: \"/\",\r\n      ...(process.env.REACT_APP_ENV === \"production\" && {\r\n        domain: \"idigics.com\",\r\n      }),\r\n    });\r\n\r\n    return response;\r\n  } catch (error) {\r\n    if (error.response && error.response.data && error.response.data.error) {\r\n      return { error: error.response.data.error };\r\n    }\r\n    return null; // Return null for other errors\r\n  }\r\n};\r\n\r\nexport const Logout = async () => {\r\n  const cookieOptions = {\r\n    path: \"/\",\r\n    ...(process.env.REACT_APP_ENV === \"production\" && {\r\n      domain: \"idigics.com\",\r\n    }),\r\n  };\r\n\r\n  Cookies.remove(\"authToken\", cookieOptions);\r\n  Cookies.remove(\"refreshToken\", cookieOptions);\r\n};\r\n\r\nexport const RefreshToken = async () => {\r\n  const data = {\r\n    refreshToken: getRefreshToken(),\r\n  };\r\n\r\n  try {\r\n    const response = await api.post(`${BASE_URL}/Auth/RefreshToken`, data, {\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    setAuthTokens(\r\n      response.data.accessToken,\r\n      response.data.refreshToken,\r\n      response.data.expiresIn\r\n    );\r\n\r\n    return response;\r\n  } catch (error) {\r\n    return null;\r\n  }\r\n};\r\n\r\nexport function setAuthTokens(accessToken, refreshToken, expiresIn) {\r\n  const expires = new Date(expiresIn);\r\n\r\n  const domain =\r\n    process.env.REACT_APP_ENV === \"production\" ? \"idigics.com\" : \"\";\r\n\r\n  // Set cookies with or without domain based on the environment\r\n  document.cookie = `authToken=${accessToken}; expires=${expires.toUTCString()}; path=/; domain=${domain}`;\r\n  document.cookie = `refreshToken=${refreshToken}; expires=${expires.toUTCString()}; path=/; domain=${domain}`;\r\n}\r\n\r\n// export function setAuthToken(token: string) {\r\n//     const expires = new Date();\r\n//     expires.setDate(expires.getDate() + 3);\r\n//     document.cookie = `authToken=${token}; expires=${expires.toUTCString()}; path=/`;\r\n// }\r\n\r\nexport function getRefreshToken() {\r\n  const cookies = document.cookie.split(\";\");\r\n\r\n  for (let i = 0; i < cookies.length; i++) {\r\n    const cookie = cookies[i].trim();\r\n    if (cookie.startsWith(\"refreshToken=\")) {\r\n      const refreshToken = cookie.substring(\r\n        \"refreshToken=\".length,\r\n        cookie.length\r\n      );\r\n      if (refreshToken.length > 0) {\r\n        return refreshToken;\r\n      } else {\r\n        console.error(\"Refresh token found, but it's empty or malformed.\");\r\n        return null;\r\n      }\r\n    }\r\n  }\r\n\r\n  return null;\r\n}\r\n\r\nexport function checkAuthToken() {\r\n  const cookies = document.cookie.split(\";\");\r\n\r\n  for (let i = 0; i < cookies.length; i++) {\r\n    const cookie = cookies[i].trim();\r\n    if (cookie.startsWith(\"authToken=\")) {\r\n      return true;\r\n    }\r\n  }\r\n\r\n  return false;\r\n}\r\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,OAAO;AACvB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,OAAOC,OAAO,MAAM,WAAW;AAqB/B,MAAMC,uBAAuB,GAAG,MAAAA,CAAA,KAAY;EAC1C,MAAMC,kBAAkB,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC;EAC7D,MAAMC,eAAe,GAAG,iCAAiCJ,kBAAkB,MAAM;EAEjF,MAAMK,QAAQ,GAAG,MAAMC,KAAK,CAACF,eAAe,CAAC;EAC7C,MAAMG,IAAI,GAAG,MAAMF,QAAQ,CAACE,IAAI,CAAC,CAAC;EAElC,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAK;IAC9B,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC/BD,MAAM,CAACE,SAAS,GAAG,MAAM;MACvBH,OAAO,CAACC,MAAM,CAACG,MAAM,CAAC;IACxB,CAAC;IACDH,MAAM,CAACI,aAAa,CAACP,IAAI,CAAC;EAC5B,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMQ,QAAQ,GAAG,MAAOC,IAAkB,IAAK;EACpD,MAAMC,cAAc,GAClBD,IAAI,CAACC,cAAc,KAAK,MAAMlB,uBAAuB,CAAC,CAAC,CAAC;EAC1D,MAAMmB,IAAI,GAAG;IACXC,KAAK,EAAEH,IAAI,CAACI,KAAK;IACjBC,SAAS,EAAEL,IAAI,CAACM,SAAS;IACzBC,QAAQ,EAAEP,IAAI,CAACQ,QAAQ;IACvBC,QAAQ,EAAET,IAAI,CAACU,QAAQ;IACvBC,QAAQ,EAAEX,IAAI,CAACY,QAAQ;IACvBC,MAAM,EAAEb,IAAI,CAACc,MAAM;IACnBC,QAAQ,EAAEf,IAAI,CAACgB,YAAY;IAC3BC,WAAW,EAAEjB,IAAI,CAACkB,WAAW;IAC7BC,eAAe,EAAEnB,IAAI,CAACoB,eAAe;IACrCC,cAAc,EAAEpB;EAClB,CAAC;EAED,IAAI;IACF,MAAMZ,QAAQ,GAAG,MAAMT,GAAG,CAAC0C,IAAI,CAAC,GAAGzC,QAAQ,gBAAgB,EAAEqB,IAAI,EAAE;MACjEqB,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IAEFC,YAAY,CAACC,OAAO,CAAC,aAAa,EAAE,MAAM,CAAC;IAE3C,OAAOpC,QAAQ;EACjB,CAAC,CAAC,OAAOqC,KAAK,EAAE;IACd,OAAO;MAAEA,KAAK,EAAEA,KAAK,CAACrC,QAAQ,CAACa;IAAK,CAAC;EACvC;AACF,CAAC;AAACyB,EAAA,GA7BW5B,QAAQ;AA+BrB,OAAO,MAAM6B,WAAW,GAAG,MAAOC,KAAa,IAAK;EAClD,IAAI;IACF,MAAMxC,QAAQ,GAAG,MAAMT,GAAG,CAACkD,GAAG,CAAC,GAAGjD,QAAQ,qBAAqBgD,KAAK,EAAE,CAAC;IACvE,OAAOxC,QAAQ;EACjB,CAAC,CAAC,OAAOqC,KAAK,EAAE;IACdK,OAAO,CAACL,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACpD,OAAO;MACLA,KAAK,EACH;IACJ,CAAC;EACH;AACF,CAAC;AAED,OAAO,MAAMM,uBAAuB,GAAG,MAAO5B,KAAa,IAAK;EAC9D,IAAI;IACF,MAAMf,QAAQ,GAAG,MAAMT,GAAG,CAAC0C,IAAI,CAC7B,GAAGzC,QAAQ,+BAA+B,EAC1CoD,IAAI,CAACC,SAAS,CAAC9B,KAAK,CAAC,EACrB;MACEmB,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CACF,CAAC;IACD,OAAOlC,QAAQ;EACjB,CAAC,CAAC,OAAOqC,KAAU,EAAE;IACnBK,OAAO,CAACL,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;IACjE,IAAIA,KAAK,CAACrC,QAAQ,IAAIqC,KAAK,CAACrC,QAAQ,CAACa,IAAI,IAAIwB,KAAK,CAACrC,QAAQ,CAACa,IAAI,CAACwB,KAAK,EAAE;MACtE,OAAO;QAAEA,KAAK,EAAEA,KAAK,CAACrC,QAAQ,CAACa,IAAI,CAACwB;MAAM,CAAC;IAC7C;IACA,OAAO;MACLA,KAAK,EACH;IACJ,CAAC;EACH;AACF,CAAC;AAED,OAAO,MAAMS,cAAc,GAAG,MAAO/B,KAAa,IAAK;EACrD,IAAI;IACF,MAAMf,QAAQ,GAAG,MAAMT,GAAG,CAAC0C,IAAI,CAAC,GAAGzC,QAAQ,sBAAsB,EAAEuB,KAAK,EAAE;MACxEmB,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IACF,OAAOlC,QAAQ;EACjB,CAAC,CAAC,OAAOqC,KAAU,EAAE;IACnB,IAAIA,KAAK,CAACrC,QAAQ,EAAE;MAClB,OAAOqC,KAAK,CAACrC,QAAQ;IACvB;IACA,OAAO;MACLqC,KAAK,EAAE;IACT,CAAC;EACH;AACF,CAAC;AAACU,GAAA,GAhBWD,cAAc;AAkB3B,OAAO,MAAME,cAAc,GAAG,MAAOzB,QAAgB,IAAK;EACxD,IAAI;IACF,MAAMvB,QAAQ,GAAG,MAAMT,GAAG,CAAC0C,IAAI,CAC7B,GAAGzC,QAAQ,sBAAsB,EACjC+B,QAAQ,EACR;MACEW,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CACF,CAAC;IACD,OAAOlC,QAAQ;EACjB,CAAC,CAAC,OAAOqC,KAAK,EAAE;IACdK,OAAO,CAACL,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACtD,OAAO;MACLA,KAAK,EACH;IACJ,CAAC;EACH;AACF,CAAC;AAACY,GAAA,GAnBWD,cAAc;AAqB3B,OAAO,MAAME,sBAAsB,GAAG,MAAAA,CACpCC,eAAuB,EACvBX,KAAa,KACV;EACH,IAAI;IACF,MAAMxC,QAAQ,GAAG,MAAMT,GAAG,CAAC0C,IAAI,CAC7B,GAAGzC,QAAQ,gCAAgCgD,KAAK,EAAE,EAClDW,eAAe,EACf;MACEjB,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CACF,CAAC;IACD,OAAOlC,QAAQ;EACjB,CAAC,CAAC,OAAOqC,KAAU,EAAE;IACnBK,OAAO,CAACL,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;IAChE,IAAIA,KAAK,CAACrC,QAAQ,EAAE;MAClB,OAAOqC,KAAK,CAACrC,QAAQ;IACvB;IACA,OAAO;MACLoD,MAAM,EAAE,GAAG;MACXvC,IAAI,EAAE;QACJwB,KAAK,EACH;MACJ;IACF,CAAC;EACH;AACF,CAAC;AAACgB,GAAA,GA5BWH,sBAAsB;AA8BnC,OAAO,MAAMI,KAAK,GAAG,MAAO3C,IAAI,IAAK;EACnC,MAAME,IAAI,GAAG;IACXC,KAAK,EAAEH,IAAI,CAACI,KAAK;IACjBO,QAAQ,EAAEX,IAAI,CAACY;EACjB,CAAC;EAED,IAAI;IACF,MAAMvB,QAAQ,GAAG,MAAMT,GAAG,CAAC0C,IAAI,CAAC,aAAa,EAAEpB,IAAI,EAAE;MACnDqB,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IAEF,MAAMqB,SAAS,GAAG,IAAIC,IAAI,CAACA,IAAI,CAACC,KAAK,CAACzD,QAAQ,CAACa,IAAI,CAAC0C,SAAS,CAAC,CAAC;IAC/DA,SAAS,CAACG,QAAQ,CAACH,SAAS,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;IAE5CC,aAAa,CACX5D,QAAQ,CAACa,IAAI,CAACgD,WAAW,EACzB7D,QAAQ,CAACa,IAAI,CAACiD,YAAY,EAC1BP,SACF,CAAC;IAED,MAAMQ,OAAO,GAAG,IAAIP,IAAI,CAACD,SAAS,CAAC;IAEnC9D,OAAO,CAACuE,GAAG,CAAC,WAAW,EAAEhE,QAAQ,CAACa,IAAI,CAACgD,WAAW,EAAE;MAClDE,OAAO,EAAEA,OAAO;MAChBE,IAAI,EAAE,GAAG;MACT,IAAIC,OAAO,CAACC,GAAG,CAACC,aAAa,KAAK,YAAY,IAAI;QAChDC,MAAM,EAAE;MACV,CAAC;IACH,CAAC,CAAC;IAEF5E,OAAO,CAACuE,GAAG,CAAC,cAAc,EAAEhE,QAAQ,CAACa,IAAI,CAACiD,YAAY,EAAE;MACtDC,OAAO,EAAEA,OAAO;MAChBE,IAAI,EAAE,GAAG;MACT,IAAIC,OAAO,CAACC,GAAG,CAACC,aAAa,KAAK,YAAY,IAAI;QAChDC,MAAM,EAAE;MACV,CAAC;IACH,CAAC,CAAC;IAEF,OAAOrE,QAAQ;EACjB,CAAC,CAAC,OAAOqC,KAAK,EAAE;IACd,IAAIA,KAAK,CAACrC,QAAQ,IAAIqC,KAAK,CAACrC,QAAQ,CAACa,IAAI,IAAIwB,KAAK,CAACrC,QAAQ,CAACa,IAAI,CAACwB,KAAK,EAAE;MACtE,OAAO;QAAEA,KAAK,EAAEA,KAAK,CAACrC,QAAQ,CAACa,IAAI,CAACwB;MAAM,CAAC;IAC7C;IACA,OAAO,IAAI,CAAC,CAAC;EACf;AACF,CAAC;AAACiC,GAAA,GA/CWhB,KAAK;AAiDlB,OAAO,MAAMiB,MAAM,GAAG,MAAAA,CAAA,KAAY;EAChC,MAAMC,aAAa,GAAG;IACpBP,IAAI,EAAE,GAAG;IACT,IAAIC,OAAO,CAACC,GAAG,CAACC,aAAa,KAAK,YAAY,IAAI;MAChDC,MAAM,EAAE;IACV,CAAC;EACH,CAAC;EAED5E,OAAO,CAACgF,MAAM,CAAC,WAAW,EAAED,aAAa,CAAC;EAC1C/E,OAAO,CAACgF,MAAM,CAAC,cAAc,EAAED,aAAa,CAAC;AAC/C,CAAC;AAACE,GAAA,GAVWH,MAAM;AAYnB,OAAO,MAAMI,YAAY,GAAG,MAAAA,CAAA,KAAY;EACtC,MAAM9D,IAAI,GAAG;IACXiD,YAAY,EAAEc,eAAe,CAAC;EAChC,CAAC;EAED,IAAI;IACF,MAAM5E,QAAQ,GAAG,MAAMT,GAAG,CAAC0C,IAAI,CAAC,GAAGzC,QAAQ,oBAAoB,EAAEqB,IAAI,EAAE;MACrEqB,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IAEF0B,aAAa,CACX5D,QAAQ,CAACa,IAAI,CAACgD,WAAW,EACzB7D,QAAQ,CAACa,IAAI,CAACiD,YAAY,EAC1B9D,QAAQ,CAACa,IAAI,CAAC0C,SAChB,CAAC;IAED,OAAOvD,QAAQ;EACjB,CAAC,CAAC,OAAOqC,KAAK,EAAE;IACd,OAAO,IAAI;EACb;AACF,CAAC;AAACwC,GAAA,GAtBWF,YAAY;AAwBzB,OAAO,SAASf,aAAaA,CAACC,WAAW,EAAEC,YAAY,EAAEP,SAAS,EAAE;EAClE,MAAMQ,OAAO,GAAG,IAAIP,IAAI,CAACD,SAAS,CAAC;EAEnC,MAAMc,MAAM,GACVH,OAAO,CAACC,GAAG,CAACC,aAAa,KAAK,YAAY,GAAG,aAAa,GAAG,EAAE;;EAEjE;EACAU,QAAQ,CAACC,MAAM,GAAG,aAAalB,WAAW,aAAaE,OAAO,CAACiB,WAAW,CAAC,CAAC,oBAAoBX,MAAM,EAAE;EACxGS,QAAQ,CAACC,MAAM,GAAG,gBAAgBjB,YAAY,aAAaC,OAAO,CAACiB,WAAW,CAAC,CAAC,oBAAoBX,MAAM,EAAE;AAC9G;;AAEA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASO,eAAeA,CAAA,EAAG;EAChC,MAAMK,OAAO,GAAGH,QAAQ,CAACC,MAAM,CAACG,KAAK,CAAC,GAAG,CAAC;EAE1C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,OAAO,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACvC,MAAMJ,MAAM,GAAGE,OAAO,CAACE,CAAC,CAAC,CAACE,IAAI,CAAC,CAAC;IAChC,IAAIN,MAAM,CAACO,UAAU,CAAC,eAAe,CAAC,EAAE;MACtC,MAAMxB,YAAY,GAAGiB,MAAM,CAACQ,SAAS,CACnC,eAAe,CAACH,MAAM,EACtBL,MAAM,CAACK,MACT,CAAC;MACD,IAAItB,YAAY,CAACsB,MAAM,GAAG,CAAC,EAAE;QAC3B,OAAOtB,YAAY;MACrB,CAAC,MAAM;QACLpB,OAAO,CAACL,KAAK,CAAC,mDAAmD,CAAC;QAClE,OAAO,IAAI;MACb;IACF;EACF;EAEA,OAAO,IAAI;AACb;AAEA,OAAO,SAASmD,cAAcA,CAAA,EAAG;EAC/B,MAAMP,OAAO,GAAGH,QAAQ,CAACC,MAAM,CAACG,KAAK,CAAC,GAAG,CAAC;EAE1C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,OAAO,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACvC,MAAMJ,MAAM,GAAGE,OAAO,CAACE,CAAC,CAAC,CAACE,IAAI,CAAC,CAAC;IAChC,IAAIN,MAAM,CAACO,UAAU,CAAC,YAAY,CAAC,EAAE;MACnC,OAAO,IAAI;IACb;EACF;EAEA,OAAO,KAAK;AACd;AAAC,IAAAhD,EAAA,EAAAS,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAiB,GAAA,EAAAI,GAAA,EAAAG,GAAA;AAAAY,YAAA,CAAAnD,EAAA;AAAAmD,YAAA,CAAA1C,GAAA;AAAA0C,YAAA,CAAAxC,GAAA;AAAAwC,YAAA,CAAApC,GAAA;AAAAoC,YAAA,CAAAnB,GAAA;AAAAmB,YAAA,CAAAf,GAAA;AAAAe,YAAA,CAAAZ,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}