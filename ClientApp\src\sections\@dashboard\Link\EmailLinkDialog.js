import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  <PERSON>alogA<PERSON>,
  <PERSON><PERSON>ield,
  Button,
  Typography,
  Box,
} from "@mui/material";
import { toast } from "react-toastify";
import { CreateContact, EditContact } from "../../../ContactData.ts";
import { useProfile } from "../../../Context/ProfileContext";

const EmailLinkDialog = ({
  setOpenEmailDialog,
  openEmailDialog,
  Id,
  editingContact = null,
  clearEditingContact,
  EmailExist,
  existingEmail = "",
  existingName = "",
}) => {
  const { fetchProfile } = useProfile();
  const [contactName, setContactName] = useState(existingName);
  const [email, setEmail] = useState(existingEmail);
  const [nameValidationError, setNameValidationError] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (editingContact) {
      // Pre-populate form with existing contact data when editing
      setEmail(editingContact.LinkUrl || editingContact.ContactInfo || "");
      setContactName(editingContact.Title || "");
    } else {
      // Use existing props for backward compatibility or clear form for new contact
      setEmail(existingEmail || "");
      setContactName(existingName || "");
    }
    setNameValidationError("");
  }, [editingContact, existingEmail, existingName, openEmailDialog]);

  const handleContactNameChange = (event) => {
    setContactName(event.target.value);
    if (event.target.value.trim() !== "") {
      setNameValidationError("");
    }
  };

  const handleEmailChange = (event) => {
    setEmail(event.target.value);
  };

  const isValidEmail = (input) => {
    // Regular expression pattern to match all email addresses
    const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

    // Check if the input matches email pattern
    const isEmail = emailPattern.test(input);

    return isEmail;
  };

  const handleConfirm = async () => {
    if (contactName.trim() === "") {
      setNameValidationError("Contact name is required");
      return;
    }

    if (!isValidEmail(email)) {
      toast.error("Invalid email format. Please use a valid email address", {
        position: "top-center",
        autoClose: 2000,
      });
      return;
    }

    setIsLoading(true);
    let response;

    try {
      if (editingContact) {
        response = await EditContact({
          Id: editingContact.Id,
          ContactInfo: email,
          Category: "Email",
          isPublic: true,
          Title: contactName.trim(),
        });
      } else {
        response = await CreateContact({
          UserId: Id,
          Name: contactName,
          ContactInfo: email,
          Category: "Email",
          isPublic: true,
          Title: contactName.trim(),
        });
      }

      setContactName("");
      setEmail("");
      setContactName("");
      if (clearEditingContact) clearEditingContact();
      setOpenEmailDialog(false);

      if (response) {
        toast.success(
          editingContact ? "Email contact updated" : "Email contact added",
          {
            position: "top-center",
            autoClose: 1000,
          }
        );
        if (fetchProfile) fetchProfile();
      } else {
        toast.error("Error while saving email contact", {
          position: "top-center",
          autoClose: 1000,
        });
      }
    } catch (error) {
      console.error("Error saving email contact:", error);
      toast.error("Error while saving email contact", {
        position: "top-center",
        autoClose: 1000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog
      open={openEmailDialog}
      onClose={() => {
        setEmail("");
        setContactName("");
        if (clearEditingContact) clearEditingContact();
        setOpenEmailDialog(false);
      }}
    >
      <DialogTitle>
        {editingContact ? "Edit Email Contact" : "Add Email Contact"}
      </DialogTitle>
      <DialogContent>
        <TextField
          name="contactName"
          autoFocus
          margin="dense"
          label="Contact Name"
          type="text"
          fullWidth
          required
          value={contactName}
          onChange={handleContactNameChange}
          error={nameValidationError !== ""}
          helperText={contactName === "" ? "Contact name is required" : ""}
          sx={{ mb: 2 }}
        />
        <TextField
          name="Email"
          margin="dense"
          label="Email Address"
          type="email"
          fullWidth
          required
          value={email}
          onChange={handleEmailChange}
          helperText={email === "" ? "Email address is required" : ""}
        />
        {/* Hints and Tips Section */}
        <Box
          mt={2}
          p={2}
          sx={{
            backgroundColor: "#f0f0f0",
            borderRadius: "5px",
          }}
        >
          <Typography variant="subtitle1" color="textPrimary">
            Tips for Adding Email Contact
          </Typography>
          <Typography variant="body2" color="textSecondary">
            - Give your contact a descriptive name (e.g., "Work Email",
            "Personal Email")
          </Typography>
          <Typography variant="body2" color="textSecondary">
            - Enter your complete email address (e.g., <EMAIL>)
          </Typography>
          <Typography variant="body2" color="textSecondary">
            - All email providers are supported (Gmail, Yahoo, Outlook, etc.)
          </Typography>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button
          onClick={() => {
            setEmail("");
            setContactName("");
            if (clearEditingContact) clearEditingContact();
            setOpenEmailDialog(false);
          }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleConfirm}
          variant="contained"
          disabled={isLoading}
        >
          {isLoading ? "Saving..." : editingContact ? "Update" : "Add"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default EmailLinkDialog;
