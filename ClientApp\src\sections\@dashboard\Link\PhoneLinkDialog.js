import { useState, useEffect } from "react";
import {
  TextField,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Box,
  Typography,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from "@mui/material";

import { CreateContact, EditContact } from "../../../ContactData.ts";
import { useProfile } from "../../../Context/ProfileContext";

import { toast } from "react-toastify";
import Chip from "@mui/material/Chip";

// Country codes data (same as WhatsApp)
const countryCodes = [
  {
    code: "+1",
    country: "United States",
    flag: "https://flagcdn.com/w20/us.png",
  },
  { code: "+1", country: "Canada", flag: "https://flagcdn.com/w20/ca.png" },
  {
    code: "+44",
    country: "United Kingdom",
    flag: "https://flagcdn.com/w20/gb.png",
  },
  { code: "+49", country: "Germany", flag: "https://flagcdn.com/w20/de.png" },
  { code: "+33", country: "France", flag: "https://flagcdn.com/w20/fr.png" },
  { code: "+39", country: "Italy", flag: "https://flagcdn.com/w20/it.png" },
  { code: "+34", country: "Spain", flag: "https://flagcdn.com/w20/es.png" },
  {
    code: "+31",
    country: "Netherlands",
    flag: "https://flagcdn.com/w20/nl.png",
  },
  {
    code: "+41",
    country: "Switzerland",
    flag: "https://flagcdn.com/w20/ch.png",
  },
  { code: "+43", country: "Austria", flag: "https://flagcdn.com/w20/at.png" },
  { code: "+32", country: "Belgium", flag: "https://flagcdn.com/w20/be.png" },
  { code: "+45", country: "Denmark", flag: "https://flagcdn.com/w20/dk.png" },
  { code: "+46", country: "Sweden", flag: "https://flagcdn.com/w20/se.png" },
  { code: "+47", country: "Norway", flag: "https://flagcdn.com/w20/no.png" },
  { code: "+358", country: "Finland", flag: "https://flagcdn.com/w20/fi.png" },
  { code: "+91", country: "India", flag: "https://flagcdn.com/w20/in.png" },
  { code: "+86", country: "China", flag: "https://flagcdn.com/w20/cn.png" },
  { code: "+81", country: "Japan", flag: "https://flagcdn.com/w20/jp.png" },
  {
    code: "+82",
    country: "South Korea",
    flag: "https://flagcdn.com/w20/kr.png",
  },
  { code: "+65", country: "Singapore", flag: "https://flagcdn.com/w20/sg.png" },
  { code: "+60", country: "Malaysia", flag: "https://flagcdn.com/w20/my.png" },
  { code: "+66", country: "Thailand", flag: "https://flagcdn.com/w20/th.png" },
  { code: "+84", country: "Vietnam", flag: "https://flagcdn.com/w20/vn.png" },
  {
    code: "+63",
    country: "Philippines",
    flag: "https://flagcdn.com/w20/ph.png",
  },
  { code: "+62", country: "Indonesia", flag: "https://flagcdn.com/w20/id.png" },
  { code: "+61", country: "Australia", flag: "https://flagcdn.com/w20/au.png" },
  {
    code: "+64",
    country: "New Zealand",
    flag: "https://flagcdn.com/w20/nz.png",
  },
  {
    code: "+27",
    country: "South Africa",
    flag: "https://flagcdn.com/w20/za.png",
  },
  { code: "+20", country: "Egypt", flag: "https://flagcdn.com/w20/eg.png" },
  { code: "+216", country: "Tunisia", flag: "https://flagcdn.com/w20/tn.png" },
  { code: "+234", country: "Nigeria", flag: "https://flagcdn.com/w20/ng.png" },
  { code: "+254", country: "Kenya", flag: "https://flagcdn.com/w20/ke.png" },
  { code: "+971", country: "UAE", flag: "https://flagcdn.com/w20/ae.png" },
  {
    code: "+966",
    country: "Saudi Arabia",
    flag: "https://flagcdn.com/w20/sa.png",
  },
  { code: "+974", country: "Qatar", flag: "https://flagcdn.com/w20/qa.png" },
  { code: "+965", country: "Kuwait", flag: "https://flagcdn.com/w20/kw.png" },
  { code: "+973", country: "Bahrain", flag: "https://flagcdn.com/w20/bh.png" },
  { code: "+968", country: "Oman", flag: "https://flagcdn.com/w20/om.png" },
  { code: "+972", country: "Israel", flag: "https://flagcdn.com/w20/il.png" },
  { code: "+90", country: "Turkey", flag: "https://flagcdn.com/w20/tr.png" },
  { code: "+7", country: "Russia", flag: "https://flagcdn.com/w20/ru.png" },
  { code: "+380", country: "Ukraine", flag: "https://flagcdn.com/w20/ua.png" },
  { code: "+48", country: "Poland", flag: "https://flagcdn.com/w20/pl.png" },
  {
    code: "+420",
    country: "Czech Republic",
    flag: "https://flagcdn.com/w20/cz.png",
  },
  { code: "+36", country: "Hungary", flag: "https://flagcdn.com/w20/hu.png" },
  { code: "+40", country: "Romania", flag: "https://flagcdn.com/w20/ro.png" },
  { code: "+359", country: "Bulgaria", flag: "https://flagcdn.com/w20/bg.png" },
  { code: "+385", country: "Croatia", flag: "https://flagcdn.com/w20/hr.png" },
  { code: "+381", country: "Serbia", flag: "https://flagcdn.com/w20/rs.png" },
  { code: "+55", country: "Brazil", flag: "https://flagcdn.com/w20/br.png" },
  { code: "+52", country: "Mexico", flag: "https://flagcdn.com/w20/mx.png" },
  { code: "+54", country: "Argentina", flag: "https://flagcdn.com/w20/ar.png" },
  { code: "+56", country: "Chile", flag: "https://flagcdn.com/w20/cl.png" },
  { code: "+57", country: "Colombia", flag: "https://flagcdn.com/w20/co.png" },
  { code: "+51", country: "Peru", flag: "https://flagcdn.com/w20/pe.png" },
  { code: "+58", country: "Venezuela", flag: "https://flagcdn.com/w20/ve.png" },
  { code: "+593", country: "Ecuador", flag: "https://flagcdn.com/w20/ec.png" },
  { code: "+595", country: "Paraguay", flag: "https://flagcdn.com/w20/py.png" },
  { code: "+598", country: "Uruguay", flag: "https://flagcdn.com/w20/uy.png" },
];

const PhoneLinkDialog = ({
  setOpenPhoneDialog,
  openPhoneDialog,
  Id,
  editingContact = null,
  fetchProfile,
  clearEditingContact,
}) => {
  const [phoneNumber, setPhoneNumber] = useState("");
  const [contactName, setContactName] = useState("");
  const [selectedCountryCode, setSelectedCountryCode] = useState("+1");

  // Function to extract country code from a phone number
  const extractCountryCode = (phoneNumber) => {
    if (!phoneNumber || !phoneNumber.startsWith("+")) {
      return "+1"; // Default to US
    }

    // Sort country codes by length (longest first) to match correctly
    const sortedCodes = countryCodes
      .map((c) => c.code)
      .filter((code, index, arr) => arr.indexOf(code) === index) // Remove duplicates
      .sort((a, b) => b.length - a.length);

    for (const code of sortedCodes) {
      if (phoneNumber.startsWith(code)) {
        return code;
      }
    }

    return "+1"; // Default fallback
  };

  // Function to extract the number without country code
  const extractNumberWithoutCode = (phoneNumber, countryCode) => {
    if (!phoneNumber || !phoneNumber.startsWith(countryCode)) {
      return "";
    }
    return phoneNumber.substring(countryCode.length).replace(/^\s+/, "");
  };

  useEffect(() => {
    if (editingContact) {
      const fullNumber = editingContact.LinkUrl || "";
      const extractedCode = extractCountryCode(fullNumber);
      const numberWithoutCode = extractNumberWithoutCode(
        fullNumber,
        extractedCode
      );
      setSelectedCountryCode(extractedCode);
      setPhoneNumber(numberWithoutCode);
      setContactName(editingContact.Title || "");
    } else {
      setSelectedCountryCode("+1");
      setPhoneNumber("");
      setContactName("");
    }
  }, [editingContact, openPhoneDialog]);

  const handleContactNameChange = (event) => {
    setContactName(event.target.value);
  };

  const validatePhoneNumber = (countryCode, phoneNumber) => {
    if (!phoneNumber || phoneNumber.trim() === "") {
      return { isValid: false, error: "Phone number is required" };
    }

    if (!countryCode) {
      return { isValid: false, error: "Country code is required" };
    }

    // Remove all spaces, dashes, parentheses, and other formatting from phone number
    const cleanNumber = phoneNumber.replace(/[\s\-\(\)\.]/g, "");

    // Check if it contains only digits after cleaning
    if (!/^\d+$/.test(cleanNumber)) {
      return {
        isValid: false,
        error:
          "Phone number should contain only digits, spaces, dashes, or parentheses",
      };
    }

    // Country-specific validation patterns (same as WhatsApp)
    const countryValidation = {
      "+1": {
        // US/Canada
        minLength: 10,
        maxLength: 10,
        pattern: /^[2-9]\d{9}$/,
        errorMsg: "US/Canada numbers should be 10 digits starting with 2-9",
      },
      "+44": {
        // UK
        minLength: 10,
        maxLength: 11,
        pattern: /^[1-9]\d{9,10}$/,
        errorMsg: "UK numbers should be 10-11 digits",
      },
      "+49": {
        // Germany
        minLength: 10,
        maxLength: 12,
        pattern: /^[1-9]\d{9,11}$/,
        errorMsg: "German numbers should be 10-12 digits",
      },
      "+33": {
        // France
        minLength: 9,
        maxLength: 9,
        pattern: /^[1-9]\d{8}$/,
        errorMsg: "French numbers should be 9 digits starting with 1-9",
      },
      "+39": {
        // Italy
        minLength: 9,
        maxLength: 11,
        pattern: /^[0-9]\d{8,10}$/,
        errorMsg: "Italian numbers should be 9-11 digits",
      },
      "+34": {
        // Spain
        minLength: 9,
        maxLength: 9,
        pattern: /^[6-9]\d{8}$/,
        errorMsg: "Spanish mobile numbers should be 9 digits starting with 6-9",
      },
      "+91": {
        // India
        minLength: 10,
        maxLength: 10,
        pattern: /^[6-9]\d{9}$/,
        errorMsg: "Indian mobile numbers should be 10 digits starting with 6-9",
      },
      "+86": {
        // China
        minLength: 11,
        maxLength: 11,
        pattern: /^1[3-9]\d{9}$/,
        errorMsg:
          "Chinese mobile numbers should be 11 digits starting with 13-19",
      },
      "+81": {
        // Japan
        minLength: 10,
        maxLength: 11,
        pattern: /^[7-9]\d{9,10}$/,
        errorMsg:
          "Japanese mobile numbers should be 10-11 digits starting with 7-9",
      },
      "+82": {
        // South Korea
        minLength: 9,
        maxLength: 10,
        pattern: /^1[0-9]\d{7,8}$/,
        errorMsg:
          "Korean mobile numbers should be 9-10 digits starting with 10-19",
      },
      "+971": {
        // UAE
        minLength: 9,
        maxLength: 9,
        pattern: /^5\d{8}$/,
        errorMsg: "UAE mobile numbers should be 9 digits starting with 5",
      },
      "+966": {
        // Saudi Arabia
        minLength: 9,
        maxLength: 9,
        pattern: /^5\d{8}$/,
        errorMsg: "Saudi mobile numbers should be 9 digits starting with 5",
      },
      "+55": {
        // Brazil
        minLength: 10,
        maxLength: 11,
        pattern: /^[1-9]\d{9,10}$/,
        errorMsg: "Brazilian mobile numbers should be 10-11 digits",
      },
      "+52": {
        // Mexico
        minLength: 10,
        maxLength: 10,
        pattern: /^[1-9]\d{9}$/,
        errorMsg: "Mexican mobile numbers should be 10 digits",
      },
      "+216": {
        // Tunisia
        minLength: 8,
        maxLength: 8,
        pattern: /^[0-9]{8}$/,
        errorMsg: "Tunisian mobile numbers should be exactly 8 digits",
      },
    };

    const validation = countryValidation[countryCode];

    if (validation) {
      // Check length
      if (cleanNumber.length < validation.minLength) {
        return {
          isValid: false,
          error: `Number too short. ${validation.errorMsg}`,
        };
      }
      if (cleanNumber.length > validation.maxLength) {
        return {
          isValid: false,
          error: `Number too long. ${validation.errorMsg}`,
        };
      }

      // Check pattern
      if (!validation.pattern.test(cleanNumber)) {
        return { isValid: false, error: validation.errorMsg };
      }
    } else {
      // General validation for countries not specifically listed
      if (cleanNumber.length < 7) {
        return {
          isValid: false,
          error: "Phone number too short (minimum 7 digits)",
        };
      }
      if (cleanNumber.length > 15) {
        return {
          isValid: false,
          error: "Phone number too long (maximum 15 digits)",
        };
      }
    }

    return { isValid: true, error: null };
  };

  const isValidPhoneNumber = (countryCode, phoneNumber) => {
    const validation = validatePhoneNumber(countryCode, phoneNumber);
    return validation.isValid;
  };

  const getValidationError = (countryCode, phoneNumber) => {
    const validation = validatePhoneNumber(countryCode, phoneNumber);
    return validation.error;
  };

  const handleDone = async () => {
    // Validate using separated country code and phone number
    const validationError = getValidationError(
      selectedCountryCode,
      phoneNumber
    );
    if (validationError) {
      toast.error(validationError, {
        position: "top-center",
        autoClose: 3000,
      });
      return;
    }

    // Combine country code with number for storage
    const fullNumber = selectedCountryCode + phoneNumber.replace(/^\s+/, "");

    if (!contactName.trim()) {
      toast.error("Contact name is required", {
        position: "top-center",
        autoClose: 2000,
      });
      return;
    }

    let response;

    if (editingContact) {
      response = await EditContact({
        Id: editingContact.Id,
        ContactInfo: fullNumber,
        Category: "PhoneNumber",
        Title: contactName.trim(),
        isPublic: true,
      });
    } else {
      response = await CreateContact({
        UserId: Id,
        ContactInfo: fullNumber,
        Category: "PhoneNumber",
        Title: contactName.trim(),
        isPublic: true,
      });
    }

    localStorage.setItem("isLinksCardVisible", "true");

    setContactName("");
    setPhoneNumber("");
    setContactName("");
    if (clearEditingContact) clearEditingContact();
    setOpenPhoneDialog(false);

    if (response) {
      toast.success(
        editingContact ? "Phone contact updated" : "Phone contact added",
        {
          position: "top-center",
          autoClose: 1000,
        }
      );
      if (fetchProfile) fetchProfile();
    } else {
      toast.error("Error while saving phone contact", {
        position: "top-center",
        autoClose: 1000,
      });
    }
  };

  return (
    <Dialog
      open={openPhoneDialog}
      onClose={() => {
        setPhoneNumber("");
        setContactName("");
        if (clearEditingContact) clearEditingContact();
        setOpenPhoneDialog(false);
      }}
    >
      <DialogTitle>
        {editingContact ? "Edit Phone Contact" : "Add Phone Contact"}
      </DialogTitle>
      <DialogContent>
        <TextField
          name="contactName"
          autoFocus
          margin="dense"
          label="Contact Name"
          type="text"
          fullWidth
          required
          value={contactName}
          onChange={handleContactNameChange}
          helperText={contactName === "" ? "Contact name is required" : ""}
          sx={{ mb: 2 }}
        />
        <Grid container spacing={2}>
          <Grid item xs={12} sm={4}>
            <FormControl fullWidth margin="dense">
              <InputLabel>Country</InputLabel>
              <Select
                value={selectedCountryCode}
                onChange={(e) => setSelectedCountryCode(e.target.value)}
                label="Country"
                sx={{
                  "& .MuiSelect-select": {
                    padding: { xs: "12px 14px", sm: "16.5px 14px" },
                    fontSize: { xs: "0.875rem", sm: "1rem" },
                  },
                }}
                renderValue={(value) => {
                  const country = countryCodes.find((c) => c.code === value);
                  return country ? (
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        gap: { xs: 0.5, sm: 1 },
                        minWidth: 0,
                      }}
                    >
                      <img
                        src={country.flag}
                        alt={country.country}
                        style={{
                          width: 20,
                          height: 15,
                          flexShrink: 0,
                        }}
                      />
                      <span
                        style={{
                          fontSize: "inherit",
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                        }}
                      >
                        {value}
                      </span>
                    </Box>
                  ) : (
                    value
                  );
                }}
              >
                {countryCodes.map((country, index) => (
                  <MenuItem
                    key={`${country.code}-${index}`}
                    value={country.code}
                    sx={{
                      padding: { xs: "8px 16px", sm: "6px 16px" },
                      minHeight: { xs: "48px", sm: "auto" },
                    }}
                  >
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        gap: 1,
                        width: "100%",
                        minWidth: 0,
                      }}
                    >
                      <img
                        src={country.flag}
                        alt={country.country}
                        style={{
                          width: 20,
                          height: 15,
                          flexShrink: 0,
                        }}
                      />
                      <span
                        style={{
                          fontWeight: 500,
                          flexShrink: 0,
                        }}
                      >
                        {country.code}
                      </span>
                      <span
                        style={{
                          fontSize: "0.875rem",
                          color: "#666",
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                          flex: 1,
                          minWidth: 0,
                        }}
                      >
                        {country.country}
                      </span>
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={8}>
            <TextField
              name="PhoneNumber"
              margin="dense"
              label="Phone Number"
              type="tel"
              fullWidth
              required
              value={phoneNumber}
              onChange={(e) =>
                setPhoneNumber(e.target.value.replace(/[^\d\s\-\(\)]/g, ""))
              }
              error={
                phoneNumber !== "" &&
                !isValidPhoneNumber(selectedCountryCode, phoneNumber)
              }
              helperText={
                phoneNumber === ""
                  ? "Phone number is required"
                  : phoneNumber !== "" &&
                    !isValidPhoneNumber(selectedCountryCode, phoneNumber)
                  ? getValidationError(selectedCountryCode, phoneNumber)
                  : "✓ Valid phone number"
              }
              placeholder="************"
              inputProps={{
                maxLength: 15,
              }}
              sx={{
                "& .MuiInputBase-input": {
                  fontSize: { xs: "16px", sm: "1rem" }, // Prevents zoom on iOS
                  padding: { xs: "12px 14px", sm: "16.5px 14px" },
                },
                "& .MuiFormHelperText-root": {
                  fontSize: { xs: "0.75rem", sm: "0.75rem" },
                  marginTop: { xs: "4px", sm: "3px" },
                },
              }}
            />
          </Grid>
        </Grid>

        {/* Preview of full number */}
        {phoneNumber && (
          <Box
            sx={{
              mt: { xs: 2, sm: 1 },
              mb: { xs: 2, sm: 1 },
              display: "flex",
              justifyContent: { xs: "center", sm: "flex-start" },
            }}
          >
            <Chip
              label={`Full Number: ${selectedCountryCode} ${phoneNumber}`}
              variant="outlined"
              size="small"
              color="primary"
              sx={{
                fontSize: { xs: "0.75rem", sm: "0.8125rem" },
                height: { xs: "28px", sm: "24px" },
                "& .MuiChip-label": {
                  padding: { xs: "0 8px", sm: "0 12px" },
                },
              }}
            />
          </Box>
        )}
        {/* Hints and Tips Section */}
        <Box
          mt={2}
          p={2}
          sx={{
            backgroundColor: "#f0f0f0",
            borderRadius: "5px",
          }}
        >
          <Typography variant="subtitle1" color="textPrimary">
            Tips for Adding Phone Contact
          </Typography>
          <Typography variant="body2" color="textSecondary">
            - Give your contact a descriptive name (e.g., "Work Phone",
            "Personal Phone")
          </Typography>
          <Typography variant="body2" color="textSecondary">
            - Enter the phone number without spaces, dashes, or symbols
          </Typography>
          <Typography variant="body2" color="textSecondary">
            - Include the country code if needed (e.g., +1 for the US)
          </Typography>
          <Typography variant="body2" color="textSecondary">
            - You can add multiple phone contacts with different names
          </Typography>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button
          onClick={() => {
            setContactName("");
            setPhoneNumber("");
            setContactName("");
            if (clearEditingContact) clearEditingContact();
            setOpenPhoneDialog(false);
          }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleDone}
          disabled={
            phoneNumber === "" ||
            !isValidPhoneNumber(selectedCountryCode, phoneNumber) ||
            contactName.trim() === ""
          }
        >
          {editingContact ? "Update" : "Add"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default PhoneLinkDialog;
