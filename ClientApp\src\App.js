import { <PERSON>rowser<PERSON>outer } from "react-router-dom";
import { He<PERSON>etProvider } from "react-helmet-async";
// routes
import "react-toastify/dist/ReactToastify.css";
import { ToastContainer } from "react-toastify";
import Router from "./routes";
// theme
import ThemeProvider from "./theme";
// components
import { StyledChart } from "./components/chart";
import ScrollToTop from "./components/scroll-to-top";
import { BudgetProvider } from "./Context/BudgetContext";
import { ProfileProvider } from "./Context/ProfileContext";
import { SearchProvider } from "./Context/SearchContext";

// ----------------------------------------------------------------------

export default function App() {
    return (
        <ProfileProvider>
            <BudgetProvider>
                <SearchProvider>
                    <HelmetProvider>
                        <BrowserRouter>
                            <ThemeProvider>
                                <ScrollToTop />
                                <StyledChart />
                                <Router />
                                <ToastContainer />
                            </ThemeProvider>
                        </BrowserRouter>
                    </HelmetProvider>
                </SearchProvider>
            </BudgetProvider>
        </ProfileProvider>
    );
}
