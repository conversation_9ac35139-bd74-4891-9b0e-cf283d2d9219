import { alpha, styled } from "@mui/material/styles";
import { Card, Typo<PERSON>, Button, Box } from "@mui/material";
import ClearOutlinedIcon from "@mui/icons-material/ClearOutlined";
import DoneOutlinedIcon from "@mui/icons-material/DoneOutlined";

// ----------------------------------------------------------------------

const StyledIcon = styled("div")(({ theme, bundleType }) => {
  const colors = {
    primary: "#ff715b",
    secondary: "#e65d47",
  };

  return {
    margin: "auto",
    display: "flex",
    borderRadius: "12px",
    alignItems: "center",
    width: theme.spacing(10),
    height: theme.spacing(10),
    justifyContent: "center",
    marginBottom: theme.spacing(3),
    color: colors.primary,
    background: `linear-gradient(135deg, ${colors.primary}20 0%, ${colors.secondary}10 100%)`,
    border: `2px solid ${colors.primary}30`,
    boxShadow: `0 8px 32px ${colors.primary}20`,
    transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
    "&:hover": {
      transform: "translateY(-4px) scale(1.05)",
      boxShadow: `0 12px 40px ${colors.primary}30`,
    },
  };
});

// ----------------------------------------------------------------------

export default function AppBundleWidget({
  title,
  icon,
  data,
  amount,
  reference,
  article,
  setOpenDialog,
  setReference,
  setAmount,
  currentUserCategory,
}) {
  const isFreeBundle = title === "Free";
  const isCurrentBundle = currentUserCategory === title;

  const getColors = () => {
    switch (title) {
      case "Student":
        return {
          primary: "#ff715b",
          secondary: "#e65d47",
        };
      case "Freelance":
        return {
          primary: "#ff715b",
          secondary: "#e65d47",
        };
      case "Enterprise":
        return {
          primary: "#ff715b",
          secondary: "#e65d47",
        };
      default:
        return {
          primary: "#ff715b",
          secondary: "#e65d47",
        };
    }
  };

  const colors = getColors();

  const handleOpen = (reference, amount) => {
    setOpenDialog(true);
    setReference(reference);
    setAmount(amount);
  };

  return (
    <>
      <Card
        sx={{
          py: 5,
          px: 3,
          textAlign: "center",
          color: colors.primary,
          background: isCurrentBundle
            ? `linear-gradient(135deg, ${colors.primary}15 0%, ${colors.secondary}08 100%)`
            : `linear-gradient(135deg, ${colors.primary}05 0%, ${colors.secondary}02 100%)`,
          border: isCurrentBundle
            ? `3px solid ${colors.primary}60`
            : `2px solid ${colors.primary}20`,
          borderRadius: "6px",
          boxShadow: isCurrentBundle
            ? `0 12px 40px ${colors.primary}30, 0 6px 20px rgba(0,0,0,0.15)`
            : `0 8px 32px ${colors.primary}15, 0 4px 16px rgba(0,0,0,0.1)`,
          height: "46rem",
          position: "relative",
          overflow: "hidden",
          transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
          "&:hover": {
            transform: isCurrentBundle ? "none" : "translateY(-8px)",
            boxShadow: isCurrentBundle
              ? `0 12px 40px ${colors.primary}30, 0 6px 20px rgba(0,0,0,0.15)`
              : `0 16px 48px ${colors.primary}25, 0 8px 24px rgba(0,0,0,0.15)`,
            border: isCurrentBundle
              ? `3px solid ${colors.primary}60`
              : `2px solid ${colors.primary}40`,
          },
        }}
      >
        {/* article or current plan indicator */}
        {(article || isCurrentBundle) && (
          <div
            style={{
              background: isCurrentBundle
                ? `linear-gradient(135deg, #4caf50, #45a049)`
                : `linear-gradient(135deg, ${colors.primary}, ${colors.secondary})`,
              color: "white",
              position: "absolute",
              top: 0,
              right: 0,
              padding: "8px 24px",
              textAlign: "center",
              borderRadius: "0 6px 0 6px",
              fontSize: "12px",
              fontWeight: "600",
              boxShadow: isCurrentBundle
                ? `0 4px 12px rgba(76, 175, 80, 0.4)`
                : `0 4px 12px ${colors.primary}40`,
            }}
          >
            {isCurrentBundle ? "Current Plan" : article}
          </div>
        )}

        <StyledIcon bundleType={title}>{icon}</StyledIcon>

        <Typography
          sx={{
            textAlign: "center",
          }}
          variant="h4"
        >
          {title}
        </Typography>

        <Typography
          sx={{
            padding: "10px",
            fontWeight: "500",
            marginBottom: "20px",
            color: isFreeBundle ? "rgba(20, 43, 58, 0.5)" : undefined, // Set color based on condition
          }}
          variant="h5"
        >
          {amount}
          <sup>dt</sup>
          <Typography
            variant="subtitle1"
            component="span"
            fontSize={16}
            color="rgba(20, 43, 58, 0.5)"
          >
            /Month
          </Typography>
        </Typography>

        {data.map((x, index) => {
          const trimmedX = x.trim();
          if (trimmedX === "") return null;

          const isInclude = trimmedX.toLowerCase().startsWith("include");
          const iconColor = isInclude ? "#78b627" : "#c42f29";
          const textDecoration = isInclude ? "none" : "line-through";
          const text = isInclude
            ? trimmedX.substring("include".length).trim()
            : trimmedX;

          const IconComponent = isInclude
            ? DoneOutlinedIcon
            : ClearOutlinedIcon;

          return (
            <Box
              sx={{
                display: "flex",
                justifyContent: "start",
                padding: "12px",
                alignItems: "center",
                marginLeft: "30px", // Align items vertically
              }}
              key={index}
            >
              <IconComponent
                sx={{
                  borderRadius: "50%",
                  fontSize: 14,
                  color: iconColor,
                  background: `linear-gradient(135deg, ${alpha(
                    iconColor,
                    0
                  )} 0%, ${alpha(iconColor, 0.24)} 100%)`,
                  height: 20,
                  width: 20,
                  padding: "3px",
                }}
              />
              <Typography
                fontSize={14}
                color="text.secondary"
                fontWeight={500}
                sx={{
                  px: "20px",
                  color: isInclude ? "text.secondary" : "rgba(20, 43, 58, 0.5)",
                  textDecoration: textDecoration,
                }}
              >
                {text}
              </Typography>
            </Box>
          );
        })}

        {!isFreeBundle && (
          <Button
            onClick={() => {
              if (!isCurrentBundle) {
                handleOpen(reference, amount);
              }
            }}
            variant="contained"
            size="large"
            disabled={isCurrentBundle}
            sx={{
              marginTop: "35px",
              background: isCurrentBundle
                ? "rgba(0, 0, 0, 0.12)"
                : `linear-gradient(135deg, ${colors.primary}, ${colors.secondary})`,
              color: isCurrentBundle ? "rgba(0, 0, 0, 0.26)" : "white",
              borderRadius: "6px",
              padding: "12px 32px",
              fontSize: "16px",
              fontWeight: "600",
              textTransform: "none",
              boxShadow: isCurrentBundle
                ? "none"
                : `0 8px 24px ${colors.primary}40`,
              transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
              "&:hover": {
                background: isCurrentBundle
                  ? "rgba(0, 0, 0, 0.12)"
                  : `linear-gradient(135deg, ${colors.secondary}, ${colors.primary})`,
                transform: isCurrentBundle ? "none" : "translateY(-2px)",
                boxShadow: isCurrentBundle
                  ? "none"
                  : `0 12px 32px ${colors.primary}50`,
              },
              "&.Mui-disabled": {
                background: "rgba(0, 0, 0, 0.12)",
                color: "rgba(0, 0, 0, 0.26)",
              },
            }}
          >
            {isCurrentBundle ? "Current Plan" : "Purchase this bundle"}
          </Button>
        )}
      </Card>
    </>
  );
}
