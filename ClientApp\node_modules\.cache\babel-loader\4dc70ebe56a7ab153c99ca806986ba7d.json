{"ast": null, "code": "import api from\"./Api\";import{BASE_URL}from\"./Context/config\";import Cookies from\"js-cookie\";const handleRandomPhotoSelect=async()=>{const randomAvatarNumber=Math.floor(Math.random()*24)+1;const randomAvatarUrl=`/assets/images/avatars/avatar_${randomAvatarNumber}.jpg`;const response=await fetch(randomAvatarUrl);const blob=await response.blob();return new Promise(resolve=>{const reader=new FileReader();reader.onloadend=()=>{resolve(reader.result);};reader.readAsDataURL(blob);});};export const Register=async user=>{const profilePicture=user.profilePicture||(await handleRandomPhotoSelect());const data={Email:user.email,FirstName:user.firstName,LastName:user.lastName,UserName:user.userName,Password:user.password,Gender:user.gender,Category:user.categoryUser,ContactInfo:user.contactInfo,ContactCategory:user.contactCategory,ProfilePicture:profilePicture};try{const response=await api.post(`${BASE_URL}/Auth/Register`,data,{headers:{\"Content-Type\":\"application/json\"}});localStorage.setItem(\"isEmailSent\",\"true\");return response;}catch(error){return{error:error.response.data};}};export const verifyEmail=async token=>{try{const response=await api.get(`${BASE_URL}/Auth/VerifyEmail/${token}`);return response;}catch(error){console.error(\"Error while verifying email:\",error);return{error:\"An error occurred while verifying the email. Please try again later.\"};}};export const resendVerificationEmail=async email=>{try{const response=await api.post(`${BASE_URL}/Auth/ResendVerificationEmail`,JSON.stringify(email),{headers:{\"Content-Type\":\"application/json\"}});return response;}catch(error){console.error(\"Error while resending verification email:\",error);if(error.response&&error.response.data&&error.response.data.error){return{error:error.response.data.error};}return{error:\"An error occurred while resending verification email. Please try again later.\"};}};export const ForgotPassword=async email=>{try{const response=await api.post(`${BASE_URL}/Auth/ForgotPassword`,email,{headers:{\"Content-Type\":\"application/json\"}});return response;}catch(error){if(error.response){return error.response;}return{error:\"An error occurred while forgot password. Please try again later.\"};}};export const ChangePassword=async password=>{try{const response=await api.post(`${BASE_URL}/Auth/ChangePassword`,password,{headers:{\"Content-Type\":\"application/json\"}});return response;}catch(error){console.error(\"Error while changing password:\",error);return{error:\"An error occurred while changing password. Please try again later.\"};}};export const VerifyPasswordChanging=async(confirmPassword,token)=>{try{const response=await api.post(`${BASE_URL}/Auth/VerifyPasswordChanging/${token}`,confirmPassword,{headers:{\"Content-Type\":\"application/json\"}});return response;}catch(error){console.error(\"Error while verifying changing password:\",error);if(error.response){return error.response;}return{status:500,data:{error:\"An error occurred while verifying changing password. Please try again later.\"}};}};export const Login=async user=>{const data={Email:user.email,Password:user.password};try{const response=await api.post(`/Auth/Login`,data,{headers:{\"Content-Type\":\"application/json\"}});const expiresIn=new Date(Date.parse(response.data.expiresIn));expiresIn.setHours(expiresIn.getHours()+1);setAuthTokens(response.data.accessToken,response.data.refreshToken,expiresIn);const expires=new Date(expiresIn);Cookies.set(\"authToken\",response.data.accessToken,{expires:expires,path:\"/\",...(process.env.REACT_APP_ENV===\"production\"&&{domain:\"idigics.com\"})});Cookies.set(\"refreshToken\",response.data.refreshToken,{expires:expires,path:\"/\",...(process.env.REACT_APP_ENV===\"production\"&&{domain:\"idigics.com\"})});return response;}catch(error){if(error.response&&error.response.data&&error.response.data.error){return{error:error.response.data.error};}return null;// Return null for other errors\n}};export const Logout=async()=>{const cookieOptions={path:\"/\",...(process.env.REACT_APP_ENV===\"production\"&&{domain:\"idigics.com\"})};Cookies.remove(\"authToken\",cookieOptions);Cookies.remove(\"refreshToken\",cookieOptions);};export const RefreshToken=async()=>{const data={refreshToken:getRefreshToken()};try{const response=await api.post(`${BASE_URL}/Auth/RefreshToken`,data,{headers:{\"Content-Type\":\"application/json\"}});setAuthTokens(response.data.accessToken,response.data.refreshToken,response.data.expiresIn);return response;}catch(error){return null;}};export function setAuthTokens(accessToken,refreshToken,expiresIn){const expires=new Date(expiresIn);const domain=process.env.REACT_APP_ENV===\"production\"?\"idigics.com\":\"\";// Set cookies with or without domain based on the environment\ndocument.cookie=`authToken=${accessToken}; expires=${expires.toUTCString()}; path=/; domain=${domain}`;document.cookie=`refreshToken=${refreshToken}; expires=${expires.toUTCString()}; path=/; domain=${domain}`;}// export function setAuthToken(token: string) {\n//     const expires = new Date();\n//     expires.setDate(expires.getDate() + 3);\n//     document.cookie = `authToken=${token}; expires=${expires.toUTCString()}; path=/`;\n// }\nexport function getRefreshToken(){const cookies=document.cookie.split(\";\");for(let i=0;i<cookies.length;i++){const cookie=cookies[i].trim();if(cookie.startsWith(\"refreshToken=\")){const refreshToken=cookie.substring(\"refreshToken=\".length,cookie.length);if(refreshToken.length>0){return refreshToken;}else{console.error(\"Refresh token found, but it's empty or malformed.\");return null;}}}return null;}export function checkAuthToken(){const cookies=document.cookie.split(\";\");for(let i=0;i<cookies.length;i++){const cookie=cookies[i].trim();if(cookie.startsWith(\"authToken=\")){return true;}}return false;}", "map": {"version": 3, "names": ["api", "BASE_URL", "Cookies", "handleRandomPhotoSelect", "randomAvatarNumber", "Math", "floor", "random", "randomAvatarUrl", "response", "fetch", "blob", "Promise", "resolve", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "Register", "user", "profilePicture", "data", "Email", "email", "FirstName", "firstName", "LastName", "lastName", "UserName", "userName", "Password", "password", "Gender", "gender", "Category", "categoryUser", "ContactInfo", "contactInfo", "ContactCategory", "contactCategory", "ProfilePicture", "post", "headers", "localStorage", "setItem", "error", "verifyEmail", "token", "get", "console", "resendVerificationEmail", "JSON", "stringify", "ForgotPassword", "ChangePassword", "VerifyPasswordChanging", "confirmPassword", "status", "<PERSON><PERSON>", "expiresIn", "Date", "parse", "setHours", "getHours", "setAuthTokens", "accessToken", "refreshToken", "expires", "set", "path", "process", "env", "REACT_APP_ENV", "domain", "Logout", "cookieOptions", "remove", "RefreshToken", "getRefreshToken", "document", "cookie", "toUTCString", "cookies", "split", "i", "length", "trim", "startsWith", "substring", "checkAuthToken"], "sources": ["C:/Users/<USER>/Desktop/iDigitalX/ClientApp/src/AuthenticationData.ts"], "sourcesContent": ["import api from \"./Api\";\r\nimport { BASE_URL } from \"./Context/config\";\r\nimport Cookies from \"js-cookie\";\r\n\r\nexport interface PostUserData {\r\n  email: string;\r\n  firstName: string;\r\n  lastName: string;\r\n  userName: string;\r\n  password: string;\r\n  gender: string;\r\n  contactInfo: string;\r\n  contactCategory: string;\r\n  categoryUser: string;\r\n  profilePicture: string;\r\n  profileCoverPicture: string;\r\n}\r\n\r\nexport interface LogUserData {\r\n  email: string;\r\n  password: string;\r\n}\r\n\r\nconst handleRandomPhotoSelect = async () => {\r\n  const randomAvatarNumber = Math.floor(Math.random() * 24) + 1;\r\n  const randomAvatarUrl = `/assets/images/avatars/avatar_${randomAvatarNumber}.jpg`;\r\n\r\n  const response = await fetch(randomAvatarUrl);\r\n  const blob = await response.blob();\r\n\r\n  return new Promise((resolve) => {\r\n    const reader = new FileReader();\r\n    reader.onloadend = () => {\r\n      resolve(reader.result);\r\n    };\r\n    reader.readAsDataURL(blob);\r\n  });\r\n};\r\n\r\nexport const Register = async (user: PostUserData) => {\r\n  const profilePicture =\r\n    user.profilePicture || (await handleRandomPhotoSelect());\r\n  const data = {\r\n    Email: user.email,\r\n    FirstName: user.firstName,\r\n    LastName: user.lastName,\r\n    UserName: user.userName,\r\n    Password: user.password,\r\n    Gender: user.gender,\r\n    Category: user.categoryUser,\r\n    ContactInfo: user.contactInfo,\r\n    ContactCategory: user.contactCategory,\r\n    ProfilePicture: profilePicture,\r\n  };\r\n\r\n  try {\r\n    const response = await api.post(`${BASE_URL}/Auth/Register`, data, {\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    localStorage.setItem(\"isEmailSent\", \"true\");\r\n\r\n    return response;\r\n  } catch (error) {\r\n    return { error: error.response.data };\r\n  }\r\n};\r\n\r\nexport const verifyEmail = async (token: string) => {\r\n  try {\r\n    const response = await api.get(`${BASE_URL}/Auth/VerifyEmail/${token}`);\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Error while verifying email:\", error);\r\n    return {\r\n      error:\r\n        \"An error occurred while verifying the email. Please try again later.\",\r\n    };\r\n  }\r\n};\r\n\r\nexport const resendVerificationEmail = async (email: string) => {\r\n  try {\r\n    const response = await api.post(\r\n      `${BASE_URL}/Auth/ResendVerificationEmail`,\r\n      JSON.stringify(email),\r\n      {\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n      }\r\n    );\r\n    return response;\r\n  } catch (error: any) {\r\n    console.error(\"Error while resending verification email:\", error);\r\n    if (error.response && error.response.data && error.response.data.error) {\r\n      return { error: error.response.data.error };\r\n    }\r\n    return {\r\n      error:\r\n        \"An error occurred while resending verification email. Please try again later.\",\r\n    };\r\n  }\r\n};\r\n\r\nexport const ForgotPassword = async (email: string) => {\r\n  try {\r\n    const response = await api.post(`${BASE_URL}/Auth/ForgotPassword`, email, {\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n    return response;\r\n  } catch (error: any) {\r\n    if (error.response) {\r\n      return error.response;\r\n    }\r\n    return {\r\n      error: \"An error occurred while forgot password. Please try again later.\",\r\n    };\r\n  }\r\n};\r\n\r\nexport const ChangePassword = async (password: string) => {\r\n  try {\r\n    const response = await api.post(\r\n      `${BASE_URL}/Auth/ChangePassword`,\r\n      password,\r\n      {\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n      }\r\n    );\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Error while changing password:\", error);\r\n    return {\r\n      error:\r\n        \"An error occurred while changing password. Please try again later.\",\r\n    };\r\n  }\r\n};\r\n\r\nexport const VerifyPasswordChanging = async (\r\n  confirmPassword: string,\r\n  token: string\r\n) => {\r\n  try {\r\n    const response = await api.post(\r\n      `${BASE_URL}/Auth/VerifyPasswordChanging/${token}`,\r\n      confirmPassword,\r\n      {\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n      }\r\n    );\r\n    return response;\r\n  } catch (error: any) {\r\n    console.error(\"Error while verifying changing password:\", error);\r\n    if (error.response) {\r\n      return error.response;\r\n    }\r\n    return {\r\n      status: 500,\r\n      data: {\r\n        error:\r\n          \"An error occurred while verifying changing password. Please try again later.\",\r\n      },\r\n    };\r\n  }\r\n};\r\n\r\nexport const Login = async (user) => {\r\n  const data = {\r\n    Email: user.email,\r\n    Password: user.password,\r\n  };\r\n\r\n  try {\r\n    const response = await api.post(`/Auth/Login`, data, {\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    const expiresIn = new Date(Date.parse(response.data.expiresIn));\r\n    expiresIn.setHours(expiresIn.getHours() + 1);\r\n\r\n    setAuthTokens(\r\n      response.data.accessToken,\r\n      response.data.refreshToken,\r\n      expiresIn\r\n    );\r\n\r\n    const expires = new Date(expiresIn);\r\n\r\n    Cookies.set(\"authToken\", response.data.accessToken, {\r\n      expires: expires,\r\n      path: \"/\",\r\n      ...(process.env.REACT_APP_ENV === \"production\" && {\r\n        domain: \"idigics.com\",\r\n      }),\r\n    });\r\n\r\n    Cookies.set(\"refreshToken\", response.data.refreshToken, {\r\n      expires: expires,\r\n      path: \"/\",\r\n      ...(process.env.REACT_APP_ENV === \"production\" && {\r\n        domain: \"idigics.com\",\r\n      }),\r\n    });\r\n\r\n    return response;\r\n  } catch (error) {\r\n    if (error.response && error.response.data && error.response.data.error) {\r\n      return { error: error.response.data.error };\r\n    }\r\n    return null; // Return null for other errors\r\n  }\r\n};\r\n\r\nexport const Logout = async () => {\r\n  const cookieOptions = {\r\n    path: \"/\",\r\n    ...(process.env.REACT_APP_ENV === \"production\" && {\r\n      domain: \"idigics.com\",\r\n    }),\r\n  };\r\n\r\n  Cookies.remove(\"authToken\", cookieOptions);\r\n  Cookies.remove(\"refreshToken\", cookieOptions);\r\n};\r\n\r\nexport const RefreshToken = async () => {\r\n  const data = {\r\n    refreshToken: getRefreshToken(),\r\n  };\r\n\r\n  try {\r\n    const response = await api.post(`${BASE_URL}/Auth/RefreshToken`, data, {\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    setAuthTokens(\r\n      response.data.accessToken,\r\n      response.data.refreshToken,\r\n      response.data.expiresIn\r\n    );\r\n\r\n    return response;\r\n  } catch (error) {\r\n    return null;\r\n  }\r\n};\r\n\r\nexport function setAuthTokens(accessToken, refreshToken, expiresIn) {\r\n  const expires = new Date(expiresIn);\r\n\r\n  const domain =\r\n    process.env.REACT_APP_ENV === \"production\" ? \"idigics.com\" : \"\";\r\n\r\n  // Set cookies with or without domain based on the environment\r\n  document.cookie = `authToken=${accessToken}; expires=${expires.toUTCString()}; path=/; domain=${domain}`;\r\n  document.cookie = `refreshToken=${refreshToken}; expires=${expires.toUTCString()}; path=/; domain=${domain}`;\r\n}\r\n\r\n// export function setAuthToken(token: string) {\r\n//     const expires = new Date();\r\n//     expires.setDate(expires.getDate() + 3);\r\n//     document.cookie = `authToken=${token}; expires=${expires.toUTCString()}; path=/`;\r\n// }\r\n\r\nexport function getRefreshToken() {\r\n  const cookies = document.cookie.split(\";\");\r\n\r\n  for (let i = 0; i < cookies.length; i++) {\r\n    const cookie = cookies[i].trim();\r\n    if (cookie.startsWith(\"refreshToken=\")) {\r\n      const refreshToken = cookie.substring(\r\n        \"refreshToken=\".length,\r\n        cookie.length\r\n      );\r\n      if (refreshToken.length > 0) {\r\n        return refreshToken;\r\n      } else {\r\n        console.error(\"Refresh token found, but it's empty or malformed.\");\r\n        return null;\r\n      }\r\n    }\r\n  }\r\n\r\n  return null;\r\n}\r\n\r\nexport function checkAuthToken() {\r\n  const cookies = document.cookie.split(\";\");\r\n\r\n  for (let i = 0; i < cookies.length; i++) {\r\n    const cookie = cookies[i].trim();\r\n    if (cookie.startsWith(\"authToken=\")) {\r\n      return true;\r\n    }\r\n  }\r\n\r\n  return false;\r\n}\r\n"], "mappings": "AAAA,MAAO,CAAAA,GAAG,KAAM,OAAO,CACvB,OAASC,QAAQ,KAAQ,kBAAkB,CAC3C,MAAO,CAAAC,OAAO,KAAM,WAAW,CAqB/B,KAAM,CAAAC,uBAAuB,CAAG,KAAAA,CAAA,GAAY,CAC1C,KAAM,CAAAC,kBAAkB,CAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,CAAG,EAAE,CAAC,CAAG,CAAC,CAC7D,KAAM,CAAAC,eAAe,CAAG,iCAAiCJ,kBAAkB,MAAM,CAEjF,KAAM,CAAAK,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAACF,eAAe,CAAC,CAC7C,KAAM,CAAAG,IAAI,CAAG,KAAM,CAAAF,QAAQ,CAACE,IAAI,CAAC,CAAC,CAElC,MAAO,IAAI,CAAAC,OAAO,CAAEC,OAAO,EAAK,CAC9B,KAAM,CAAAC,MAAM,CAAG,GAAI,CAAAC,UAAU,CAAC,CAAC,CAC/BD,MAAM,CAACE,SAAS,CAAG,IAAM,CACvBH,OAAO,CAACC,MAAM,CAACG,MAAM,CAAC,CACxB,CAAC,CACDH,MAAM,CAACI,aAAa,CAACP,IAAI,CAAC,CAC5B,CAAC,CAAC,CACJ,CAAC,CAED,MAAO,MAAM,CAAAQ,QAAQ,CAAG,KAAO,CAAAC,IAAkB,EAAK,CACpD,KAAM,CAAAC,cAAc,CAClBD,IAAI,CAACC,cAAc,GAAK,KAAM,CAAAlB,uBAAuB,CAAC,CAAC,CAAC,CAC1D,KAAM,CAAAmB,IAAI,CAAG,CACXC,KAAK,CAAEH,IAAI,CAACI,KAAK,CACjBC,SAAS,CAAEL,IAAI,CAACM,SAAS,CACzBC,QAAQ,CAAEP,IAAI,CAACQ,QAAQ,CACvBC,QAAQ,CAAET,IAAI,CAACU,QAAQ,CACvBC,QAAQ,CAAEX,IAAI,CAACY,QAAQ,CACvBC,MAAM,CAAEb,IAAI,CAACc,MAAM,CACnBC,QAAQ,CAAEf,IAAI,CAACgB,YAAY,CAC3BC,WAAW,CAAEjB,IAAI,CAACkB,WAAW,CAC7BC,eAAe,CAAEnB,IAAI,CAACoB,eAAe,CACrCC,cAAc,CAAEpB,cAClB,CAAC,CAED,GAAI,CACF,KAAM,CAAAZ,QAAQ,CAAG,KAAM,CAAAT,GAAG,CAAC0C,IAAI,CAAC,GAAGzC,QAAQ,gBAAgB,CAAEqB,IAAI,CAAE,CACjEqB,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CACF,CAAC,CAAC,CAEFC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAE,MAAM,CAAC,CAE3C,MAAO,CAAApC,QAAQ,CACjB,CAAE,MAAOqC,KAAK,CAAE,CACd,MAAO,CAAEA,KAAK,CAAEA,KAAK,CAACrC,QAAQ,CAACa,IAAK,CAAC,CACvC,CACF,CAAC,CAED,MAAO,MAAM,CAAAyB,WAAW,CAAG,KAAO,CAAAC,KAAa,EAAK,CAClD,GAAI,CACF,KAAM,CAAAvC,QAAQ,CAAG,KAAM,CAAAT,GAAG,CAACiD,GAAG,CAAC,GAAGhD,QAAQ,qBAAqB+C,KAAK,EAAE,CAAC,CACvE,MAAO,CAAAvC,QAAQ,CACjB,CAAE,MAAOqC,KAAK,CAAE,CACdI,OAAO,CAACJ,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACpD,MAAO,CACLA,KAAK,CACH,sEACJ,CAAC,CACH,CACF,CAAC,CAED,MAAO,MAAM,CAAAK,uBAAuB,CAAG,KAAO,CAAA3B,KAAa,EAAK,CAC9D,GAAI,CACF,KAAM,CAAAf,QAAQ,CAAG,KAAM,CAAAT,GAAG,CAAC0C,IAAI,CAC7B,GAAGzC,QAAQ,+BAA+B,CAC1CmD,IAAI,CAACC,SAAS,CAAC7B,KAAK,CAAC,CACrB,CACEmB,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CACF,CACF,CAAC,CACD,MAAO,CAAAlC,QAAQ,CACjB,CAAE,MAAOqC,KAAU,CAAE,CACnBI,OAAO,CAACJ,KAAK,CAAC,2CAA2C,CAAEA,KAAK,CAAC,CACjE,GAAIA,KAAK,CAACrC,QAAQ,EAAIqC,KAAK,CAACrC,QAAQ,CAACa,IAAI,EAAIwB,KAAK,CAACrC,QAAQ,CAACa,IAAI,CAACwB,KAAK,CAAE,CACtE,MAAO,CAAEA,KAAK,CAAEA,KAAK,CAACrC,QAAQ,CAACa,IAAI,CAACwB,KAAM,CAAC,CAC7C,CACA,MAAO,CACLA,KAAK,CACH,+EACJ,CAAC,CACH,CACF,CAAC,CAED,MAAO,MAAM,CAAAQ,cAAc,CAAG,KAAO,CAAA9B,KAAa,EAAK,CACrD,GAAI,CACF,KAAM,CAAAf,QAAQ,CAAG,KAAM,CAAAT,GAAG,CAAC0C,IAAI,CAAC,GAAGzC,QAAQ,sBAAsB,CAAEuB,KAAK,CAAE,CACxEmB,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CACF,CAAC,CAAC,CACF,MAAO,CAAAlC,QAAQ,CACjB,CAAE,MAAOqC,KAAU,CAAE,CACnB,GAAIA,KAAK,CAACrC,QAAQ,CAAE,CAClB,MAAO,CAAAqC,KAAK,CAACrC,QAAQ,CACvB,CACA,MAAO,CACLqC,KAAK,CAAE,kEACT,CAAC,CACH,CACF,CAAC,CAED,MAAO,MAAM,CAAAS,cAAc,CAAG,KAAO,CAAAvB,QAAgB,EAAK,CACxD,GAAI,CACF,KAAM,CAAAvB,QAAQ,CAAG,KAAM,CAAAT,GAAG,CAAC0C,IAAI,CAC7B,GAAGzC,QAAQ,sBAAsB,CACjC+B,QAAQ,CACR,CACEW,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CACF,CACF,CAAC,CACD,MAAO,CAAAlC,QAAQ,CACjB,CAAE,MAAOqC,KAAK,CAAE,CACdI,OAAO,CAACJ,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CACtD,MAAO,CACLA,KAAK,CACH,oEACJ,CAAC,CACH,CACF,CAAC,CAED,MAAO,MAAM,CAAAU,sBAAsB,CAAG,KAAAA,CACpCC,eAAuB,CACvBT,KAAa,GACV,CACH,GAAI,CACF,KAAM,CAAAvC,QAAQ,CAAG,KAAM,CAAAT,GAAG,CAAC0C,IAAI,CAC7B,GAAGzC,QAAQ,gCAAgC+C,KAAK,EAAE,CAClDS,eAAe,CACf,CACEd,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CACF,CACF,CAAC,CACD,MAAO,CAAAlC,QAAQ,CACjB,CAAE,MAAOqC,KAAU,CAAE,CACnBI,OAAO,CAACJ,KAAK,CAAC,0CAA0C,CAAEA,KAAK,CAAC,CAChE,GAAIA,KAAK,CAACrC,QAAQ,CAAE,CAClB,MAAO,CAAAqC,KAAK,CAACrC,QAAQ,CACvB,CACA,MAAO,CACLiD,MAAM,CAAE,GAAG,CACXpC,IAAI,CAAE,CACJwB,KAAK,CACH,8EACJ,CACF,CAAC,CACH,CACF,CAAC,CAED,MAAO,MAAM,CAAAa,KAAK,CAAG,KAAO,CAAAvC,IAAI,EAAK,CACnC,KAAM,CAAAE,IAAI,CAAG,CACXC,KAAK,CAAEH,IAAI,CAACI,KAAK,CACjBO,QAAQ,CAAEX,IAAI,CAACY,QACjB,CAAC,CAED,GAAI,CACF,KAAM,CAAAvB,QAAQ,CAAG,KAAM,CAAAT,GAAG,CAAC0C,IAAI,CAAC,aAAa,CAAEpB,IAAI,CAAE,CACnDqB,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CACF,CAAC,CAAC,CAEF,KAAM,CAAAiB,SAAS,CAAG,GAAI,CAAAC,IAAI,CAACA,IAAI,CAACC,KAAK,CAACrD,QAAQ,CAACa,IAAI,CAACsC,SAAS,CAAC,CAAC,CAC/DA,SAAS,CAACG,QAAQ,CAACH,SAAS,CAACI,QAAQ,CAAC,CAAC,CAAG,CAAC,CAAC,CAE5CC,aAAa,CACXxD,QAAQ,CAACa,IAAI,CAAC4C,WAAW,CACzBzD,QAAQ,CAACa,IAAI,CAAC6C,YAAY,CAC1BP,SACF,CAAC,CAED,KAAM,CAAAQ,OAAO,CAAG,GAAI,CAAAP,IAAI,CAACD,SAAS,CAAC,CAEnC1D,OAAO,CAACmE,GAAG,CAAC,WAAW,CAAE5D,QAAQ,CAACa,IAAI,CAAC4C,WAAW,CAAE,CAClDE,OAAO,CAAEA,OAAO,CAChBE,IAAI,CAAE,GAAG,CACT,IAAIC,OAAO,CAACC,GAAG,CAACC,aAAa,GAAK,YAAY,EAAI,CAChDC,MAAM,CAAE,aACV,CAAC,CACH,CAAC,CAAC,CAEFxE,OAAO,CAACmE,GAAG,CAAC,cAAc,CAAE5D,QAAQ,CAACa,IAAI,CAAC6C,YAAY,CAAE,CACtDC,OAAO,CAAEA,OAAO,CAChBE,IAAI,CAAE,GAAG,CACT,IAAIC,OAAO,CAACC,GAAG,CAACC,aAAa,GAAK,YAAY,EAAI,CAChDC,MAAM,CAAE,aACV,CAAC,CACH,CAAC,CAAC,CAEF,MAAO,CAAAjE,QAAQ,CACjB,CAAE,MAAOqC,KAAK,CAAE,CACd,GAAIA,KAAK,CAACrC,QAAQ,EAAIqC,KAAK,CAACrC,QAAQ,CAACa,IAAI,EAAIwB,KAAK,CAACrC,QAAQ,CAACa,IAAI,CAACwB,KAAK,CAAE,CACtE,MAAO,CAAEA,KAAK,CAAEA,KAAK,CAACrC,QAAQ,CAACa,IAAI,CAACwB,KAAM,CAAC,CAC7C,CACA,MAAO,KAAI,CAAE;AACf,CACF,CAAC,CAED,MAAO,MAAM,CAAA6B,MAAM,CAAG,KAAAA,CAAA,GAAY,CAChC,KAAM,CAAAC,aAAa,CAAG,CACpBN,IAAI,CAAE,GAAG,CACT,IAAIC,OAAO,CAACC,GAAG,CAACC,aAAa,GAAK,YAAY,EAAI,CAChDC,MAAM,CAAE,aACV,CAAC,CACH,CAAC,CAEDxE,OAAO,CAAC2E,MAAM,CAAC,WAAW,CAAED,aAAa,CAAC,CAC1C1E,OAAO,CAAC2E,MAAM,CAAC,cAAc,CAAED,aAAa,CAAC,CAC/C,CAAC,CAED,MAAO,MAAM,CAAAE,YAAY,CAAG,KAAAA,CAAA,GAAY,CACtC,KAAM,CAAAxD,IAAI,CAAG,CACX6C,YAAY,CAAEY,eAAe,CAAC,CAChC,CAAC,CAED,GAAI,CACF,KAAM,CAAAtE,QAAQ,CAAG,KAAM,CAAAT,GAAG,CAAC0C,IAAI,CAAC,GAAGzC,QAAQ,oBAAoB,CAAEqB,IAAI,CAAE,CACrEqB,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CACF,CAAC,CAAC,CAEFsB,aAAa,CACXxD,QAAQ,CAACa,IAAI,CAAC4C,WAAW,CACzBzD,QAAQ,CAACa,IAAI,CAAC6C,YAAY,CAC1B1D,QAAQ,CAACa,IAAI,CAACsC,SAChB,CAAC,CAED,MAAO,CAAAnD,QAAQ,CACjB,CAAE,MAAOqC,KAAK,CAAE,CACd,MAAO,KAAI,CACb,CACF,CAAC,CAED,MAAO,SAAS,CAAAmB,aAAaA,CAACC,WAAW,CAAEC,YAAY,CAAEP,SAAS,CAAE,CAClE,KAAM,CAAAQ,OAAO,CAAG,GAAI,CAAAP,IAAI,CAACD,SAAS,CAAC,CAEnC,KAAM,CAAAc,MAAM,CACVH,OAAO,CAACC,GAAG,CAACC,aAAa,GAAK,YAAY,CAAG,aAAa,CAAG,EAAE,CAEjE;AACAO,QAAQ,CAACC,MAAM,CAAG,aAAaf,WAAW,aAAaE,OAAO,CAACc,WAAW,CAAC,CAAC,oBAAoBR,MAAM,EAAE,CACxGM,QAAQ,CAACC,MAAM,CAAG,gBAAgBd,YAAY,aAAaC,OAAO,CAACc,WAAW,CAAC,CAAC,oBAAoBR,MAAM,EAAE,CAC9G,CAEA;AACA;AACA;AACA;AACA;AAEA,MAAO,SAAS,CAAAK,eAAeA,CAAA,CAAG,CAChC,KAAM,CAAAI,OAAO,CAAGH,QAAQ,CAACC,MAAM,CAACG,KAAK,CAAC,GAAG,CAAC,CAE1C,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGF,OAAO,CAACG,MAAM,CAAED,CAAC,EAAE,CAAE,CACvC,KAAM,CAAAJ,MAAM,CAAGE,OAAO,CAACE,CAAC,CAAC,CAACE,IAAI,CAAC,CAAC,CAChC,GAAIN,MAAM,CAACO,UAAU,CAAC,eAAe,CAAC,CAAE,CACtC,KAAM,CAAArB,YAAY,CAAGc,MAAM,CAACQ,SAAS,CACnC,eAAe,CAACH,MAAM,CACtBL,MAAM,CAACK,MACT,CAAC,CACD,GAAInB,YAAY,CAACmB,MAAM,CAAG,CAAC,CAAE,CAC3B,MAAO,CAAAnB,YAAY,CACrB,CAAC,IAAM,CACLjB,OAAO,CAACJ,KAAK,CAAC,mDAAmD,CAAC,CAClE,MAAO,KAAI,CACb,CACF,CACF,CAEA,MAAO,KAAI,CACb,CAEA,MAAO,SAAS,CAAA4C,cAAcA,CAAA,CAAG,CAC/B,KAAM,CAAAP,OAAO,CAAGH,QAAQ,CAACC,MAAM,CAACG,KAAK,CAAC,GAAG,CAAC,CAE1C,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGF,OAAO,CAACG,MAAM,CAAED,CAAC,EAAE,CAAE,CACvC,KAAM,CAAAJ,MAAM,CAAGE,OAAO,CAACE,CAAC,CAAC,CAACE,IAAI,CAAC,CAAC,CAChC,GAAIN,MAAM,CAACO,UAAU,CAAC,YAAY,CAAC,CAAE,CACnC,MAAO,KAAI,CACb,CACF,CAEA,MAAO,MAAK,CACd", "ignoreList": []}, "metadata": {}, "sourceType": "module"}