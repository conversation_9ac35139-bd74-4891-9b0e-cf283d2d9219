{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\iDigitalX\\\\ClientApp\\\\src\\\\pages\\\\SearchResults.js\",\n  _s = $RefreshSig$();\nimport { useEffect, useState } from \"react\";\nimport { <PERSON>rid, <PERSON>ton, Card, Box, Typography, Tabs, Tab, Container, Rating, Stack } from \"@mui/material\";\nimport LocationOnIcon from \"@mui/icons-material/LocationOn\";\nimport WorkOutlineIcon from \"@mui/icons-material/WorkOutline\";\nimport StarBorderIcon from \"@mui/icons-material/StarBorder\";\nimport Avatar from \"@mui/material/Avatar\";\nimport { Link } from \"react-router-dom\";\nimport { useSearch } from \"../Context/SearchContext\";\nimport SearchNotFound from \"./SearchNotFound\";\nimport { motion } from \"framer-motion\";\nimport EmojiPeopleIcon from \"@mui/icons-material/EmojiPeople\";\nimport EngineeringIcon from \"@mui/icons-material/Engineering\";\nimport ApartmentIcon from \"@mui/icons-material/Apartment\";\nimport BusinessCenterIcon from \"@mui/icons-material/BusinessCenter\";\nimport { useSearchParams } from \"react-router-dom\";\nimport { CircularProgress } from \"@mui/material\";\n\n// Helper component for result card\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ResultCard = _ref => {\n  let {\n    result,\n    GetCategoryIcon\n  } = _ref;\n  return /*#__PURE__*/_jsxDEV(Grid, {\n    item: true,\n    xs: 12,\n    children: /*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"animate-on-scroll\",\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        duration: 0.5,\n        ease: \"easeOut\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Link, {\n        to: `/Profile/${result.userName}`,\n        target: \"_blank\",\n        rel: \"noreferrer\",\n        style: {\n          textDecoration: \"none\",\n          color: \"inherit\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            display: \"flex\",\n            flexDirection: \"row\",\n            alignItems: \"center\",\n            p: 3,\n            gap: \"10px\",\n            boxShadow: \"0px 4px 10px rgba(0, 0, 0, 0.1)\",\n            transition: \"box-shadow 0.3s ease\",\n            \"&:hover\": {\n              boxShadow: \"0px 8px 20px rgba(0, 0, 0, 0.2)\"\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            alignItems: \"center\",\n            spacing: 2,\n            sx: {\n              flex: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Avatar, {\n              alt: result.user.firstName + \" \" + result.user.lastName,\n              src: result.profilePicture,\n              sx: {\n                width: 80,\n                height: 80\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Stack, {\n              direction: \"column\",\n              spacing: 1,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                children: [result.user.firstName, \" \", result.user.lastName, \" \", /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  alignItems: \"baseline\",\n                  spacing: 1,\n                  sx: {\n                    display: \"inline-flex\",\n                    marginLeft: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      color: \"rgba(20, 43, 58, 0.5)\"\n                    },\n                    children: [\"@\", result.userName]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 90,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      color: \"rgba(20, 43, 58, 0.5)\"\n                    },\n                    children: GetCategoryIcon(result.user.category)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 98,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 81,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 15\n              }, this), result.user.rate > 0 && /*#__PURE__*/_jsxDEV(Rating, {\n                name: \"read-only\",\n                value: result.user.rate,\n                readOnly: true,\n                emptyIcon: /*#__PURE__*/_jsxDEV(StarBorderIcon, {\n                  fontSize: \"inherit\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 113,\n                  columnNumber: 30\n                }, this),\n                sx: {\n                  marginLeft: \"10px\",\n                  fontSize: \"13px\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: \"10px\"\n                },\n                children: result.country && /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(LocationOnIcon, {\n                    sx: {\n                      fontSize: \"15px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 129,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      color: \"rgba(20, 43, 58, 0.5)\"\n                    },\n                    children: result.country\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 134,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: \"10px\"\n                },\n                children: result.occupation && /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(WorkOutlineIcon, {\n                    sx: {\n                      fontSize: \"15px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 154,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      color: \"rgba(20, 43, 58, 0.5)\"\n                    },\n                    children: result.occupation\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 159,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 5\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 3\n  }, this);\n};\n_c = ResultCard;\nconst SearchResults = () => {\n  _s();\n  const {\n    searchResults,\n    fetchSearchResults,\n    loading\n  } = useSearch();\n  const [activeTab, setActiveTab] = useState(0); // Default to \"All\" tab\n  const [displayCount, setDisplayCount] = useState(5);\n  const [sortedResults, setSortedResults] = useState([]);\n  const [ratedResults, setRatedResults] = useState([]);\n  const [normalResults, setNormalResults] = useState([]);\n  const [searchParams] = useSearchParams();\n\n  // Fetch search results only when search parameters change\n  useEffect(() => {\n    const handleSearch = async () => {\n      if (searchParams && searchParams.toString() !== \"\") {\n        await fetchSearchResults(searchParams.get(\"q\"));\n      }\n    };\n    handleSearch(); // Call search function only when searchParams change\n  }, [searchParams]);\n\n  // Update sortedResults whenever searchResults or activeTab changes\n  useEffect(() => {\n    const {\n      sorted,\n      rated,\n      normal\n    } = sortResultsByTab(activeTab);\n    setSortedResults(sorted);\n    setRatedResults(rated);\n    setNormalResults(normal);\n  }, [searchResults, activeTab]);\n  const GetCategoryIcon = category => {\n    switch (category) {\n      case \"Free\":\n        return /*#__PURE__*/_jsxDEV(EmojiPeopleIcon, {\n          fontSize: \"Large\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 16\n        }, this);\n      case \"Student\":\n        return /*#__PURE__*/_jsxDEV(BusinessCenterIcon, {\n          fontSize: \"Large\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 16\n        }, this);\n      case \"Freelance\":\n        return /*#__PURE__*/_jsxDEV(EngineeringIcon, {\n          fontSize: \"Large\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 16\n        }, this);\n      case \"Entrepreneur\":\n        return /*#__PURE__*/_jsxDEV(ApartmentIcon, {\n          fontSize: \"Large\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 16\n        }, this);\n      default:\n        return null;\n    }\n  };\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n    setDisplayCount(5); // Reset display count when switching tabs\n  };\n  const sortResultsByTab = tabValue => {\n    const ratedAccounts = searchResults.filter(result => result.user.rate > 0).sort((a, b) => b.user.rate - a.user.rate);\n    const normalAccounts = searchResults.filter(result => !result.user.rate || result.user.rate === 0).sort((a, b) => a.userName.localeCompare(b.userName));\n    switch (tabValue) {\n      case 0:\n        // \"All\" tab - show both sections\n        return {\n          sorted: [...ratedAccounts, ...normalAccounts],\n          rated: ratedAccounts,\n          normal: normalAccounts\n        };\n      case 1:\n        // \"Rated\" tab - show only rated accounts\n        return {\n          sorted: ratedAccounts,\n          rated: ratedAccounts,\n          normal: []\n        };\n      default:\n        return {\n          sorted: searchResults,\n          rated: ratedAccounts,\n          normal: normalAccounts\n        };\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h5\",\n      component: \"h1\",\n      gutterBottom: true,\n      children: \"Search\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      component: \"h2\",\n      color: \"textSecondary\",\n      children: [\"Results for \", searchParams.get(\"q\")]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: \"flex\",\n        justifyContent: \"center\",\n        alignItems: \"center\",\n        minHeight: \"300px\",\n        flexDirection: \"column\",\n        gap: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 60\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"textSecondary\",\n        children: \"Searching...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 265,\n      columnNumber: 9\n    }, this) : searchResults.length > 0 ? /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 5\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          justifyContent: \"center\",\n          gap: \"10px\",\n          padding: \"10px\",\n          marginBottom: \"30px\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Tabs, {\n          value: activeTab,\n          onChange: handleTabChange,\n          \"aria-label\": \"Account tabs\",\n          children: [/*#__PURE__*/_jsxDEV(Tab, {\n            label: \"All\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            label: \"Rated\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 11\n      }, this), activeTab === 0 ?\n      /*#__PURE__*/\n      // \"All\" tab - show organized sections\n      _jsxDEV(Box, {\n        children: [ratedResults.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 4\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            component: \"h2\",\n            sx: {\n              mb: 1,\n              fontWeight: 600,\n              color: \"primary.main\"\n            },\n            children: \"Top Rated Accounts\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            sx: {\n              mb: 3\n            },\n            children: \"Accounts sorted by their ratings from highest to lowest\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 3,\n            children: ratedResults.slice(0, Math.min(displayCount, ratedResults.length)).map(result => /*#__PURE__*/_jsxDEV(ResultCard, {\n              result: result,\n              GetCategoryIcon: GetCategoryIcon\n            }, result.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 25\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 17\n        }, this), normalResults.length > 0 && displayCount > ratedResults.length && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 4\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            component: \"h2\",\n            sx: {\n              mb: 1,\n              fontWeight: 600,\n              color: \"primary.main\"\n            },\n            children: \"Other Accounts\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            sx: {\n              mb: 3\n            },\n            children: \"Accounts sorted alphabetically by username\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 3,\n            children: normalResults.slice(0, Math.max(0, displayCount - ratedResults.length)).map(result => /*#__PURE__*/_jsxDEV(ResultCard, {\n              result: result,\n              GetCategoryIcon: GetCategoryIcon\n            }, result.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 27\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 19\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 13\n      }, this) :\n      /*#__PURE__*/\n      // \"Rated\" tab - show only rated accounts\n      _jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: sortedResults.slice(0, displayCount).map(result => /*#__PURE__*/_jsxDEV(ResultCard, {\n          result: result,\n          GetCategoryIcon: GetCategoryIcon\n        }, result.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 17\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 377,\n        columnNumber: 13\n      }, this), displayCount < sortedResults.length && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          justifyContent: \"center\",\n          mt: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          onClick: () => setDisplayCount(displayCount + 5),\n          children: \"View More\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 281,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(SearchNotFound, {\n      searchQuery: searchParams.get(\"q\")\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 405,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 257,\n    columnNumber: 5\n  }, this);\n};\n_s(SearchResults, \"lGe2OXg3g5WOuqOVdgYrkDAFHV4=\", false, function () {\n  return [useSearch, useSearchParams];\n});\n_c2 = SearchResults;\nexport default SearchResults;\nvar _c, _c2;\n$RefreshReg$(_c, \"ResultCard\");\n$RefreshReg$(_c2, \"SearchResults\");", "map": {"version": 3, "names": ["useEffect", "useState", "Grid", "<PERSON><PERSON>", "Card", "Box", "Typography", "Tabs", "Tab", "Container", "Rating", "<PERSON><PERSON>", "LocationOnIcon", "WorkOutlineIcon", "StarBorderIcon", "Avatar", "Link", "useSearch", "SearchNotFound", "motion", "EmojiPeopleIcon", "EngineeringIcon", "ApartmentIcon", "BusinessCenterIcon", "useSearchParams", "CircularProgress", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ResultCard", "_ref", "result", "GetCategoryIcon", "item", "xs", "children", "div", "className", "initial", "opacity", "y", "animate", "transition", "duration", "ease", "to", "userName", "target", "rel", "style", "textDecoration", "color", "sx", "display", "flexDirection", "alignItems", "p", "gap", "boxShadow", "direction", "spacing", "flex", "alt", "user", "firstName", "lastName", "src", "profilePicture", "width", "height", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "marginLeft", "category", "rate", "name", "value", "readOnly", "emptyIcon", "fontSize", "country", "occupation", "_c", "SearchResults", "_s", "searchResults", "fetchSearchResults", "loading", "activeTab", "setActiveTab", "displayCount", "setDisplayCount", "sortedResults", "setSortedResults", "ratedResults", "setRatedResults", "normalResults", "setNormalResults", "searchParams", "handleSearch", "toString", "get", "sorted", "rated", "normal", "sortResultsByTab", "handleTabChange", "event", "newValue", "tabValue", "ratedAccounts", "filter", "sort", "a", "b", "normalAccounts", "localeCompare", "component", "gutterBottom", "justifyContent", "minHeight", "size", "length", "mt", "padding", "marginBottom", "onChange", "label", "mb", "fontWeight", "container", "slice", "Math", "min", "map", "id", "max", "onClick", "searchQuery", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/iDigitalX/ClientApp/src/pages/SearchResults.js"], "sourcesContent": ["import { useEffect, useState } from \"react\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON><PERSON>,\r\n  <PERSON>,\r\n  Box,\r\n  Typography,\r\n  Tabs,\r\n  Tab,\r\n  Container,\r\n  Rating,\r\n  Stack,\r\n} from \"@mui/material\";\r\nimport LocationOnIcon from \"@mui/icons-material/LocationOn\";\r\nimport WorkOutlineIcon from \"@mui/icons-material/WorkOutline\";\r\nimport StarBorderIcon from \"@mui/icons-material/StarBorder\";\r\nimport Avatar from \"@mui/material/Avatar\";\r\nimport { Link } from \"react-router-dom\";\r\nimport { useSearch } from \"../Context/SearchContext\";\r\nimport SearchNotFound from \"./SearchNotFound\";\r\nimport { motion } from \"framer-motion\";\r\nimport EmojiPeopleIcon from \"@mui/icons-material/EmojiPeople\";\r\nimport EngineeringIcon from \"@mui/icons-material/Engineering\";\r\nimport ApartmentIcon from \"@mui/icons-material/Apartment\";\r\nimport BusinessCenterIcon from \"@mui/icons-material/BusinessCenter\";\r\nimport { useSearchParams } from \"react-router-dom\";\r\nimport { CircularProgress } from \"@mui/material\";\r\n\r\n// Helper component for result card\r\nconst ResultCard = ({ result, GetCategoryIcon }) => (\r\n  <Grid item xs={12}>\r\n    <motion.div\r\n      className=\"animate-on-scroll\"\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      transition={{\r\n        duration: 0.5,\r\n        ease: \"easeOut\",\r\n      }}\r\n    >\r\n      <Link\r\n        to={`/Profile/${result.userName}`}\r\n        target=\"_blank\"\r\n        rel=\"noreferrer\"\r\n        style={{\r\n          textDecoration: \"none\",\r\n          color: \"inherit\",\r\n        }}\r\n      >\r\n        <Card\r\n          sx={{\r\n            display: \"flex\",\r\n            flexDirection: \"row\",\r\n            alignItems: \"center\",\r\n            p: 3,\r\n            gap: \"10px\",\r\n            boxShadow: \"0px 4px 10px rgba(0, 0, 0, 0.1)\",\r\n            transition: \"box-shadow 0.3s ease\",\r\n            \"&:hover\": {\r\n              boxShadow: \"0px 8px 20px rgba(0, 0, 0, 0.2)\",\r\n            },\r\n          }}\r\n        >\r\n          <Stack\r\n            direction=\"row\"\r\n            alignItems=\"center\"\r\n            spacing={2}\r\n            sx={{ flex: 1 }}\r\n          >\r\n            <Avatar\r\n              alt={result.user.firstName + \" \" + result.user.lastName}\r\n              src={result.profilePicture}\r\n              sx={{\r\n                width: 80,\r\n                height: 80,\r\n              }}\r\n            />\r\n            <Stack direction=\"column\" spacing={1}>\r\n              <Typography variant=\"h6\">\r\n                {result.user.firstName} {result.user.lastName}{\" \"}\r\n                <Stack\r\n                  direction=\"row\"\r\n                  alignItems=\"baseline\"\r\n                  spacing={1}\r\n                  sx={{\r\n                    display: \"inline-flex\",\r\n                    marginLeft: 1,\r\n                  }}\r\n                >\r\n                  <Typography\r\n                    variant=\"caption\"\r\n                    sx={{\r\n                      color: \"rgba(20, 43, 58, 0.5)\",\r\n                    }}\r\n                  >\r\n                    @{result.userName}\r\n                  </Typography>\r\n                  <Typography\r\n                    variant=\"body2\"\r\n                    sx={{\r\n                      color: \"rgba(20, 43, 58, 0.5)\",\r\n                    }}\r\n                  >\r\n                    {GetCategoryIcon(result.user.category)}\r\n                  </Typography>\r\n                </Stack>\r\n              </Typography>\r\n              {result.user.rate > 0 && (\r\n                <Rating\r\n                  name=\"read-only\"\r\n                  value={result.user.rate}\r\n                  readOnly\r\n                  emptyIcon={<StarBorderIcon fontSize=\"inherit\" />}\r\n                  sx={{\r\n                    marginLeft: \"10px\",\r\n                    fontSize: \"13px\",\r\n                  }}\r\n                />\r\n              )}\r\n              <Box\r\n                sx={{\r\n                  display: \"flex\",\r\n                  alignItems: \"center\",\r\n                  gap: \"10px\",\r\n                }}\r\n              >\r\n                {result.country && (\r\n                  <>\r\n                    <LocationOnIcon\r\n                      sx={{\r\n                        fontSize: \"15px\",\r\n                      }}\r\n                    />\r\n                    <Typography\r\n                      variant=\"caption\"\r\n                      sx={{\r\n                        color: \"rgba(20, 43, 58, 0.5)\",\r\n                      }}\r\n                    >\r\n                      {result.country}\r\n                    </Typography>\r\n                  </>\r\n                )}\r\n              </Box>\r\n              <Box\r\n                sx={{\r\n                  display: \"flex\",\r\n                  alignItems: \"center\",\r\n                  gap: \"10px\",\r\n                }}\r\n              >\r\n                {result.occupation && (\r\n                  <>\r\n                    <WorkOutlineIcon\r\n                      sx={{\r\n                        fontSize: \"15px\",\r\n                      }}\r\n                    />\r\n                    <Typography\r\n                      variant=\"caption\"\r\n                      sx={{\r\n                        color: \"rgba(20, 43, 58, 0.5)\",\r\n                      }}\r\n                    >\r\n                      {result.occupation}\r\n                    </Typography>\r\n                  </>\r\n                )}\r\n              </Box>\r\n            </Stack>\r\n          </Stack>\r\n        </Card>\r\n      </Link>\r\n    </motion.div>\r\n  </Grid>\r\n);\r\n\r\nconst SearchResults = () => {\r\n  const { searchResults, fetchSearchResults, loading } = useSearch();\r\n  const [activeTab, setActiveTab] = useState(0); // Default to \"All\" tab\r\n  const [displayCount, setDisplayCount] = useState(5);\r\n  const [sortedResults, setSortedResults] = useState([]);\r\n  const [ratedResults, setRatedResults] = useState([]);\r\n  const [normalResults, setNormalResults] = useState([]);\r\n  const [searchParams] = useSearchParams();\r\n\r\n  // Fetch search results only when search parameters change\r\n  useEffect(() => {\r\n    const handleSearch = async () => {\r\n      if (searchParams && searchParams.toString() !== \"\") {\r\n        await fetchSearchResults(searchParams.get(\"q\"));\r\n      }\r\n    };\r\n    handleSearch(); // Call search function only when searchParams change\r\n  }, [searchParams]);\r\n\r\n  // Update sortedResults whenever searchResults or activeTab changes\r\n  useEffect(() => {\r\n    const { sorted, rated, normal } = sortResultsByTab(activeTab);\r\n    setSortedResults(sorted);\r\n    setRatedResults(rated);\r\n    setNormalResults(normal);\r\n  }, [searchResults, activeTab]);\r\n\r\n  const GetCategoryIcon = (category) => {\r\n    switch (category) {\r\n      case \"Free\":\r\n        return <EmojiPeopleIcon fontSize=\"Large\" />;\r\n      case \"Student\":\r\n        return <BusinessCenterIcon fontSize=\"Large\" />;\r\n      case \"Freelance\":\r\n        return <EngineeringIcon fontSize=\"Large\" />;\r\n      case \"Entrepreneur\":\r\n        return <ApartmentIcon fontSize=\"Large\" />;\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  const handleTabChange = (event, newValue) => {\r\n    setActiveTab(newValue);\r\n    setDisplayCount(5); // Reset display count when switching tabs\r\n  };\r\n\r\n  const sortResultsByTab = (tabValue) => {\r\n    const ratedAccounts = searchResults\r\n      .filter((result) => result.user.rate > 0)\r\n      .sort((a, b) => b.user.rate - a.user.rate);\r\n\r\n    const normalAccounts = searchResults\r\n      .filter((result) => !result.user.rate || result.user.rate === 0)\r\n      .sort((a, b) => a.userName.localeCompare(b.userName));\r\n\r\n    switch (tabValue) {\r\n      case 0: // \"All\" tab - show both sections\r\n        return {\r\n          sorted: [...ratedAccounts, ...normalAccounts],\r\n          rated: ratedAccounts,\r\n          normal: normalAccounts,\r\n        };\r\n      case 1: // \"Rated\" tab - show only rated accounts\r\n        return {\r\n          sorted: ratedAccounts,\r\n          rated: ratedAccounts,\r\n          normal: [],\r\n        };\r\n      default:\r\n        return {\r\n          sorted: searchResults,\r\n          rated: ratedAccounts,\r\n          normal: normalAccounts,\r\n        };\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Container>\r\n      <Typography variant=\"h5\" component=\"h1\" gutterBottom>\r\n        Search\r\n      </Typography>\r\n      <Typography component=\"h2\" color=\"textSecondary\">\r\n        Results for {searchParams.get(\"q\")}\r\n      </Typography>\r\n      {loading ? (\r\n        <Box\r\n          sx={{\r\n            display: \"flex\",\r\n            justifyContent: \"center\",\r\n            alignItems: \"center\",\r\n            minHeight: \"300px\",\r\n            flexDirection: \"column\",\r\n            gap: 2,\r\n          }}\r\n        >\r\n          <CircularProgress size={60} />\r\n          <Typography variant=\"h6\" color=\"textSecondary\">\r\n            Searching...\r\n          </Typography>\r\n        </Box>\r\n      ) : searchResults.length > 0 ? (\r\n        <Box sx={{ mt: 5 }}>\r\n          <Box\r\n            sx={{\r\n              display: \"flex\",\r\n              justifyContent: \"center\",\r\n              gap: \"10px\",\r\n              padding: \"10px\",\r\n              marginBottom: \"30px\",\r\n            }}\r\n          >\r\n            <Tabs\r\n              value={activeTab}\r\n              onChange={handleTabChange}\r\n              aria-label=\"Account tabs\"\r\n            >\r\n              <Tab label=\"All\" />\r\n              <Tab label=\"Rated\" />\r\n            </Tabs>\r\n          </Box>\r\n          {activeTab === 0 ? (\r\n            // \"All\" tab - show organized sections\r\n            <Box>\r\n              {ratedResults.length > 0 && (\r\n                <Box sx={{ mb: 4 }}>\r\n                  <Typography\r\n                    variant=\"h5\"\r\n                    component=\"h2\"\r\n                    sx={{\r\n                      mb: 1,\r\n                      fontWeight: 600,\r\n                      color: \"primary.main\",\r\n                    }}\r\n                  >\r\n                    Top Rated Accounts\r\n                  </Typography>\r\n                  <Typography\r\n                    variant=\"body2\"\r\n                    color=\"text.secondary\"\r\n                    sx={{ mb: 3 }}\r\n                  >\r\n                    Accounts sorted by their ratings from highest to lowest\r\n                  </Typography>\r\n                  <Grid container spacing={3}>\r\n                    {ratedResults\r\n                      .slice(0, Math.min(displayCount, ratedResults.length))\r\n                      .map((result) => (\r\n                        <ResultCard\r\n                          key={result.id}\r\n                          result={result}\r\n                          GetCategoryIcon={GetCategoryIcon}\r\n                        />\r\n                      ))}\r\n                  </Grid>\r\n                </Box>\r\n              )}\r\n\r\n              {normalResults.length > 0 &&\r\n                displayCount > ratedResults.length && (\r\n                  <Box sx={{ mb: 4 }}>\r\n                    <Typography\r\n                      variant=\"h5\"\r\n                      component=\"h2\"\r\n                      sx={{\r\n                        mb: 1,\r\n                        fontWeight: 600,\r\n                        color: \"primary.main\",\r\n                      }}\r\n                    >\r\n                      Other Accounts\r\n                    </Typography>\r\n                    <Typography\r\n                      variant=\"body2\"\r\n                      color=\"text.secondary\"\r\n                      sx={{ mb: 3 }}\r\n                    >\r\n                      Accounts sorted alphabetically by username\r\n                    </Typography>\r\n                    <Grid container spacing={3}>\r\n                      {normalResults\r\n                        .slice(\r\n                          0,\r\n                          Math.max(0, displayCount - ratedResults.length)\r\n                        )\r\n                        .map((result) => (\r\n                          <ResultCard\r\n                            key={result.id}\r\n                            result={result}\r\n                            GetCategoryIcon={GetCategoryIcon}\r\n                          />\r\n                        ))}\r\n                    </Grid>\r\n                  </Box>\r\n                )}\r\n            </Box>\r\n          ) : (\r\n            // \"Rated\" tab - show only rated accounts\r\n            <Grid container spacing={3}>\r\n              {sortedResults.slice(0, displayCount).map((result) => (\r\n                <ResultCard\r\n                  key={result.id}\r\n                  result={result}\r\n                  GetCategoryIcon={GetCategoryIcon}\r\n                />\r\n              ))}\r\n            </Grid>\r\n          )}\r\n          {displayCount < sortedResults.length && (\r\n            <Box\r\n              sx={{\r\n                display: \"flex\",\r\n                justifyContent: \"center\",\r\n                mt: 3,\r\n              }}\r\n            >\r\n              <Button\r\n                variant=\"outlined\"\r\n                onClick={() => setDisplayCount(displayCount + 5)}\r\n              >\r\n                View More\r\n              </Button>\r\n            </Box>\r\n          )}\r\n        </Box>\r\n      ) : (\r\n        <SearchNotFound searchQuery={searchParams.get(\"q\")} />\r\n      )}\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default SearchResults;\r\n"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SACEC,IAAI,EACJC,MAAM,EACNC,IAAI,EACJC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,GAAG,EACHC,SAAS,EACTC,MAAM,EACNC,KAAK,QACA,eAAe;AACtB,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,eAAe,MAAM,iCAAiC;AAC7D,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,MAAM,MAAM,sBAAsB;AACzC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,SAAS,QAAQ,0BAA0B;AACpD,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,SAASC,MAAM,QAAQ,eAAe;AACtC,OAAOC,eAAe,MAAM,iCAAiC;AAC7D,OAAOC,eAAe,MAAM,iCAAiC;AAC7D,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,SAASC,eAAe,QAAQ,kBAAkB;AAClD,SAASC,gBAAgB,QAAQ,eAAe;;AAEhD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,UAAU,GAAGC,IAAA;EAAA,IAAC;IAAEC,MAAM;IAAEC;EAAgB,CAAC,GAAAF,IAAA;EAAA,oBAC7CJ,OAAA,CAACzB,IAAI;IAACgC,IAAI;IAACC,EAAE,EAAE,EAAG;IAAAC,QAAA,eAChBT,OAAA,CAACR,MAAM,CAACkB,GAAG;MACTC,SAAS,EAAC,mBAAmB;MAC7BC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BE,UAAU,EAAE;QACVC,QAAQ,EAAE,GAAG;QACbC,IAAI,EAAE;MACR,CAAE;MAAAT,QAAA,eAEFT,OAAA,CAACX,IAAI;QACH8B,EAAE,EAAE,YAAYd,MAAM,CAACe,QAAQ,EAAG;QAClCC,MAAM,EAAC,QAAQ;QACfC,GAAG,EAAC,YAAY;QAChBC,KAAK,EAAE;UACLC,cAAc,EAAE,MAAM;UACtBC,KAAK,EAAE;QACT,CAAE;QAAAhB,QAAA,eAEFT,OAAA,CAACvB,IAAI;UACHiD,EAAE,EAAE;YACFC,OAAO,EAAE,MAAM;YACfC,aAAa,EAAE,KAAK;YACpBC,UAAU,EAAE,QAAQ;YACpBC,CAAC,EAAE,CAAC;YACJC,GAAG,EAAE,MAAM;YACXC,SAAS,EAAE,iCAAiC;YAC5ChB,UAAU,EAAE,sBAAsB;YAClC,SAAS,EAAE;cACTgB,SAAS,EAAE;YACb;UACF,CAAE;UAAAvB,QAAA,eAEFT,OAAA,CAAChB,KAAK;YACJiD,SAAS,EAAC,KAAK;YACfJ,UAAU,EAAC,QAAQ;YACnBK,OAAO,EAAE,CAAE;YACXR,EAAE,EAAE;cAAES,IAAI,EAAE;YAAE,CAAE;YAAA1B,QAAA,gBAEhBT,OAAA,CAACZ,MAAM;cACLgD,GAAG,EAAE/B,MAAM,CAACgC,IAAI,CAACC,SAAS,GAAG,GAAG,GAAGjC,MAAM,CAACgC,IAAI,CAACE,QAAS;cACxDC,GAAG,EAAEnC,MAAM,CAACoC,cAAe;cAC3Bf,EAAE,EAAE;gBACFgB,KAAK,EAAE,EAAE;gBACTC,MAAM,EAAE;cACV;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACF/C,OAAA,CAAChB,KAAK;cAACiD,SAAS,EAAC,QAAQ;cAACC,OAAO,EAAE,CAAE;cAAAzB,QAAA,gBACnCT,OAAA,CAACrB,UAAU;gBAACqE,OAAO,EAAC,IAAI;gBAAAvC,QAAA,GACrBJ,MAAM,CAACgC,IAAI,CAACC,SAAS,EAAC,GAAC,EAACjC,MAAM,CAACgC,IAAI,CAACE,QAAQ,EAAE,GAAG,eAClDvC,OAAA,CAAChB,KAAK;kBACJiD,SAAS,EAAC,KAAK;kBACfJ,UAAU,EAAC,UAAU;kBACrBK,OAAO,EAAE,CAAE;kBACXR,EAAE,EAAE;oBACFC,OAAO,EAAE,aAAa;oBACtBsB,UAAU,EAAE;kBACd,CAAE;kBAAAxC,QAAA,gBAEFT,OAAA,CAACrB,UAAU;oBACTqE,OAAO,EAAC,SAAS;oBACjBtB,EAAE,EAAE;sBACFD,KAAK,EAAE;oBACT,CAAE;oBAAAhB,QAAA,GACH,GACE,EAACJ,MAAM,CAACe,QAAQ;kBAAA;oBAAAwB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACb/C,OAAA,CAACrB,UAAU;oBACTqE,OAAO,EAAC,OAAO;oBACftB,EAAE,EAAE;sBACFD,KAAK,EAAE;oBACT,CAAE;oBAAAhB,QAAA,EAEDH,eAAe,CAACD,MAAM,CAACgC,IAAI,CAACa,QAAQ;kBAAC;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,EACZ1C,MAAM,CAACgC,IAAI,CAACc,IAAI,GAAG,CAAC,iBACnBnD,OAAA,CAACjB,MAAM;gBACLqE,IAAI,EAAC,WAAW;gBAChBC,KAAK,EAAEhD,MAAM,CAACgC,IAAI,CAACc,IAAK;gBACxBG,QAAQ;gBACRC,SAAS,eAAEvD,OAAA,CAACb,cAAc;kBAACqE,QAAQ,EAAC;gBAAS;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACjDrB,EAAE,EAAE;kBACFuB,UAAU,EAAE,MAAM;kBAClBO,QAAQ,EAAE;gBACZ;cAAE;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACF,eACD/C,OAAA,CAACtB,GAAG;gBACFgD,EAAE,EAAE;kBACFC,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBE,GAAG,EAAE;gBACP,CAAE;gBAAAtB,QAAA,EAEDJ,MAAM,CAACoD,OAAO,iBACbzD,OAAA,CAAAE,SAAA;kBAAAO,QAAA,gBACET,OAAA,CAACf,cAAc;oBACbyC,EAAE,EAAE;sBACF8B,QAAQ,EAAE;oBACZ;kBAAE;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACF/C,OAAA,CAACrB,UAAU;oBACTqE,OAAO,EAAC,SAAS;oBACjBtB,EAAE,EAAE;sBACFD,KAAK,EAAE;oBACT,CAAE;oBAAAhB,QAAA,EAEDJ,MAAM,CAACoD;kBAAO;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA,eACb;cACH;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACN/C,OAAA,CAACtB,GAAG;gBACFgD,EAAE,EAAE;kBACFC,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBE,GAAG,EAAE;gBACP,CAAE;gBAAAtB,QAAA,EAEDJ,MAAM,CAACqD,UAAU,iBAChB1D,OAAA,CAAAE,SAAA;kBAAAO,QAAA,gBACET,OAAA,CAACd,eAAe;oBACdwC,EAAE,EAAE;sBACF8B,QAAQ,EAAE;oBACZ;kBAAE;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACF/C,OAAA,CAACrB,UAAU;oBACTqE,OAAO,EAAC,SAAS;oBACjBtB,EAAE,EAAE;sBACFD,KAAK,EAAE;oBACT,CAAE;oBAAAhB,QAAA,EAEDJ,MAAM,CAACqD;kBAAU;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CAAC;gBAAA,eACb;cACH;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAAA,CACR;AAACY,EAAA,GAlJIxD,UAAU;AAoJhB,MAAMyD,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM;IAAEC,aAAa;IAAEC,kBAAkB;IAAEC;EAAQ,CAAC,GAAG1E,SAAS,CAAC,CAAC;EAClE,MAAM,CAAC2E,SAAS,EAAEC,YAAY,CAAC,GAAG5F,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/C,MAAM,CAAC6F,YAAY,EAAEC,eAAe,CAAC,GAAG9F,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAAC+F,aAAa,EAAEC,gBAAgB,CAAC,GAAGhG,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACiG,YAAY,EAAEC,eAAe,CAAC,GAAGlG,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACmG,aAAa,EAAEC,gBAAgB,CAAC,GAAGpG,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACqG,YAAY,CAAC,GAAG9E,eAAe,CAAC,CAAC;;EAExC;EACAxB,SAAS,CAAC,MAAM;IACd,MAAMuG,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAID,YAAY,IAAIA,YAAY,CAACE,QAAQ,CAAC,CAAC,KAAK,EAAE,EAAE;QAClD,MAAMd,kBAAkB,CAACY,YAAY,CAACG,GAAG,CAAC,GAAG,CAAC,CAAC;MACjD;IACF,CAAC;IACDF,YAAY,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,EAAE,CAACD,YAAY,CAAC,CAAC;;EAElB;EACAtG,SAAS,CAAC,MAAM;IACd,MAAM;MAAE0G,MAAM;MAAEC,KAAK;MAAEC;IAAO,CAAC,GAAGC,gBAAgB,CAACjB,SAAS,CAAC;IAC7DK,gBAAgB,CAACS,MAAM,CAAC;IACxBP,eAAe,CAACQ,KAAK,CAAC;IACtBN,gBAAgB,CAACO,MAAM,CAAC;EAC1B,CAAC,EAAE,CAACnB,aAAa,EAAEG,SAAS,CAAC,CAAC;EAE9B,MAAM3D,eAAe,GAAI4C,QAAQ,IAAK;IACpC,QAAQA,QAAQ;MACd,KAAK,MAAM;QACT,oBAAOlD,OAAA,CAACP,eAAe;UAAC+D,QAAQ,EAAC;QAAO;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7C,KAAK,SAAS;QACZ,oBAAO/C,OAAA,CAACJ,kBAAkB;UAAC4D,QAAQ,EAAC;QAAO;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAChD,KAAK,WAAW;QACd,oBAAO/C,OAAA,CAACN,eAAe;UAAC8D,QAAQ,EAAC;QAAO;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7C,KAAK,cAAc;QACjB,oBAAO/C,OAAA,CAACL,aAAa;UAAC6D,QAAQ,EAAC;QAAO;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC3C;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,MAAMoC,eAAe,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC3CnB,YAAY,CAACmB,QAAQ,CAAC;IACtBjB,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC;EAED,MAAMc,gBAAgB,GAAII,QAAQ,IAAK;IACrC,MAAMC,aAAa,GAAGzB,aAAa,CAChC0B,MAAM,CAAEnF,MAAM,IAAKA,MAAM,CAACgC,IAAI,CAACc,IAAI,GAAG,CAAC,CAAC,CACxCsC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACtD,IAAI,CAACc,IAAI,GAAGuC,CAAC,CAACrD,IAAI,CAACc,IAAI,CAAC;IAE5C,MAAMyC,cAAc,GAAG9B,aAAa,CACjC0B,MAAM,CAAEnF,MAAM,IAAK,CAACA,MAAM,CAACgC,IAAI,CAACc,IAAI,IAAI9C,MAAM,CAACgC,IAAI,CAACc,IAAI,KAAK,CAAC,CAAC,CAC/DsC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACtE,QAAQ,CAACyE,aAAa,CAACF,CAAC,CAACvE,QAAQ,CAAC,CAAC;IAEvD,QAAQkE,QAAQ;MACd,KAAK,CAAC;QAAE;QACN,OAAO;UACLP,MAAM,EAAE,CAAC,GAAGQ,aAAa,EAAE,GAAGK,cAAc,CAAC;UAC7CZ,KAAK,EAAEO,aAAa;UACpBN,MAAM,EAAEW;QACV,CAAC;MACH,KAAK,CAAC;QAAE;QACN,OAAO;UACLb,MAAM,EAAEQ,aAAa;UACrBP,KAAK,EAAEO,aAAa;UACpBN,MAAM,EAAE;QACV,CAAC;MACH;QACE,OAAO;UACLF,MAAM,EAAEjB,aAAa;UACrBkB,KAAK,EAAEO,aAAa;UACpBN,MAAM,EAAEW;QACV,CAAC;IACL;EACF,CAAC;EAED,oBACE5F,OAAA,CAAClB,SAAS;IAAA2B,QAAA,gBACRT,OAAA,CAACrB,UAAU;MAACqE,OAAO,EAAC,IAAI;MAAC8C,SAAS,EAAC,IAAI;MAACC,YAAY;MAAAtF,QAAA,EAAC;IAErD;MAAAmC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACb/C,OAAA,CAACrB,UAAU;MAACmH,SAAS,EAAC,IAAI;MAACrE,KAAK,EAAC,eAAe;MAAAhB,QAAA,GAAC,cACnC,EAACkE,YAAY,CAACG,GAAG,CAAC,GAAG,CAAC;IAAA;MAAAlC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC,EACZiB,OAAO,gBACNhE,OAAA,CAACtB,GAAG;MACFgD,EAAE,EAAE;QACFC,OAAO,EAAE,MAAM;QACfqE,cAAc,EAAE,QAAQ;QACxBnE,UAAU,EAAE,QAAQ;QACpBoE,SAAS,EAAE,OAAO;QAClBrE,aAAa,EAAE,QAAQ;QACvBG,GAAG,EAAE;MACP,CAAE;MAAAtB,QAAA,gBAEFT,OAAA,CAACF,gBAAgB;QAACoG,IAAI,EAAE;MAAG;QAAAtD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9B/C,OAAA,CAACrB,UAAU;QAACqE,OAAO,EAAC,IAAI;QAACvB,KAAK,EAAC,eAAe;QAAAhB,QAAA,EAAC;MAE/C;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,GACJe,aAAa,CAACqC,MAAM,GAAG,CAAC,gBAC1BnG,OAAA,CAACtB,GAAG;MAACgD,EAAE,EAAE;QAAE0E,EAAE,EAAE;MAAE,CAAE;MAAA3F,QAAA,gBACjBT,OAAA,CAACtB,GAAG;QACFgD,EAAE,EAAE;UACFC,OAAO,EAAE,MAAM;UACfqE,cAAc,EAAE,QAAQ;UACxBjE,GAAG,EAAE,MAAM;UACXsE,OAAO,EAAE,MAAM;UACfC,YAAY,EAAE;QAChB,CAAE;QAAA7F,QAAA,eAEFT,OAAA,CAACpB,IAAI;UACHyE,KAAK,EAAEY,SAAU;UACjBsC,QAAQ,EAAEpB,eAAgB;UAC1B,cAAW,cAAc;UAAA1E,QAAA,gBAEzBT,OAAA,CAACnB,GAAG;YAAC2H,KAAK,EAAC;UAAK;YAAA5D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnB/C,OAAA,CAACnB,GAAG;YAAC2H,KAAK,EAAC;UAAO;YAAA5D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,EACLkB,SAAS,KAAK,CAAC;MAAA;MACd;MACAjE,OAAA,CAACtB,GAAG;QAAA+B,QAAA,GACD8D,YAAY,CAAC4B,MAAM,GAAG,CAAC,iBACtBnG,OAAA,CAACtB,GAAG;UAACgD,EAAE,EAAE;YAAE+E,EAAE,EAAE;UAAE,CAAE;UAAAhG,QAAA,gBACjBT,OAAA,CAACrB,UAAU;YACTqE,OAAO,EAAC,IAAI;YACZ8C,SAAS,EAAC,IAAI;YACdpE,EAAE,EAAE;cACF+E,EAAE,EAAE,CAAC;cACLC,UAAU,EAAE,GAAG;cACfjF,KAAK,EAAE;YACT,CAAE;YAAAhB,QAAA,EACH;UAED;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb/C,OAAA,CAACrB,UAAU;YACTqE,OAAO,EAAC,OAAO;YACfvB,KAAK,EAAC,gBAAgB;YACtBC,EAAE,EAAE;cAAE+E,EAAE,EAAE;YAAE,CAAE;YAAAhG,QAAA,EACf;UAED;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb/C,OAAA,CAACzB,IAAI;YAACoI,SAAS;YAACzE,OAAO,EAAE,CAAE;YAAAzB,QAAA,EACxB8D,YAAY,CACVqC,KAAK,CAAC,CAAC,EAAEC,IAAI,CAACC,GAAG,CAAC3C,YAAY,EAAEI,YAAY,CAAC4B,MAAM,CAAC,CAAC,CACrDY,GAAG,CAAE1G,MAAM,iBACVL,OAAA,CAACG,UAAU;cAETE,MAAM,EAAEA,MAAO;cACfC,eAAe,EAAEA;YAAgB,GAF5BD,MAAM,CAAC2G,EAAE;cAAApE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGf,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,EAEA0B,aAAa,CAAC0B,MAAM,GAAG,CAAC,IACvBhC,YAAY,GAAGI,YAAY,CAAC4B,MAAM,iBAChCnG,OAAA,CAACtB,GAAG;UAACgD,EAAE,EAAE;YAAE+E,EAAE,EAAE;UAAE,CAAE;UAAAhG,QAAA,gBACjBT,OAAA,CAACrB,UAAU;YACTqE,OAAO,EAAC,IAAI;YACZ8C,SAAS,EAAC,IAAI;YACdpE,EAAE,EAAE;cACF+E,EAAE,EAAE,CAAC;cACLC,UAAU,EAAE,GAAG;cACfjF,KAAK,EAAE;YACT,CAAE;YAAAhB,QAAA,EACH;UAED;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb/C,OAAA,CAACrB,UAAU;YACTqE,OAAO,EAAC,OAAO;YACfvB,KAAK,EAAC,gBAAgB;YACtBC,EAAE,EAAE;cAAE+E,EAAE,EAAE;YAAE,CAAE;YAAAhG,QAAA,EACf;UAED;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb/C,OAAA,CAACzB,IAAI;YAACoI,SAAS;YAACzE,OAAO,EAAE,CAAE;YAAAzB,QAAA,EACxBgE,aAAa,CACXmC,KAAK,CACJ,CAAC,EACDC,IAAI,CAACI,GAAG,CAAC,CAAC,EAAE9C,YAAY,GAAGI,YAAY,CAAC4B,MAAM,CAChD,CAAC,CACAY,GAAG,CAAE1G,MAAM,iBACVL,OAAA,CAACG,UAAU;cAETE,MAAM,EAAEA,MAAO;cACfC,eAAe,EAAEA;YAAgB,GAF5BD,MAAM,CAAC2G,EAAE;cAAApE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGf,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;MAAA;MAEN;MACA/C,OAAA,CAACzB,IAAI;QAACoI,SAAS;QAACzE,OAAO,EAAE,CAAE;QAAAzB,QAAA,EACxB4D,aAAa,CAACuC,KAAK,CAAC,CAAC,EAAEzC,YAAY,CAAC,CAAC4C,GAAG,CAAE1G,MAAM,iBAC/CL,OAAA,CAACG,UAAU;UAETE,MAAM,EAAEA,MAAO;UACfC,eAAe,EAAEA;QAAgB,GAF5BD,MAAM,CAAC2G,EAAE;UAAApE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGf,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACP,EACAoB,YAAY,GAAGE,aAAa,CAAC8B,MAAM,iBAClCnG,OAAA,CAACtB,GAAG;QACFgD,EAAE,EAAE;UACFC,OAAO,EAAE,MAAM;UACfqE,cAAc,EAAE,QAAQ;UACxBI,EAAE,EAAE;QACN,CAAE;QAAA3F,QAAA,eAEFT,OAAA,CAACxB,MAAM;UACLwE,OAAO,EAAC,UAAU;UAClBkE,OAAO,EAAEA,CAAA,KAAM9C,eAAe,CAACD,YAAY,GAAG,CAAC,CAAE;UAAA1D,QAAA,EAClD;QAED;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,gBAEN/C,OAAA,CAACT,cAAc;MAAC4H,WAAW,EAAExC,YAAY,CAACG,GAAG,CAAC,GAAG;IAAE;MAAAlC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CACtD;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACQ,CAAC;AAEhB,CAAC;AAACc,EAAA,CAvOID,aAAa;EAAA,QACsCtE,SAAS,EAMzCO,eAAe;AAAA;AAAAuH,GAAA,GAPlCxD,aAAa;AAyOnB,eAAeA,aAAa;AAAC,IAAAD,EAAA,EAAAyD,GAAA;AAAAC,YAAA,CAAA1D,EAAA;AAAA0D,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}