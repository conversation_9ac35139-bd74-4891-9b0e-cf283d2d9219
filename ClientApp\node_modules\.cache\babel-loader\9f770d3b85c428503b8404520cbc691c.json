{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\iDigitalX\\\\ClientApp\\\\src\\\\sections\\\\auth\\\\signup\\\\PhotoSelector.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useRef } from \"react\";\nimport { Button } from \"@mui/material\";\nimport PostAddIcon from \"@mui/icons-material/PostAdd\";\nimport { toast } from \"react-toastify\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PhotoSelector = _ref => {\n  _s();\n  let {\n    onSelect\n  } = _ref;\n  const fileInputRef = useRef(null);\n  const maxSize = 1024 * 1024;\n  const handleFileChange = event => {\n    const file = event.target.files[0];\n    if (file) {\n      // Validate file type\n      if (!file.type.startsWith('image/')) {\n        toast.error(\"Please select a valid image file\", {\n          position: \"top-center\",\n          autoClose: 3000\n        });\n        return;\n      }\n\n      // Validate file size\n      if (file.size > maxSize) {\n        toast.error(\"File size exceeds the 1MB limit. Please select a smaller file.\", {\n          position: \"top-center\",\n          autoClose: 3000\n        });\n        return;\n      }\n      const reader = new FileReader();\n      reader.onload = () => {\n        try {\n          onSelect(reader.result);\n          toast.success(\"Image uploaded successfully\", {\n            position: \"top-center\",\n            autoClose: 1000\n          });\n        } catch (error) {\n          toast.error(\"Error processing image\", {\n            position: \"top-center\",\n            autoClose: 3000\n          });\n        }\n      };\n      reader.onerror = () => {\n        toast.error(\"Error reading file\", {\n          position: \"top-center\",\n          autoClose: 3000\n        });\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n  const handleButtonClick = () => {\n    fileInputRef.current.click();\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n      type: \"file\",\n      accept: \"image/*\",\n      ref: fileInputRef,\n      style: {\n        display: \"none\"\n      },\n      onChange: handleFileChange\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      variant: \"contained\",\n      onClick: handleButtonClick,\n      style: {\n        position: \"absolute\",\n        width: \"0px\",\n        height: \"0px\",\n        border: \"none\",\n        padding: \"0\",\n        fontSize: \"20px\",\n        backgroundColor: \"transparent\",\n        color: \"#ff715b\"\n      },\n      children: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"bi bi-plus-circle-fill\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_s(PhotoSelector, \"YQqvMxdmg33cmOXmQcOjJm+FLVI=\");\n_c = PhotoSelector;\nexport const FileSelector = _ref2 => {\n  _s2();\n  let {\n    onSelect\n  } = _ref2;\n  const fileInputRef = useRef(null);\n  const maxSize = 1024 * 1024;\n  const handleFileChange = event => {\n    const file = event.target.files[0];\n    if (file) {\n      if (file.type != \"application/pdf\") {\n        alert(\"Invalid file type. Only PDF are allowed.\");\n        return;\n      }\n      if (file.size > maxSize) {\n        alert(\"File size exceeds the 1MB limit. Please select a smaller file.\");\n        return;\n      }\n      const reader = new FileReader();\n      reader.onload = () => {\n        onSelect(reader.result);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n  const handleButtonClick = () => {\n    fileInputRef.current.click();\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n      type: \"file\",\n      accept: \"application/pdf\",\n      ref: fileInputRef,\n      style: {\n        display: \"none\"\n      },\n      onChange: handleFileChange\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      variant: \"contained\",\n      onClick: handleButtonClick,\n      color: \"primary\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          marginRight: \"10px\"\n        },\n        children: \"Upload\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(PostAddIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_s2(FileSelector, \"YQqvMxdmg33cmOXmQcOjJm+FLVI=\");\n_c2 = FileSelector;\nexport default PhotoSelector;\nvar _c, _c2;\n$RefreshReg$(_c, \"PhotoSelector\");\n$RefreshReg$(_c2, \"FileSelector\");", "map": {"version": 3, "names": ["React", "useRef", "<PERSON><PERSON>", "PostAddIcon", "toast", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PhotoSelector", "_ref", "_s", "onSelect", "fileInputRef", "maxSize", "handleFileChange", "event", "file", "target", "files", "type", "startsWith", "error", "position", "autoClose", "size", "reader", "FileReader", "onload", "result", "success", "onerror", "readAsDataURL", "handleButtonClick", "current", "click", "children", "accept", "ref", "style", "display", "onChange", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "onClick", "width", "height", "border", "padding", "fontSize", "backgroundColor", "color", "className", "_c", "FileSelector", "_ref2", "_s2", "alert", "marginRight", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/iDigitalX/ClientApp/src/sections/auth/signup/PhotoSelector.js"], "sourcesContent": ["import React, { useRef } from \"react\";\r\nimport { Button } from \"@mui/material\";\r\nimport PostAddIcon from \"@mui/icons-material/PostAdd\";\r\nimport { toast } from \"react-toastify\";\r\n\r\nconst PhotoSelector = ({ onSelect }) => {\r\n    const fileInputRef = useRef(null);\r\n    const maxSize = 1024 * 1024;\r\n\r\n    const handleFileChange = (event) => {\r\n        const file = event.target.files[0];\r\n\r\n        if (file) {\r\n            // Validate file type\r\n            if (!file.type.startsWith('image/')) {\r\n                toast.error(\"Please select a valid image file\", {\r\n                    position: \"top-center\",\r\n                    autoClose: 3000,\r\n                });\r\n                return;\r\n            }\r\n\r\n            // Validate file size\r\n            if (file.size > maxSize) {\r\n                toast.error(\"File size exceeds the 1MB limit. Please select a smaller file.\", {\r\n                    position: \"top-center\",\r\n                    autoClose: 3000,\r\n                });\r\n                return;\r\n            }\r\n\r\n            const reader = new FileReader();\r\n            reader.onload = () => {\r\n                try {\r\n                    onSelect(reader.result);\r\n                    toast.success(\"Image uploaded successfully\", {\r\n                        position: \"top-center\",\r\n                        autoClose: 1000,\r\n                    });\r\n                } catch (error) {\r\n                    toast.error(\"Error processing image\", {\r\n                        position: \"top-center\",\r\n                        autoClose: 3000,\r\n                    });\r\n                }\r\n            };\r\n            reader.onerror = () => {\r\n                toast.error(\"Error reading file\", {\r\n                    position: \"top-center\",\r\n                    autoClose: 3000,\r\n                });\r\n            };\r\n            reader.readAsDataURL(file);\r\n        }\r\n    };\r\n\r\n    const handleButtonClick = () => {\r\n        fileInputRef.current.click();\r\n    };\r\n\r\n    return (\r\n        <>\r\n            <input\r\n                type=\"file\"\r\n                accept=\"image/*\"\r\n                ref={fileInputRef}\r\n                style={{ display: \"none\" }}\r\n                onChange={handleFileChange}\r\n            />\r\n            <Button\r\n                variant=\"contained\"\r\n                onClick={handleButtonClick}\r\n                style={{\r\n                    position: \"absolute\",\r\n                    width: \"0px\",\r\n                    height: \"0px\",\r\n                    border: \"none\",\r\n                    padding: \"0\",\r\n                    fontSize: \"20px\",\r\n                    backgroundColor: \"transparent\",\r\n                    color: \"#ff715b\",\r\n                }}\r\n            >\r\n                <i className=\"bi bi-plus-circle-fill\"></i>\r\n            </Button>\r\n        </>\r\n    );\r\n};\r\n\r\nexport const FileSelector = ({ onSelect }) => {\r\n    const fileInputRef = useRef(null);\r\n    const maxSize = 1024 * 1024;\r\n\r\n    const handleFileChange = (event) => {\r\n        const file = event.target.files[0];\r\n\r\n        if (file) {\r\n            if (file.type != \"application/pdf\") {\r\n                alert(\"Invalid file type. Only PDF are allowed.\");\r\n                return;\r\n            }\r\n\r\n            if (file.size > maxSize) {\r\n                alert(\r\n                    \"File size exceeds the 1MB limit. Please select a smaller file.\"\r\n                );\r\n                return;\r\n            }\r\n\r\n            const reader = new FileReader();\r\n            reader.onload = () => {\r\n                onSelect(reader.result);\r\n            };\r\n            reader.readAsDataURL(file);\r\n        }\r\n    };\r\n\r\n    const handleButtonClick = () => {\r\n        fileInputRef.current.click();\r\n    };\r\n\r\n    return (\r\n        <>\r\n            <input\r\n                type=\"file\"\r\n                accept=\"application/pdf\"\r\n                ref={fileInputRef}\r\n                style={{ display: \"none\" }}\r\n                onChange={handleFileChange}\r\n            />\r\n            <Button\r\n                variant=\"contained\"\r\n                onClick={handleButtonClick}\r\n                color=\"primary\"\r\n            >\r\n                <span\r\n                    style={{\r\n                        marginRight: \"10px\",\r\n                    }}\r\n                >\r\n                    Upload\r\n                </span>\r\n                <PostAddIcon />\r\n            </Button>\r\n        </>\r\n    );\r\n};\r\n\r\nexport default PhotoSelector;\r\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,MAAM,QAAQ,OAAO;AACrC,SAASC,MAAM,QAAQ,eAAe;AACtC,OAAOC,WAAW,MAAM,6BAA6B;AACrD,SAASC,KAAK,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEvC,MAAMC,aAAa,GAAGC,IAAA,IAAkB;EAAAC,EAAA;EAAA,IAAjB;IAAEC;EAAS,CAAC,GAAAF,IAAA;EAC/B,MAAMG,YAAY,GAAGZ,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMa,OAAO,GAAG,IAAI,GAAG,IAAI;EAE3B,MAAMC,gBAAgB,GAAIC,KAAK,IAAK;IAChC,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAElC,IAAIF,IAAI,EAAE;MACN;MACA,IAAI,CAACA,IAAI,CAACG,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;QACjCjB,KAAK,CAACkB,KAAK,CAAC,kCAAkC,EAAE;UAC5CC,QAAQ,EAAE,YAAY;UACtBC,SAAS,EAAE;QACf,CAAC,CAAC;QACF;MACJ;;MAEA;MACA,IAAIP,IAAI,CAACQ,IAAI,GAAGX,OAAO,EAAE;QACrBV,KAAK,CAACkB,KAAK,CAAC,gEAAgE,EAAE;UAC1EC,QAAQ,EAAE,YAAY;UACtBC,SAAS,EAAE;QACf,CAAC,CAAC;QACF;MACJ;MAEA,MAAME,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAG,MAAM;QAClB,IAAI;UACAhB,QAAQ,CAACc,MAAM,CAACG,MAAM,CAAC;UACvBzB,KAAK,CAAC0B,OAAO,CAAC,6BAA6B,EAAE;YACzCP,QAAQ,EAAE,YAAY;YACtBC,SAAS,EAAE;UACf,CAAC,CAAC;QACN,CAAC,CAAC,OAAOF,KAAK,EAAE;UACZlB,KAAK,CAACkB,KAAK,CAAC,wBAAwB,EAAE;YAClCC,QAAQ,EAAE,YAAY;YACtBC,SAAS,EAAE;UACf,CAAC,CAAC;QACN;MACJ,CAAC;MACDE,MAAM,CAACK,OAAO,GAAG,MAAM;QACnB3B,KAAK,CAACkB,KAAK,CAAC,oBAAoB,EAAE;UAC9BC,QAAQ,EAAE,YAAY;UACtBC,SAAS,EAAE;QACf,CAAC,CAAC;MACN,CAAC;MACDE,MAAM,CAACM,aAAa,CAACf,IAAI,CAAC;IAC9B;EACJ,CAAC;EAED,MAAMgB,iBAAiB,GAAGA,CAAA,KAAM;IAC5BpB,YAAY,CAACqB,OAAO,CAACC,KAAK,CAAC,CAAC;EAChC,CAAC;EAED,oBACI7B,OAAA,CAAAE,SAAA;IAAA4B,QAAA,gBACI9B,OAAA;MACIc,IAAI,EAAC,MAAM;MACXiB,MAAM,EAAC,SAAS;MAChBC,GAAG,EAAEzB,YAAa;MAClB0B,KAAK,EAAE;QAAEC,OAAO,EAAE;MAAO,CAAE;MAC3BC,QAAQ,EAAE1B;IAAiB;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC,eACFvC,OAAA,CAACJ,MAAM;MACH4C,OAAO,EAAC,WAAW;MACnBC,OAAO,EAAEd,iBAAkB;MAC3BM,KAAK,EAAE;QACHhB,QAAQ,EAAE,UAAU;QACpByB,KAAK,EAAE,KAAK;QACZC,MAAM,EAAE,KAAK;QACbC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,GAAG;QACZC,QAAQ,EAAE,MAAM;QAChBC,eAAe,EAAE,aAAa;QAC9BC,KAAK,EAAE;MACX,CAAE;MAAAlB,QAAA,eAEF9B,OAAA;QAAGiD,SAAS,EAAC;MAAwB;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC;EAAA,eACX,CAAC;AAEX,CAAC;AAAClC,EAAA,CAlFIF,aAAa;AAAA+C,EAAA,GAAb/C,aAAa;AAoFnB,OAAO,MAAMgD,YAAY,GAAGC,KAAA,IAAkB;EAAAC,GAAA;EAAA,IAAjB;IAAE/C;EAAS,CAAC,GAAA8C,KAAA;EACrC,MAAM7C,YAAY,GAAGZ,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMa,OAAO,GAAG,IAAI,GAAG,IAAI;EAE3B,MAAMC,gBAAgB,GAAIC,KAAK,IAAK;IAChC,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAElC,IAAIF,IAAI,EAAE;MACN,IAAIA,IAAI,CAACG,IAAI,IAAI,iBAAiB,EAAE;QAChCwC,KAAK,CAAC,0CAA0C,CAAC;QACjD;MACJ;MAEA,IAAI3C,IAAI,CAACQ,IAAI,GAAGX,OAAO,EAAE;QACrB8C,KAAK,CACD,gEACJ,CAAC;QACD;MACJ;MAEA,MAAMlC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAG,MAAM;QAClBhB,QAAQ,CAACc,MAAM,CAACG,MAAM,CAAC;MAC3B,CAAC;MACDH,MAAM,CAACM,aAAa,CAACf,IAAI,CAAC;IAC9B;EACJ,CAAC;EAED,MAAMgB,iBAAiB,GAAGA,CAAA,KAAM;IAC5BpB,YAAY,CAACqB,OAAO,CAACC,KAAK,CAAC,CAAC;EAChC,CAAC;EAED,oBACI7B,OAAA,CAAAE,SAAA;IAAA4B,QAAA,gBACI9B,OAAA;MACIc,IAAI,EAAC,MAAM;MACXiB,MAAM,EAAC,iBAAiB;MACxBC,GAAG,EAAEzB,YAAa;MAClB0B,KAAK,EAAE;QAAEC,OAAO,EAAE;MAAO,CAAE;MAC3BC,QAAQ,EAAE1B;IAAiB;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC,eACFvC,OAAA,CAACJ,MAAM;MACH4C,OAAO,EAAC,WAAW;MACnBC,OAAO,EAAEd,iBAAkB;MAC3BqB,KAAK,EAAC,SAAS;MAAAlB,QAAA,gBAEf9B,OAAA;QACIiC,KAAK,EAAE;UACHsB,WAAW,EAAE;QACjB,CAAE;QAAAzB,QAAA,EACL;MAED;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACPvC,OAAA,CAACH,WAAW;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC;EAAA,eACX,CAAC;AAEX,CAAC;AAACc,GAAA,CAzDWF,YAAY;AAAAK,GAAA,GAAZL,YAAY;AA2DzB,eAAehD,aAAa;AAAC,IAAA+C,EAAA,EAAAM,GAAA;AAAAC,YAAA,CAAAP,EAAA;AAAAO,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}