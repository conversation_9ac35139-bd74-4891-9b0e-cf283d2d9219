{"ast": null, "code": "import{Navigate,useRoutes}from\"react-router-dom\";import DashboardLayout from\"./layouts/dashboard\";import LoginPage from\"./pages/LoginPage\";import SingUpPage from\"./pages/SignUpPage\";import Page404 from\"./pages/Page404\";import AccessDenied from\"./pages/AccessDenied\";import VerifyMail from\"./pages/VerifyMailPage\";import VerifiyingMail from\"./pages/VerifiyingMailPage\";import VerifyPasswordChangingPage from\"./pages/VerifyPasswordChangingPage\";import ForgotPasswordEmail from\"./pages/ForgotPasswordEmail\";// import ProductsPage from \"./pages/ProductsPage\";\nimport AnalyticsPage from\"./pages/AnalyticsPage\";import Prof from\"./pages/profileUser\";import{checkAuthToken}from\"./AuthenticationData.ts\";import{BundlesPage}from\"./pages/BundlesPage\";import{SettingsPage}from\"./pages/SettingsPage\";import{RatingPage}from\"./pages/RatingPage\";import{Track}from\"./pages/TrackPage\";import AppManageCoupons from\"./sections/@dashboard/Coupons/AppManageCoupons\";import SearchResults from\"./pages/SearchResults\";import Profile from\"./pages/Profile\";import{jsx as _jsx}from\"react/jsx-runtime\";import{Fragment as _Fragment}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";export default function Router(){const isAuthenticated=checkAuthToken();const userCategory=localStorage.getItem(\"userCategory\");const isEmailSent=localStorage.getItem(\"isEmailSent\")===\"true\";const RouteGuarding=(category,allowedCategories)=>{if(isAuthenticated){if(!allowedCategories.includes(category)){return/*#__PURE__*/_jsx(Navigate,{to:\"/AccessDenied\"});}}else{return/*#__PURE__*/_jsx(Navigate,{to:\"/Login\"});}return null;};const EmailVerifGuard=()=>{if(isEmailSent!==true)return/*#__PURE__*/_jsx(Navigate,{to:\"/AccessDenied\"});return null;};const VerifyMailGuard=()=>{// Allow access to VerifyMail page if user is not authenticated (coming from login)\n// or if isEmailSent is true (coming from registration)\nif(isAuthenticated||isEmailSent===true){return null;}return/*#__PURE__*/_jsx(Navigate,{to:\"/Login\"});};const routes=useRoutes([{path:\"/\",element:isAuthenticated?/*#__PURE__*/_jsx(Navigate,{to:\"/admin/User\"}):/*#__PURE__*/_jsx(Navigate,{to:\"/Login\"})},{path:\"/admin\",element:/*#__PURE__*/_jsx(DashboardLayout,{}),children:[{element:/*#__PURE__*/_jsx(Navigate,{to:\"/admin/user\"})},{path:\"User\",element:/*#__PURE__*/_jsxs(_Fragment,{children:[RouteGuarding(userCategory,[\"Free\",\"Student\",\"Freelance\",\"Enterprise\"]),/*#__PURE__*/_jsx(Prof,{})]})},{path:\"Analytics\",element:/*#__PURE__*/_jsxs(_Fragment,{children:[RouteGuarding(userCategory,[\"Freelance\",\"Enterprise\"]),/*#__PURE__*/_jsx(AnalyticsPage,{})]})},{path:\"Rating\",element:/*#__PURE__*/_jsxs(_Fragment,{children:[RouteGuarding(userCategory,[\"Freelance\",\"Enterprise\"]),/*#__PURE__*/_jsx(RatingPage,{})]})},// {\n//     path: \"Products\",\n//     element: (\n//         <>\n//             {RouteGuarding(userCategory, [\n//                 \"Free\",\n//                 \"Student\",\n//                 \"Freelance\",\n//                 \"Enterprise\",\n//             ])}\n//             <ProductsPage />\n//         </>\n//     ),\n// },\n{path:\"Search\",element:/*#__PURE__*/_jsxs(_Fragment,{children:[RouteGuarding(userCategory,[\"Free\",\"Student\",\"Freelance\",\"Enterprise\"]),/*#__PURE__*/_jsx(SearchResults,{})]})},{path:\"Settings\",element:/*#__PURE__*/_jsxs(_Fragment,{children:[RouteGuarding(userCategory,[\"Free\",\"Student\",\"Freelance\",\"Enterprise\"]),/*#__PURE__*/_jsx(SettingsPage,{})]})},{path:\"Bundles\",element:/*#__PURE__*/_jsxs(_Fragment,{children:[RouteGuarding(userCategory,[\"Free\",\"Student\",\"Freelance\",\"Enterprise\"]),/*#__PURE__*/_jsx(BundlesPage,{})]})},{path:\"ManageCoupons\",element:/*#__PURE__*/_jsxs(_Fragment,{children:[RouteGuarding(userCategory,[\"Free\",\"Student\",\"Freelance\",\"Enterprise\"]),/*#__PURE__*/_jsx(AppManageCoupons,{})]})}]},{path:\"/Signup\",element:isAuthenticated?/*#__PURE__*/_jsx(Navigate,{to:\"/admin/User\"}):/*#__PURE__*/_jsx(SingUpPage,{})},{path:\"/Login\",element:isAuthenticated?/*#__PURE__*/_jsx(Navigate,{to:\"/admin/User\"}):/*#__PURE__*/_jsx(LoginPage,{})},{path:\"/Profile/:param\",element:/*#__PURE__*/_jsx(Profile,{})},{path:\"/Track/:param\",element:/*#__PURE__*/_jsxs(_Fragment,{children:[RouteGuarding(userCategory,[\"Enterprise\"]),/*#__PURE__*/_jsx(Track,{})]})},{path:\"404\",element:/*#__PURE__*/_jsx(Page404,{})},{path:\"AccessDenied\",element:/*#__PURE__*/_jsx(AccessDenied,{})},{path:\"/VerifyMail\",element:/*#__PURE__*/_jsxs(_Fragment,{children:[VerifyMailGuard(),/*#__PURE__*/_jsx(VerifyMail,{})]})},{path:\"/ForgotPasswordEmail\",element:isAuthenticated?/*#__PURE__*/_jsx(Navigate,{to:\"/admin/User\"}):/*#__PURE__*/_jsx(ForgotPasswordEmail,{})},{path:\"/VerifiyingMail/:token\",element:/*#__PURE__*/_jsxs(_Fragment,{children:[EmailVerifGuard(),/*#__PURE__*/_jsx(VerifiyingMail,{})]})},{path:\"/VerifyPasswordChanging/:token\",element:isAuthenticated?/*#__PURE__*/_jsx(Navigate,{to:\"/admin/User\"}):/*#__PURE__*/_jsx(VerifyPasswordChangingPage,{})},{path:\"*\",element:/*#__PURE__*/_jsx(Page404,{})}]);return routes;}", "map": {"version": 3, "names": ["Navigate", "useRoutes", "DashboardLayout", "LoginPage", "SingUpPage", "Page404", "AccessDenied", "VerifyMail", "VerifiyingMail", "VerifyPasswordChangingPage", "ForgotPasswordEmail", "AnalyticsPage", "Prof", "checkAuthToken", "BundlesPage", "SettingsPage", "RatingPage", "Track", "AppManageCoupons", "SearchResults", "Profile", "jsx", "_jsx", "Fragment", "_Fragment", "jsxs", "_jsxs", "Router", "isAuthenticated", "userCategory", "localStorage", "getItem", "isEmailSent", "RouteGuarding", "category", "allowedCategories", "includes", "to", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "VerifyMailGuard", "routes", "path", "element", "children"], "sources": ["C:/Users/<USER>/Desktop/iDigitalX/ClientApp/src/routes.js"], "sourcesContent": ["import { Navigate, useRoutes } from \"react-router-dom\";\r\nimport DashboardLayout from \"./layouts/dashboard\";\r\nimport LoginPage from \"./pages/LoginPage\";\r\nimport SingUpPage from \"./pages/SignUpPage\";\r\nimport Page404 from \"./pages/Page404\";\r\nimport AccessDenied from \"./pages/AccessDenied\";\r\nimport VerifyMail from \"./pages/VerifyMailPage\";\r\nimport VerifiyingMail from \"./pages/VerifiyingMailPage\";\r\nimport VerifyPasswordChangingPage from \"./pages/VerifyPasswordChangingPage\";\r\nimport ForgotPasswordEmail from \"./pages/ForgotPasswordEmail\";\r\n// import ProductsPage from \"./pages/ProductsPage\";\r\nimport AnalyticsPage from \"./pages/AnalyticsPage\";\r\nimport Prof from \"./pages/profileUser\";\r\nimport { checkAuthToken } from \"./AuthenticationData.ts\";\r\nimport { BundlesPage } from \"./pages/BundlesPage\";\r\nimport { SettingsPage } from \"./pages/SettingsPage\";\r\nimport { RatingPage } from \"./pages/RatingPage\";\r\nimport { Track } from \"./pages/TrackPage\";\r\nimport AppManageCoupons from \"./sections/@dashboard/Coupons/AppManageCoupons\";\r\nimport SearchResults from \"./pages/SearchResults\";\r\nimport Profile from \"./pages/Profile\";\r\n\r\nexport default function Router() {\r\n  const isAuthenticated = checkAuthToken();\r\n  const userCategory = localStorage.getItem(\"userCategory\");\r\n  const isEmailSent = localStorage.getItem(\"isEmailSent\") === \"true\";\r\n\r\n  const RouteGuarding = (category, allowedCategories) => {\r\n    if (isAuthenticated) {\r\n      if (!allowedCategories.includes(category)) {\r\n        return <Navigate to=\"/AccessDenied\" />;\r\n      }\r\n    } else {\r\n      return <Navigate to=\"/Login\" />;\r\n    }\r\n    return null;\r\n  };\r\n\r\n  const EmailVerifGuard = () => {\r\n    if (isEmailSent !== true) return <Navigate to=\"/AccessDenied\" />;\r\n    return null;\r\n  };\r\n\r\n  const VerifyMailGuard = () => {\r\n    // Allow access to VerifyMail page if user is not authenticated (coming from login)\r\n    // or if isEmailSent is true (coming from registration)\r\n    if (isAuthenticated || isEmailSent === true) {\r\n      return null;\r\n    }\r\n    return <Navigate to=\"/Login\" />;\r\n  };\r\n\r\n  const routes = useRoutes([\r\n    {\r\n      path: \"/\",\r\n      element: isAuthenticated ? (\r\n        <Navigate to=\"/admin/User\" />\r\n      ) : (\r\n        <Navigate to=\"/Login\" />\r\n      ),\r\n    },\r\n    {\r\n      path: \"/admin\",\r\n      element: <DashboardLayout />,\r\n      children: [\r\n        { element: <Navigate to=\"/admin/user\" /> },\r\n        {\r\n          path: \"User\",\r\n          element: (\r\n            <>\r\n              {RouteGuarding(userCategory, [\r\n                \"Free\",\r\n                \"Student\",\r\n                \"Freelance\",\r\n                \"Enterprise\",\r\n              ])}\r\n              <Prof />\r\n            </>\r\n          ),\r\n        },\r\n        {\r\n          path: \"Analytics\",\r\n          element: (\r\n            <>\r\n              {RouteGuarding(userCategory, [\"Freelance\", \"Enterprise\"])}\r\n              <AnalyticsPage />\r\n            </>\r\n          ),\r\n        },\r\n        {\r\n          path: \"Rating\",\r\n          element: (\r\n            <>\r\n              {RouteGuarding(userCategory, [\"Freelance\", \"Enterprise\"])}\r\n              <RatingPage />\r\n            </>\r\n          ),\r\n        },\r\n        // {\r\n        //     path: \"Products\",\r\n        //     element: (\r\n        //         <>\r\n        //             {RouteGuarding(userCategory, [\r\n        //                 \"Free\",\r\n        //                 \"Student\",\r\n        //                 \"Freelance\",\r\n        //                 \"Enterprise\",\r\n        //             ])}\r\n        //             <ProductsPage />\r\n        //         </>\r\n        //     ),\r\n        // },\r\n        {\r\n          path: \"Search\",\r\n          element: (\r\n            <>\r\n              {RouteGuarding(userCategory, [\r\n                \"Free\",\r\n                \"Student\",\r\n                \"Freelance\",\r\n                \"Enterprise\",\r\n              ])}\r\n              <SearchResults />\r\n            </>\r\n          ),\r\n        },\r\n        {\r\n          path: \"Settings\",\r\n          element: (\r\n            <>\r\n              {RouteGuarding(userCategory, [\r\n                \"Free\",\r\n                \"Student\",\r\n                \"Freelance\",\r\n                \"Enterprise\",\r\n              ])}\r\n              <SettingsPage />\r\n            </>\r\n          ),\r\n        },\r\n        {\r\n          path: \"Bundles\",\r\n          element: (\r\n            <>\r\n              {RouteGuarding(userCategory, [\r\n                \"Free\",\r\n                \"Student\",\r\n                \"Freelance\",\r\n                \"Enterprise\",\r\n              ])}\r\n              <BundlesPage />\r\n            </>\r\n          ),\r\n        },\r\n        {\r\n          path: \"ManageCoupons\",\r\n          element: (\r\n            <>\r\n              {RouteGuarding(userCategory, [\r\n                \"Free\",\r\n                \"Student\",\r\n                \"Freelance\",\r\n                \"Enterprise\",\r\n              ])}\r\n              <AppManageCoupons />\r\n            </>\r\n          ),\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      path: \"/Signup\",\r\n      element: isAuthenticated ? <Navigate to=\"/admin/User\" /> : <SingUpPage />,\r\n    },\r\n    {\r\n      path: \"/Login\",\r\n      element: isAuthenticated ? <Navigate to=\"/admin/User\" /> : <LoginPage />,\r\n    },\r\n    { path: \"/Profile/:param\", element: <Profile /> },\r\n    {\r\n      path: \"/Track/:param\",\r\n      element: (\r\n        <>\r\n          {RouteGuarding(userCategory, [\"Enterprise\"])}\r\n          <Track />\r\n        </>\r\n      ),\r\n    },\r\n\r\n    { path: \"404\", element: <Page404 /> },\r\n    { path: \"AccessDenied\", element: <AccessDenied /> },\r\n    {\r\n      path: \"/VerifyMail\",\r\n      element: (\r\n        <>\r\n          {VerifyMailGuard()}\r\n          <VerifyMail />\r\n        </>\r\n      ),\r\n    },\r\n    {\r\n      path: \"/ForgotPasswordEmail\",\r\n      element: isAuthenticated ? (\r\n        <Navigate to=\"/admin/User\" />\r\n      ) : (\r\n        <ForgotPasswordEmail />\r\n      ),\r\n    },\r\n    {\r\n      path: \"/VerifiyingMail/:token\",\r\n      element: (\r\n        <>\r\n          {EmailVerifGuard()}\r\n          <VerifiyingMail />\r\n        </>\r\n      ),\r\n    },\r\n    {\r\n      path: \"/VerifyPasswordChanging/:token\",\r\n      element: isAuthenticated ? (\r\n        <Navigate to=\"/admin/User\" />\r\n      ) : (\r\n        <VerifyPasswordChangingPage />\r\n      ),\r\n    },\r\n    { path: \"*\", element: <Page404 /> },\r\n  ]);\r\n\r\n  return routes;\r\n}\r\n"], "mappings": "AAAA,OAASA,QAAQ,CAAEC,SAAS,KAAQ,kBAAkB,CACtD,MAAO,CAAAC,eAAe,KAAM,qBAAqB,CACjD,MAAO,CAAAC,SAAS,KAAM,mBAAmB,CACzC,MAAO,CAAAC,UAAU,KAAM,oBAAoB,CAC3C,MAAO,CAAAC,OAAO,KAAM,iBAAiB,CACrC,MAAO,CAAAC,YAAY,KAAM,sBAAsB,CAC/C,MAAO,CAAAC,UAAU,KAAM,wBAAwB,CAC/C,MAAO,CAAAC,cAAc,KAAM,4BAA4B,CACvD,MAAO,CAAAC,0BAA0B,KAAM,oCAAoC,CAC3E,MAAO,CAAAC,mBAAmB,KAAM,6BAA6B,CAC7D;AACA,MAAO,CAAAC,aAAa,KAAM,uBAAuB,CACjD,MAAO,CAAAC,IAAI,KAAM,qBAAqB,CACtC,OAASC,cAAc,KAAQ,yBAAyB,CACxD,OAASC,WAAW,KAAQ,qBAAqB,CACjD,OAASC,YAAY,KAAQ,sBAAsB,CACnD,OAASC,UAAU,KAAQ,oBAAoB,CAC/C,OAASC,KAAK,KAAQ,mBAAmB,CACzC,MAAO,CAAAC,gBAAgB,KAAM,gDAAgD,CAC7E,MAAO,CAAAC,aAAa,KAAM,uBAAuB,CACjD,MAAO,CAAAC,OAAO,KAAM,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,gCAAAC,QAAA,IAAAC,SAAA,gCAAAC,IAAA,IAAAC,KAAA,yBAEtC,cAAe,SAAS,CAAAC,MAAMA,CAAA,CAAG,CAC/B,KAAM,CAAAC,eAAe,CAAGf,cAAc,CAAC,CAAC,CACxC,KAAM,CAAAgB,YAAY,CAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,CACzD,KAAM,CAAAC,WAAW,CAAGF,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC,GAAK,MAAM,CAElE,KAAM,CAAAE,aAAa,CAAGA,CAACC,QAAQ,CAAEC,iBAAiB,GAAK,CACrD,GAAIP,eAAe,CAAE,CACnB,GAAI,CAACO,iBAAiB,CAACC,QAAQ,CAACF,QAAQ,CAAC,CAAE,CACzC,mBAAOZ,IAAA,CAACtB,QAAQ,EAACqC,EAAE,CAAC,eAAe,CAAE,CAAC,CACxC,CACF,CAAC,IAAM,CACL,mBAAOf,IAAA,CAACtB,QAAQ,EAACqC,EAAE,CAAC,QAAQ,CAAE,CAAC,CACjC,CACA,MAAO,KAAI,CACb,CAAC,CAED,KAAM,CAAAC,eAAe,CAAGA,CAAA,GAAM,CAC5B,GAAIN,WAAW,GAAK,IAAI,CAAE,mBAAOV,IAAA,CAACtB,QAAQ,EAACqC,EAAE,CAAC,eAAe,CAAE,CAAC,CAChE,MAAO,KAAI,CACb,CAAC,CAED,KAAM,CAAAE,eAAe,CAAGA,CAAA,GAAM,CAC5B;AACA;AACA,GAAIX,eAAe,EAAII,WAAW,GAAK,IAAI,CAAE,CAC3C,MAAO,KAAI,CACb,CACA,mBAAOV,IAAA,CAACtB,QAAQ,EAACqC,EAAE,CAAC,QAAQ,CAAE,CAAC,CACjC,CAAC,CAED,KAAM,CAAAG,MAAM,CAAGvC,SAAS,CAAC,CACvB,CACEwC,IAAI,CAAE,GAAG,CACTC,OAAO,CAAEd,eAAe,cACtBN,IAAA,CAACtB,QAAQ,EAACqC,EAAE,CAAC,aAAa,CAAE,CAAC,cAE7Bf,IAAA,CAACtB,QAAQ,EAACqC,EAAE,CAAC,QAAQ,CAAE,CAE3B,CAAC,CACD,CACEI,IAAI,CAAE,QAAQ,CACdC,OAAO,cAAEpB,IAAA,CAACpB,eAAe,GAAE,CAAC,CAC5ByC,QAAQ,CAAE,CACR,CAAED,OAAO,cAAEpB,IAAA,CAACtB,QAAQ,EAACqC,EAAE,CAAC,aAAa,CAAE,CAAE,CAAC,CAC1C,CACEI,IAAI,CAAE,MAAM,CACZC,OAAO,cACLhB,KAAA,CAAAF,SAAA,EAAAmB,QAAA,EACGV,aAAa,CAACJ,YAAY,CAAE,CAC3B,MAAM,CACN,SAAS,CACT,WAAW,CACX,YAAY,CACb,CAAC,cACFP,IAAA,CAACV,IAAI,GAAE,CAAC,EACR,CAEN,CAAC,CACD,CACE6B,IAAI,CAAE,WAAW,CACjBC,OAAO,cACLhB,KAAA,CAAAF,SAAA,EAAAmB,QAAA,EACGV,aAAa,CAACJ,YAAY,CAAE,CAAC,WAAW,CAAE,YAAY,CAAC,CAAC,cACzDP,IAAA,CAACX,aAAa,GAAE,CAAC,EACjB,CAEN,CAAC,CACD,CACE8B,IAAI,CAAE,QAAQ,CACdC,OAAO,cACLhB,KAAA,CAAAF,SAAA,EAAAmB,QAAA,EACGV,aAAa,CAACJ,YAAY,CAAE,CAAC,WAAW,CAAE,YAAY,CAAC,CAAC,cACzDP,IAAA,CAACN,UAAU,GAAE,CAAC,EACd,CAEN,CAAC,CACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CACEyB,IAAI,CAAE,QAAQ,CACdC,OAAO,cACLhB,KAAA,CAAAF,SAAA,EAAAmB,QAAA,EACGV,aAAa,CAACJ,YAAY,CAAE,CAC3B,MAAM,CACN,SAAS,CACT,WAAW,CACX,YAAY,CACb,CAAC,cACFP,IAAA,CAACH,aAAa,GAAE,CAAC,EACjB,CAEN,CAAC,CACD,CACEsB,IAAI,CAAE,UAAU,CAChBC,OAAO,cACLhB,KAAA,CAAAF,SAAA,EAAAmB,QAAA,EACGV,aAAa,CAACJ,YAAY,CAAE,CAC3B,MAAM,CACN,SAAS,CACT,WAAW,CACX,YAAY,CACb,CAAC,cACFP,IAAA,CAACP,YAAY,GAAE,CAAC,EAChB,CAEN,CAAC,CACD,CACE0B,IAAI,CAAE,SAAS,CACfC,OAAO,cACLhB,KAAA,CAAAF,SAAA,EAAAmB,QAAA,EACGV,aAAa,CAACJ,YAAY,CAAE,CAC3B,MAAM,CACN,SAAS,CACT,WAAW,CACX,YAAY,CACb,CAAC,cACFP,IAAA,CAACR,WAAW,GAAE,CAAC,EACf,CAEN,CAAC,CACD,CACE2B,IAAI,CAAE,eAAe,CACrBC,OAAO,cACLhB,KAAA,CAAAF,SAAA,EAAAmB,QAAA,EACGV,aAAa,CAACJ,YAAY,CAAE,CAC3B,MAAM,CACN,SAAS,CACT,WAAW,CACX,YAAY,CACb,CAAC,cACFP,IAAA,CAACJ,gBAAgB,GAAE,CAAC,EACpB,CAEN,CAAC,CAEL,CAAC,CACD,CACEuB,IAAI,CAAE,SAAS,CACfC,OAAO,CAAEd,eAAe,cAAGN,IAAA,CAACtB,QAAQ,EAACqC,EAAE,CAAC,aAAa,CAAE,CAAC,cAAGf,IAAA,CAAClB,UAAU,GAAE,CAC1E,CAAC,CACD,CACEqC,IAAI,CAAE,QAAQ,CACdC,OAAO,CAAEd,eAAe,cAAGN,IAAA,CAACtB,QAAQ,EAACqC,EAAE,CAAC,aAAa,CAAE,CAAC,cAAGf,IAAA,CAACnB,SAAS,GAAE,CACzE,CAAC,CACD,CAAEsC,IAAI,CAAE,iBAAiB,CAAEC,OAAO,cAAEpB,IAAA,CAACF,OAAO,GAAE,CAAE,CAAC,CACjD,CACEqB,IAAI,CAAE,eAAe,CACrBC,OAAO,cACLhB,KAAA,CAAAF,SAAA,EAAAmB,QAAA,EACGV,aAAa,CAACJ,YAAY,CAAE,CAAC,YAAY,CAAC,CAAC,cAC5CP,IAAA,CAACL,KAAK,GAAE,CAAC,EACT,CAEN,CAAC,CAED,CAAEwB,IAAI,CAAE,KAAK,CAAEC,OAAO,cAAEpB,IAAA,CAACjB,OAAO,GAAE,CAAE,CAAC,CACrC,CAAEoC,IAAI,CAAE,cAAc,CAAEC,OAAO,cAAEpB,IAAA,CAAChB,YAAY,GAAE,CAAE,CAAC,CACnD,CACEmC,IAAI,CAAE,aAAa,CACnBC,OAAO,cACLhB,KAAA,CAAAF,SAAA,EAAAmB,QAAA,EACGJ,eAAe,CAAC,CAAC,cAClBjB,IAAA,CAACf,UAAU,GAAE,CAAC,EACd,CAEN,CAAC,CACD,CACEkC,IAAI,CAAE,sBAAsB,CAC5BC,OAAO,CAAEd,eAAe,cACtBN,IAAA,CAACtB,QAAQ,EAACqC,EAAE,CAAC,aAAa,CAAE,CAAC,cAE7Bf,IAAA,CAACZ,mBAAmB,GAAE,CAE1B,CAAC,CACD,CACE+B,IAAI,CAAE,wBAAwB,CAC9BC,OAAO,cACLhB,KAAA,CAAAF,SAAA,EAAAmB,QAAA,EACGL,eAAe,CAAC,CAAC,cAClBhB,IAAA,CAACd,cAAc,GAAE,CAAC,EAClB,CAEN,CAAC,CACD,CACEiC,IAAI,CAAE,gCAAgC,CACtCC,OAAO,CAAEd,eAAe,cACtBN,IAAA,CAACtB,QAAQ,EAACqC,EAAE,CAAC,aAAa,CAAE,CAAC,cAE7Bf,IAAA,CAACb,0BAA0B,GAAE,CAEjC,CAAC,CACD,CAAEgC,IAAI,CAAE,GAAG,CAAEC,OAAO,cAAEpB,IAAA,CAACjB,OAAO,GAAE,CAAE,CAAC,CACpC,CAAC,CAEF,MAAO,CAAAmC,MAAM,CACf", "ignoreList": []}, "metadata": {}, "sourceType": "module"}