{"ast": null, "code": "import{useRef}from\"react\";import{<PERSON><PERSON>,CircularProgress}from\"@mui/material\";import PostAddIcon from\"@mui/icons-material/PostAdd\";import{toast}from\"react-toastify\";import{jsx as _jsx}from\"react/jsx-runtime\";import{Fragment as _Fragment}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";const PhotoSelector=_ref=>{let{onSelect}=_ref;const fileInputRef=useRef(null);const maxSize=1024*1024;const handleFileChange=event=>{const file=event.target.files[0];if(file){// Validate file type\nif(!file.type.startsWith(\"image/\")){toast.error(\"Please select a valid image file\",{position:\"top-center\",autoClose:3000});return;}// Validate file size\nif(file.size>maxSize){toast.error(\"File size exceeds the 1MB limit. Please select a smaller file.\",{position:\"top-center\",autoClose:3000});return;}const reader=new FileReader();reader.onload=()=>{try{onSelect(reader.result);toast.success(\"Image uploaded successfully\",{position:\"top-center\",autoClose:1000});}catch(error){toast.error(\"Error processing image\",{position:\"top-center\",autoClose:3000});}};reader.onerror=()=>{toast.error(\"Error reading file\",{position:\"top-center\",autoClose:3000});};reader.readAsDataURL(file);}};const handleButtonClick=()=>{fileInputRef.current.click();};return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"input\",{type:\"file\",accept:\"image/*\",ref:fileInputRef,style:{display:\"none\"},onChange:handleFileChange}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",onClick:handleButtonClick,style:{position:\"absolute\",width:\"0px\",height:\"0px\",border:\"none\",padding:\"0\",fontSize:\"20px\",backgroundColor:\"transparent\",color:\"#ff715b\"},children:/*#__PURE__*/_jsx(\"i\",{className:\"bi bi-plus-circle-fill\"})})]});};export const FileSelector=_ref2=>{let{onSelect,isLoading=false}=_ref2;const fileInputRef=useRef(null);const maxSize=1024*1024;const handleFileChange=event=>{const file=event.target.files[0];if(file){if(file.type!=\"application/pdf\"){alert(\"Invalid file type. Only PDF are allowed.\");return;}if(file.size>maxSize){alert(\"File size exceeds the 1MB limit. Please select a smaller file.\");return;}const reader=new FileReader();reader.onload=()=>{onSelect(reader.result);};reader.readAsDataURL(file);}};const handleButtonClick=()=>{fileInputRef.current.click();};return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"input\",{type:\"file\",accept:\"application/pdf\",ref:fileInputRef,style:{display:\"none\"},onChange:handleFileChange}),/*#__PURE__*/_jsxs(Button,{variant:\"contained\",onClick:handleButtonClick,color:\"primary\",disabled:isLoading,children:[/*#__PURE__*/_jsx(\"span\",{style:{marginRight:\"10px\"},children:isLoading?\"Uploading...\":\"Upload\"}),isLoading?/*#__PURE__*/_jsx(CircularProgress,{size:20,color:\"inherit\"}):/*#__PURE__*/_jsx(PostAddIcon,{})]})]});};export default PhotoSelector;", "map": {"version": 3, "names": ["useRef", "<PERSON><PERSON>", "CircularProgress", "PostAddIcon", "toast", "jsx", "_jsx", "Fragment", "_Fragment", "jsxs", "_jsxs", "PhotoSelector", "_ref", "onSelect", "fileInputRef", "maxSize", "handleFileChange", "event", "file", "target", "files", "type", "startsWith", "error", "position", "autoClose", "size", "reader", "FileReader", "onload", "result", "success", "onerror", "readAsDataURL", "handleButtonClick", "current", "click", "children", "accept", "ref", "style", "display", "onChange", "variant", "onClick", "width", "height", "border", "padding", "fontSize", "backgroundColor", "color", "className", "FileSelector", "_ref2", "isLoading", "alert", "disabled", "marginRight"], "sources": ["C:/Users/<USER>/Desktop/iDigitalX/ClientApp/src/sections/auth/signup/PhotoSelector.js"], "sourcesContent": ["import { useRef } from \"react\";\r\nimport { <PERSON><PERSON>, CircularProgress } from \"@mui/material\";\r\nimport PostAddIcon from \"@mui/icons-material/PostAdd\";\r\nimport { toast } from \"react-toastify\";\r\n\r\nconst PhotoSelector = ({ onSelect }) => {\r\n  const fileInputRef = useRef(null);\r\n  const maxSize = 1024 * 1024;\r\n\r\n  const handleFileChange = (event) => {\r\n    const file = event.target.files[0];\r\n\r\n    if (file) {\r\n      // Validate file type\r\n      if (!file.type.startsWith(\"image/\")) {\r\n        toast.error(\"Please select a valid image file\", {\r\n          position: \"top-center\",\r\n          autoClose: 3000,\r\n        });\r\n        return;\r\n      }\r\n\r\n      // Validate file size\r\n      if (file.size > maxSize) {\r\n        toast.error(\r\n          \"File size exceeds the 1MB limit. Please select a smaller file.\",\r\n          {\r\n            position: \"top-center\",\r\n            autoClose: 3000,\r\n          }\r\n        );\r\n        return;\r\n      }\r\n\r\n      const reader = new FileReader();\r\n      reader.onload = () => {\r\n        try {\r\n          onSelect(reader.result);\r\n          toast.success(\"Image uploaded successfully\", {\r\n            position: \"top-center\",\r\n            autoClose: 1000,\r\n          });\r\n        } catch (error) {\r\n          toast.error(\"Error processing image\", {\r\n            position: \"top-center\",\r\n            autoClose: 3000,\r\n          });\r\n        }\r\n      };\r\n      reader.onerror = () => {\r\n        toast.error(\"Error reading file\", {\r\n          position: \"top-center\",\r\n          autoClose: 3000,\r\n        });\r\n      };\r\n      reader.readAsDataURL(file);\r\n    }\r\n  };\r\n\r\n  const handleButtonClick = () => {\r\n    fileInputRef.current.click();\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <input\r\n        type=\"file\"\r\n        accept=\"image/*\"\r\n        ref={fileInputRef}\r\n        style={{ display: \"none\" }}\r\n        onChange={handleFileChange}\r\n      />\r\n      <Button\r\n        variant=\"contained\"\r\n        onClick={handleButtonClick}\r\n        style={{\r\n          position: \"absolute\",\r\n          width: \"0px\",\r\n          height: \"0px\",\r\n          border: \"none\",\r\n          padding: \"0\",\r\n          fontSize: \"20px\",\r\n          backgroundColor: \"transparent\",\r\n          color: \"#ff715b\",\r\n        }}\r\n      >\r\n        <i className=\"bi bi-plus-circle-fill\"></i>\r\n      </Button>\r\n    </>\r\n  );\r\n};\r\n\r\nexport const FileSelector = ({ onSelect, isLoading = false }) => {\r\n  const fileInputRef = useRef(null);\r\n  const maxSize = 1024 * 1024;\r\n\r\n  const handleFileChange = (event) => {\r\n    const file = event.target.files[0];\r\n\r\n    if (file) {\r\n      if (file.type != \"application/pdf\") {\r\n        alert(\"Invalid file type. Only PDF are allowed.\");\r\n        return;\r\n      }\r\n\r\n      if (file.size > maxSize) {\r\n        alert(\"File size exceeds the 1MB limit. Please select a smaller file.\");\r\n        return;\r\n      }\r\n\r\n      const reader = new FileReader();\r\n      reader.onload = () => {\r\n        onSelect(reader.result);\r\n      };\r\n      reader.readAsDataURL(file);\r\n    }\r\n  };\r\n\r\n  const handleButtonClick = () => {\r\n    fileInputRef.current.click();\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <input\r\n        type=\"file\"\r\n        accept=\"application/pdf\"\r\n        ref={fileInputRef}\r\n        style={{ display: \"none\" }}\r\n        onChange={handleFileChange}\r\n      />\r\n      <Button\r\n        variant=\"contained\"\r\n        onClick={handleButtonClick}\r\n        color=\"primary\"\r\n        disabled={isLoading}\r\n      >\r\n        <span\r\n          style={{\r\n            marginRight: \"10px\",\r\n          }}\r\n        >\r\n          {isLoading ? \"Uploading...\" : \"Upload\"}\r\n        </span>\r\n        {isLoading ? (\r\n          <CircularProgress size={20} color=\"inherit\" />\r\n        ) : (\r\n          <PostAddIcon />\r\n        )}\r\n      </Button>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default PhotoSelector;\r\n"], "mappings": "AAAA,OAASA,MAAM,KAAQ,OAAO,CAC9B,OAASC,MAAM,CAAEC,gBAAgB,KAAQ,eAAe,CACxD,MAAO,CAAAC,WAAW,KAAM,6BAA6B,CACrD,OAASC,KAAK,KAAQ,gBAAgB,CAAC,OAAAC,GAAA,IAAAC,IAAA,gCAAAC,QAAA,IAAAC,SAAA,gCAAAC,IAAA,IAAAC,KAAA,yBAEvC,KAAM,CAAAC,aAAa,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACjC,KAAM,CAAAE,YAAY,CAAGd,MAAM,CAAC,IAAI,CAAC,CACjC,KAAM,CAAAe,OAAO,CAAG,IAAI,CAAG,IAAI,CAE3B,KAAM,CAAAC,gBAAgB,CAAIC,KAAK,EAAK,CAClC,KAAM,CAAAC,IAAI,CAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAElC,GAAIF,IAAI,CAAE,CACR;AACA,GAAI,CAACA,IAAI,CAACG,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,CAAE,CACnClB,KAAK,CAACmB,KAAK,CAAC,kCAAkC,CAAE,CAC9CC,QAAQ,CAAE,YAAY,CACtBC,SAAS,CAAE,IACb,CAAC,CAAC,CACF,OACF,CAEA;AACA,GAAIP,IAAI,CAACQ,IAAI,CAAGX,OAAO,CAAE,CACvBX,KAAK,CAACmB,KAAK,CACT,gEAAgE,CAChE,CACEC,QAAQ,CAAE,YAAY,CACtBC,SAAS,CAAE,IACb,CACF,CAAC,CACD,OACF,CAEA,KAAM,CAAAE,MAAM,CAAG,GAAI,CAAAC,UAAU,CAAC,CAAC,CAC/BD,MAAM,CAACE,MAAM,CAAG,IAAM,CACpB,GAAI,CACFhB,QAAQ,CAACc,MAAM,CAACG,MAAM,CAAC,CACvB1B,KAAK,CAAC2B,OAAO,CAAC,6BAA6B,CAAE,CAC3CP,QAAQ,CAAE,YAAY,CACtBC,SAAS,CAAE,IACb,CAAC,CAAC,CACJ,CAAE,MAAOF,KAAK,CAAE,CACdnB,KAAK,CAACmB,KAAK,CAAC,wBAAwB,CAAE,CACpCC,QAAQ,CAAE,YAAY,CACtBC,SAAS,CAAE,IACb,CAAC,CAAC,CACJ,CACF,CAAC,CACDE,MAAM,CAACK,OAAO,CAAG,IAAM,CACrB5B,KAAK,CAACmB,KAAK,CAAC,oBAAoB,CAAE,CAChCC,QAAQ,CAAE,YAAY,CACtBC,SAAS,CAAE,IACb,CAAC,CAAC,CACJ,CAAC,CACDE,MAAM,CAACM,aAAa,CAACf,IAAI,CAAC,CAC5B,CACF,CAAC,CAED,KAAM,CAAAgB,iBAAiB,CAAGA,CAAA,GAAM,CAC9BpB,YAAY,CAACqB,OAAO,CAACC,KAAK,CAAC,CAAC,CAC9B,CAAC,CAED,mBACE1B,KAAA,CAAAF,SAAA,EAAA6B,QAAA,eACE/B,IAAA,UACEe,IAAI,CAAC,MAAM,CACXiB,MAAM,CAAC,SAAS,CAChBC,GAAG,CAAEzB,YAAa,CAClB0B,KAAK,CAAE,CAAEC,OAAO,CAAE,MAAO,CAAE,CAC3BC,QAAQ,CAAE1B,gBAAiB,CAC5B,CAAC,cACFV,IAAA,CAACL,MAAM,EACL0C,OAAO,CAAC,WAAW,CACnBC,OAAO,CAAEV,iBAAkB,CAC3BM,KAAK,CAAE,CACLhB,QAAQ,CAAE,UAAU,CACpBqB,KAAK,CAAE,KAAK,CACZC,MAAM,CAAE,KAAK,CACbC,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,GAAG,CACZC,QAAQ,CAAE,MAAM,CAChBC,eAAe,CAAE,aAAa,CAC9BC,KAAK,CAAE,SACT,CAAE,CAAAd,QAAA,cAEF/B,IAAA,MAAG8C,SAAS,CAAC,wBAAwB,CAAI,CAAC,CACpC,CAAC,EACT,CAAC,CAEP,CAAC,CAED,MAAO,MAAM,CAAAC,YAAY,CAAGC,KAAA,EAAqC,IAApC,CAAEzC,QAAQ,CAAE0C,SAAS,CAAG,KAAM,CAAC,CAAAD,KAAA,CAC1D,KAAM,CAAAxC,YAAY,CAAGd,MAAM,CAAC,IAAI,CAAC,CACjC,KAAM,CAAAe,OAAO,CAAG,IAAI,CAAG,IAAI,CAE3B,KAAM,CAAAC,gBAAgB,CAAIC,KAAK,EAAK,CAClC,KAAM,CAAAC,IAAI,CAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAElC,GAAIF,IAAI,CAAE,CACR,GAAIA,IAAI,CAACG,IAAI,EAAI,iBAAiB,CAAE,CAClCmC,KAAK,CAAC,0CAA0C,CAAC,CACjD,OACF,CAEA,GAAItC,IAAI,CAACQ,IAAI,CAAGX,OAAO,CAAE,CACvByC,KAAK,CAAC,gEAAgE,CAAC,CACvE,OACF,CAEA,KAAM,CAAA7B,MAAM,CAAG,GAAI,CAAAC,UAAU,CAAC,CAAC,CAC/BD,MAAM,CAACE,MAAM,CAAG,IAAM,CACpBhB,QAAQ,CAACc,MAAM,CAACG,MAAM,CAAC,CACzB,CAAC,CACDH,MAAM,CAACM,aAAa,CAACf,IAAI,CAAC,CAC5B,CACF,CAAC,CAED,KAAM,CAAAgB,iBAAiB,CAAGA,CAAA,GAAM,CAC9BpB,YAAY,CAACqB,OAAO,CAACC,KAAK,CAAC,CAAC,CAC9B,CAAC,CAED,mBACE1B,KAAA,CAAAF,SAAA,EAAA6B,QAAA,eACE/B,IAAA,UACEe,IAAI,CAAC,MAAM,CACXiB,MAAM,CAAC,iBAAiB,CACxBC,GAAG,CAAEzB,YAAa,CAClB0B,KAAK,CAAE,CAAEC,OAAO,CAAE,MAAO,CAAE,CAC3BC,QAAQ,CAAE1B,gBAAiB,CAC5B,CAAC,cACFN,KAAA,CAACT,MAAM,EACL0C,OAAO,CAAC,WAAW,CACnBC,OAAO,CAAEV,iBAAkB,CAC3BiB,KAAK,CAAC,SAAS,CACfM,QAAQ,CAAEF,SAAU,CAAAlB,QAAA,eAEpB/B,IAAA,SACEkC,KAAK,CAAE,CACLkB,WAAW,CAAE,MACf,CAAE,CAAArB,QAAA,CAEDkB,SAAS,CAAG,cAAc,CAAG,QAAQ,CAClC,CAAC,CACNA,SAAS,cACRjD,IAAA,CAACJ,gBAAgB,EAACwB,IAAI,CAAE,EAAG,CAACyB,KAAK,CAAC,SAAS,CAAE,CAAC,cAE9C7C,IAAA,CAACH,WAAW,GAAE,CACf,EACK,CAAC,EACT,CAAC,CAEP,CAAC,CAED,cAAe,CAAAQ,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module"}