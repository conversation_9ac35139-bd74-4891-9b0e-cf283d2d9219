{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\iDigitalX\\\\ClientApp\\\\src\\\\App.js\";\nimport { <PERSON><PERSON>er<PERSON>outer } from \"react-router-dom\";\nimport { HelmetProvider } from \"react-helmet-async\";\n// routes\nimport \"react-toastify/dist/ReactToastify.css\";\nimport { ToastContainer } from \"react-toastify\";\nimport Router from \"./routes\";\n// theme\nimport ThemeProvider from \"./theme\";\n// components\nimport { StyledChart } from \"./components/chart\";\nimport ScrollToTop from \"./components/scroll-to-top\";\nimport { BudgetProvider } from \"./Context/BudgetContext\";\nimport { ProfileProvider } from \"./Context/ProfileContext\";\nimport { SearchProvider } from \"./Context/SearchContext\";\n\n// ----------------------------------------------------------------------\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function App() {\n  return /*#__PURE__*/_jsxDEV(ProfileProvider, {\n    children: /*#__PURE__*/_jsxDEV(BudgetProvider, {\n      children: /*#__PURE__*/_jsxDEV(SearchProvider, {\n        children: /*#__PURE__*/_jsxDEV(HelmetProvider, {\n          children: /*#__PURE__*/_jsxDEV(BrowserRouter, {\n            children: /*#__PURE__*/_jsxDEV(ThemeProvider, {\n              children: [/*#__PURE__*/_jsxDEV(ScrollToTop, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 26,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(StyledChart, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 27,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(Router, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 28,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(ToastContainer, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 29,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 9\n  }, this);\n}\n_c = App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ToastContainer", "Router", "ThemeProvider", "<PERSON><PERSON><PERSON><PERSON>", "ScrollToTop", "BudgetProvider", "ProfileProvider", "SearchProvider", "jsxDEV", "_jsxDEV", "App", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/iDigitalX/ClientApp/src/App.js"], "sourcesContent": ["import { <PERSON>rowser<PERSON>outer } from \"react-router-dom\";\r\nimport { He<PERSON>etProvider } from \"react-helmet-async\";\r\n// routes\r\nimport \"react-toastify/dist/ReactToastify.css\";\r\nimport { ToastContainer } from \"react-toastify\";\r\nimport Router from \"./routes\";\r\n// theme\r\nimport ThemeProvider from \"./theme\";\r\n// components\r\nimport { StyledChart } from \"./components/chart\";\r\nimport ScrollToTop from \"./components/scroll-to-top\";\r\nimport { BudgetProvider } from \"./Context/BudgetContext\";\r\nimport { ProfileProvider } from \"./Context/ProfileContext\";\r\nimport { SearchProvider } from \"./Context/SearchContext\";\r\n\r\n// ----------------------------------------------------------------------\r\n\r\nexport default function App() {\r\n    return (\r\n        <ProfileProvider>\r\n            <BudgetProvider>\r\n                <SearchProvider>\r\n                    <HelmetProvider>\r\n                        <BrowserRouter>\r\n                            <ThemeProvider>\r\n                                <ScrollToTop />\r\n                                <StyledChart />\r\n                                <Router />\r\n                                <ToastContainer />\r\n                            </ThemeProvider>\r\n                        </BrowserRouter>\r\n                    </HelmetProvider>\r\n                </SearchProvider>\r\n            </BudgetProvider>\r\n        </ProfileProvider>\r\n    );\r\n}\r\n"], "mappings": ";AAAA,SAASA,aAAa,QAAQ,kBAAkB;AAChD,SAASC,cAAc,QAAQ,oBAAoB;AACnD;AACA,OAAO,uCAAuC;AAC9C,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,OAAOC,MAAM,MAAM,UAAU;AAC7B;AACA,OAAOC,aAAa,MAAM,SAAS;AACnC;AACA,SAASC,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;;AAExD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,eAAe,SAASC,GAAGA,CAAA,EAAG;EAC1B,oBACID,OAAA,CAACH,eAAe;IAAAK,QAAA,eACZF,OAAA,CAACJ,cAAc;MAAAM,QAAA,eACXF,OAAA,CAACF,cAAc;QAAAI,QAAA,eACXF,OAAA,CAACV,cAAc;UAAAY,QAAA,eACXF,OAAA,CAACX,aAAa;YAAAa,QAAA,eACVF,OAAA,CAACP,aAAa;cAAAS,QAAA,gBACVF,OAAA,CAACL,WAAW;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACfN,OAAA,CAACN,WAAW;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACfN,OAAA,CAACR,MAAM;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACVN,OAAA,CAACT,cAAc;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAE1B;AAACC,EAAA,GAnBuBN,GAAG;AAAA,IAAAM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}