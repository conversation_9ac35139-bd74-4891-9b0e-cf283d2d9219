{"ast": null, "code": "import{useEffect,useState}from\"react\";import{<PERSON><PERSON>,<PERSON><PERSON>,<PERSON>,Box,Typography,Tabs,Tab,Container,Rating,Stack}from\"@mui/material\";import LocationOnIcon from\"@mui/icons-material/LocationOn\";import WorkOutlineIcon from\"@mui/icons-material/WorkOutline\";import StarBorderIcon from\"@mui/icons-material/StarBorder\";import Avatar from\"@mui/material/Avatar\";import{Link}from\"react-router-dom\";import{useSearch}from\"../Context/SearchContext\";import SearchNotFound from\"./SearchNotFound\";import{motion}from\"framer-motion\";import EmojiPeopleIcon from\"@mui/icons-material/EmojiPeople\";import EngineeringIcon from\"@mui/icons-material/Engineering\";import ApartmentIcon from\"@mui/icons-material/Apartment\";import BusinessCenterIcon from\"@mui/icons-material/BusinessCenter\";import{useSearchParams}from\"react-router-dom\";import{CircularProgress}from\"@mui/material\";// Helper component for result card\nimport{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";import{Fragment as _Fragment}from\"react/jsx-runtime\";const ResultCard=_ref=>{let{result,GetCategoryIcon}=_ref;return/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(motion.div,{className:\"animate-on-scroll\",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:0.5,ease:\"easeOut\"},children:/*#__PURE__*/_jsx(Link,{to:`/Profile/${result.userName}`,target:\"_blank\",rel:\"noreferrer\",style:{textDecoration:\"none\",color:\"inherit\"},children:/*#__PURE__*/_jsx(Card,{sx:{display:\"flex\",flexDirection:\"row\",alignItems:\"center\",p:3,gap:\"10px\",boxShadow:\"0px 4px 10px rgba(0, 0, 0, 0.1)\",transition:\"box-shadow 0.3s ease\",\"&:hover\":{boxShadow:\"0px 8px 20px rgba(0, 0, 0, 0.2)\"}},children:/*#__PURE__*/_jsxs(Stack,{direction:\"row\",alignItems:\"center\",spacing:2,sx:{flex:1},children:[/*#__PURE__*/_jsx(Avatar,{alt:result.user.firstName+\" \"+result.user.lastName,src:result.profilePicture,sx:{width:80,height:80}}),/*#__PURE__*/_jsxs(Stack,{direction:\"column\",spacing:1,children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",children:[result.user.firstName,\" \",result.user.lastName,\" \",/*#__PURE__*/_jsxs(Stack,{direction:\"row\",alignItems:\"baseline\",spacing:1,sx:{display:\"inline-flex\",marginLeft:1},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",sx:{color:\"rgba(20, 43, 58, 0.5)\"},children:[\"@\",result.userName]}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{color:\"rgba(20, 43, 58, 0.5)\"},children:GetCategoryIcon(result.user.category)})]})]}),result.user.rate>0&&/*#__PURE__*/_jsx(Rating,{name:\"read-only\",value:result.user.rate,readOnly:true,emptyIcon:/*#__PURE__*/_jsx(StarBorderIcon,{fontSize:\"inherit\"}),sx:{marginLeft:\"10px\",fontSize:\"13px\"}}),/*#__PURE__*/_jsx(Box,{sx:{display:\"flex\",alignItems:\"center\",gap:\"10px\"},children:result.country&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(LocationOnIcon,{sx:{fontSize:\"15px\"}}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",sx:{color:\"rgba(20, 43, 58, 0.5)\"},children:result.country})]})}),/*#__PURE__*/_jsx(Box,{sx:{display:\"flex\",alignItems:\"center\",gap:\"10px\"},children:result.occupation&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(WorkOutlineIcon,{sx:{fontSize:\"15px\"}}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",sx:{color:\"rgba(20, 43, 58, 0.5)\"},children:result.occupation})]})})]})]})})})})});};const SearchResults=()=>{const{searchResults,fetchSearchResults,loading}=useSearch();const[activeTab,setActiveTab]=useState(0);// Default to \"All\" tab\nconst[displayCount,setDisplayCount]=useState(5);const[sortedResults,setSortedResults]=useState([]);const[ratedResults,setRatedResults]=useState([]);const[normalResults,setNormalResults]=useState([]);const[searchParams]=useSearchParams();// Fetch search results only when search parameters change\nuseEffect(()=>{const handleSearch=async()=>{if(searchParams&&searchParams.toString()!==\"\"){await fetchSearchResults(searchParams.get(\"q\"));}};handleSearch();// Call search function only when searchParams change\n},[searchParams]);// Update sortedResults whenever searchResults or activeTab changes\nuseEffect(()=>{const{sorted,rated,normal}=sortResultsByTab(activeTab);setSortedResults(sorted);setRatedResults(rated);setNormalResults(normal);},[searchResults,activeTab]);const GetCategoryIcon=category=>{switch(category){case\"Free\":return/*#__PURE__*/_jsx(EmojiPeopleIcon,{fontSize:\"Large\"});case\"Student\":return/*#__PURE__*/_jsx(BusinessCenterIcon,{fontSize:\"Large\"});case\"Freelance\":return/*#__PURE__*/_jsx(EngineeringIcon,{fontSize:\"Large\"});case\"Entrepreneur\":return/*#__PURE__*/_jsx(ApartmentIcon,{fontSize:\"Large\"});default:return null;}};const handleTabChange=(event,newValue)=>{setActiveTab(newValue);setDisplayCount(5);// Reset display count when switching tabs\n};const sortResultsByTab=tabValue=>{const ratedAccounts=searchResults.filter(result=>result.user.rate>0).sort((a,b)=>b.user.rate-a.user.rate);const normalAccounts=searchResults.filter(result=>!result.user.rate||result.user.rate===0).sort((a,b)=>a.userName.localeCompare(b.userName));switch(tabValue){case 0:// \"All\" tab - show both sections\nreturn{sorted:[...ratedAccounts,...normalAccounts],rated:ratedAccounts,normal:normalAccounts};case 1:// \"Rated\" tab - show only rated accounts\nreturn{sorted:ratedAccounts,rated:ratedAccounts,normal:[]};default:return{sorted:searchResults,rated:ratedAccounts,normal:normalAccounts};}};return/*#__PURE__*/_jsxs(Container,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h5\",component:\"h1\",gutterBottom:true,children:\"Search\"}),/*#__PURE__*/_jsxs(Typography,{component:\"h2\",color:\"textSecondary\",children:[\"Results for \",searchParams.get(\"q\")]}),loading?/*#__PURE__*/_jsxs(Box,{sx:{display:\"flex\",justifyContent:\"center\",alignItems:\"center\",minHeight:\"300px\",flexDirection:\"column\",gap:2},children:[/*#__PURE__*/_jsx(CircularProgress,{size:60}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"textSecondary\",children:\"Searching...\"})]}):searchResults.length>0?/*#__PURE__*/_jsxs(Box,{sx:{mt:5},children:[/*#__PURE__*/_jsx(Box,{sx:{display:\"flex\",justifyContent:\"center\",gap:\"10px\",padding:\"10px\",marginBottom:\"30px\"},children:/*#__PURE__*/_jsxs(Tabs,{value:activeTab,onChange:handleTabChange,\"aria-label\":\"Account tabs\",children:[/*#__PURE__*/_jsx(Tab,{label:\"All\"}),/*#__PURE__*/_jsx(Tab,{label:\"Rated\"})]})}),activeTab===0?/*#__PURE__*/ // \"All\" tab - show organized sections\n_jsxs(Box,{children:[ratedResults.length>0&&/*#__PURE__*/_jsxs(Box,{sx:{mb:4},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h5\",component:\"h2\",sx:{mb:1,fontWeight:600,color:\"primary.main\"},children:\"Top Rated Accounts\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",sx:{mb:3},children:\"Accounts sorted by their ratings from highest to lowest\"}),/*#__PURE__*/_jsx(Grid,{container:true,spacing:3,children:ratedResults.slice(0,Math.min(displayCount,ratedResults.length)).map(result=>/*#__PURE__*/_jsx(ResultCard,{result:result,GetCategoryIcon:GetCategoryIcon},result.id))})]}),normalResults.length>0&&displayCount>ratedResults.length&&/*#__PURE__*/_jsxs(Box,{sx:{mb:4},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h5\",component:\"h2\",sx:{mb:1,fontWeight:600,color:\"primary.main\"},children:\"Other Accounts\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",sx:{mb:3},children:\"Accounts sorted alphabetically by username\"}),/*#__PURE__*/_jsx(Grid,{container:true,spacing:3,children:normalResults.slice(0,Math.max(0,displayCount-ratedResults.length)).map(result=>/*#__PURE__*/_jsx(ResultCard,{result:result,GetCategoryIcon:GetCategoryIcon},result.id))})]})]}):/*#__PURE__*/ // \"Rated\" tab - show only rated accounts\n_jsx(Grid,{container:true,spacing:3,children:sortedResults.slice(0,displayCount).map(result=>/*#__PURE__*/_jsx(ResultCard,{result:result,GetCategoryIcon:GetCategoryIcon},result.id))}),displayCount<sortedResults.length&&/*#__PURE__*/_jsx(Box,{sx:{display:\"flex\",justifyContent:\"center\",mt:3},children:/*#__PURE__*/_jsx(Button,{variant:\"outlined\",onClick:()=>setDisplayCount(displayCount+5),children:\"View More\"})})]}):/*#__PURE__*/_jsx(SearchNotFound,{searchQuery:searchParams.get(\"q\")})]});};export default SearchResults;", "map": {"version": 3, "names": ["useEffect", "useState", "Grid", "<PERSON><PERSON>", "Card", "Box", "Typography", "Tabs", "Tab", "Container", "Rating", "<PERSON><PERSON>", "LocationOnIcon", "WorkOutlineIcon", "StarBorderIcon", "Avatar", "Link", "useSearch", "SearchNotFound", "motion", "EmojiPeopleIcon", "EngineeringIcon", "ApartmentIcon", "BusinessCenterIcon", "useSearchParams", "CircularProgress", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "ResultCard", "_ref", "result", "GetCategoryIcon", "item", "xs", "children", "div", "className", "initial", "opacity", "y", "animate", "transition", "duration", "ease", "to", "userName", "target", "rel", "style", "textDecoration", "color", "sx", "display", "flexDirection", "alignItems", "p", "gap", "boxShadow", "direction", "spacing", "flex", "alt", "user", "firstName", "lastName", "src", "profilePicture", "width", "height", "variant", "marginLeft", "category", "rate", "name", "value", "readOnly", "emptyIcon", "fontSize", "country", "occupation", "SearchResults", "searchResults", "fetchSearchResults", "loading", "activeTab", "setActiveTab", "displayCount", "setDisplayCount", "sortedResults", "setSortedResults", "ratedResults", "setRatedResults", "normalResults", "setNormalResults", "searchParams", "handleSearch", "toString", "get", "sorted", "rated", "normal", "sortResultsByTab", "handleTabChange", "event", "newValue", "tabValue", "ratedAccounts", "filter", "sort", "a", "b", "normalAccounts", "localeCompare", "component", "gutterBottom", "justifyContent", "minHeight", "size", "length", "mt", "padding", "marginBottom", "onChange", "label", "mb", "fontWeight", "container", "slice", "Math", "min", "map", "id", "max", "onClick", "searchQuery"], "sources": ["C:/Users/<USER>/Desktop/iDigitalX/ClientApp/src/pages/SearchResults.js"], "sourcesContent": ["import { useEffect, useState } from \"react\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON><PERSON>,\r\n  <PERSON>,\r\n  Box,\r\n  Typography,\r\n  Tabs,\r\n  Tab,\r\n  Container,\r\n  Rating,\r\n  Stack,\r\n} from \"@mui/material\";\r\nimport LocationOnIcon from \"@mui/icons-material/LocationOn\";\r\nimport WorkOutlineIcon from \"@mui/icons-material/WorkOutline\";\r\nimport StarBorderIcon from \"@mui/icons-material/StarBorder\";\r\nimport Avatar from \"@mui/material/Avatar\";\r\nimport { Link } from \"react-router-dom\";\r\nimport { useSearch } from \"../Context/SearchContext\";\r\nimport SearchNotFound from \"./SearchNotFound\";\r\nimport { motion } from \"framer-motion\";\r\nimport EmojiPeopleIcon from \"@mui/icons-material/EmojiPeople\";\r\nimport EngineeringIcon from \"@mui/icons-material/Engineering\";\r\nimport ApartmentIcon from \"@mui/icons-material/Apartment\";\r\nimport BusinessCenterIcon from \"@mui/icons-material/BusinessCenter\";\r\nimport { useSearchParams } from \"react-router-dom\";\r\nimport { CircularProgress } from \"@mui/material\";\r\n\r\n// Helper component for result card\r\nconst ResultCard = ({ result, GetCategoryIcon }) => (\r\n  <Grid item xs={12}>\r\n    <motion.div\r\n      className=\"animate-on-scroll\"\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      transition={{\r\n        duration: 0.5,\r\n        ease: \"easeOut\",\r\n      }}\r\n    >\r\n      <Link\r\n        to={`/Profile/${result.userName}`}\r\n        target=\"_blank\"\r\n        rel=\"noreferrer\"\r\n        style={{\r\n          textDecoration: \"none\",\r\n          color: \"inherit\",\r\n        }}\r\n      >\r\n        <Card\r\n          sx={{\r\n            display: \"flex\",\r\n            flexDirection: \"row\",\r\n            alignItems: \"center\",\r\n            p: 3,\r\n            gap: \"10px\",\r\n            boxShadow: \"0px 4px 10px rgba(0, 0, 0, 0.1)\",\r\n            transition: \"box-shadow 0.3s ease\",\r\n            \"&:hover\": {\r\n              boxShadow: \"0px 8px 20px rgba(0, 0, 0, 0.2)\",\r\n            },\r\n          }}\r\n        >\r\n          <Stack\r\n            direction=\"row\"\r\n            alignItems=\"center\"\r\n            spacing={2}\r\n            sx={{ flex: 1 }}\r\n          >\r\n            <Avatar\r\n              alt={result.user.firstName + \" \" + result.user.lastName}\r\n              src={result.profilePicture}\r\n              sx={{\r\n                width: 80,\r\n                height: 80,\r\n              }}\r\n            />\r\n            <Stack direction=\"column\" spacing={1}>\r\n              <Typography variant=\"h6\">\r\n                {result.user.firstName} {result.user.lastName}{\" \"}\r\n                <Stack\r\n                  direction=\"row\"\r\n                  alignItems=\"baseline\"\r\n                  spacing={1}\r\n                  sx={{\r\n                    display: \"inline-flex\",\r\n                    marginLeft: 1,\r\n                  }}\r\n                >\r\n                  <Typography\r\n                    variant=\"caption\"\r\n                    sx={{\r\n                      color: \"rgba(20, 43, 58, 0.5)\",\r\n                    }}\r\n                  >\r\n                    @{result.userName}\r\n                  </Typography>\r\n                  <Typography\r\n                    variant=\"body2\"\r\n                    sx={{\r\n                      color: \"rgba(20, 43, 58, 0.5)\",\r\n                    }}\r\n                  >\r\n                    {GetCategoryIcon(result.user.category)}\r\n                  </Typography>\r\n                </Stack>\r\n              </Typography>\r\n              {result.user.rate > 0 && (\r\n                <Rating\r\n                  name=\"read-only\"\r\n                  value={result.user.rate}\r\n                  readOnly\r\n                  emptyIcon={<StarBorderIcon fontSize=\"inherit\" />}\r\n                  sx={{\r\n                    marginLeft: \"10px\",\r\n                    fontSize: \"13px\",\r\n                  }}\r\n                />\r\n              )}\r\n              <Box\r\n                sx={{\r\n                  display: \"flex\",\r\n                  alignItems: \"center\",\r\n                  gap: \"10px\",\r\n                }}\r\n              >\r\n                {result.country && (\r\n                  <>\r\n                    <LocationOnIcon\r\n                      sx={{\r\n                        fontSize: \"15px\",\r\n                      }}\r\n                    />\r\n                    <Typography\r\n                      variant=\"caption\"\r\n                      sx={{\r\n                        color: \"rgba(20, 43, 58, 0.5)\",\r\n                      }}\r\n                    >\r\n                      {result.country}\r\n                    </Typography>\r\n                  </>\r\n                )}\r\n              </Box>\r\n              <Box\r\n                sx={{\r\n                  display: \"flex\",\r\n                  alignItems: \"center\",\r\n                  gap: \"10px\",\r\n                }}\r\n              >\r\n                {result.occupation && (\r\n                  <>\r\n                    <WorkOutlineIcon\r\n                      sx={{\r\n                        fontSize: \"15px\",\r\n                      }}\r\n                    />\r\n                    <Typography\r\n                      variant=\"caption\"\r\n                      sx={{\r\n                        color: \"rgba(20, 43, 58, 0.5)\",\r\n                      }}\r\n                    >\r\n                      {result.occupation}\r\n                    </Typography>\r\n                  </>\r\n                )}\r\n              </Box>\r\n            </Stack>\r\n          </Stack>\r\n        </Card>\r\n      </Link>\r\n    </motion.div>\r\n  </Grid>\r\n);\r\n\r\nconst SearchResults = () => {\r\n  const { searchResults, fetchSearchResults, loading } = useSearch();\r\n  const [activeTab, setActiveTab] = useState(0); // Default to \"All\" tab\r\n  const [displayCount, setDisplayCount] = useState(5);\r\n  const [sortedResults, setSortedResults] = useState([]);\r\n  const [ratedResults, setRatedResults] = useState([]);\r\n  const [normalResults, setNormalResults] = useState([]);\r\n  const [searchParams] = useSearchParams();\r\n\r\n  // Fetch search results only when search parameters change\r\n  useEffect(() => {\r\n    const handleSearch = async () => {\r\n      if (searchParams && searchParams.toString() !== \"\") {\r\n        await fetchSearchResults(searchParams.get(\"q\"));\r\n      }\r\n    };\r\n    handleSearch(); // Call search function only when searchParams change\r\n  }, [searchParams]);\r\n\r\n  // Update sortedResults whenever searchResults or activeTab changes\r\n  useEffect(() => {\r\n    const { sorted, rated, normal } = sortResultsByTab(activeTab);\r\n    setSortedResults(sorted);\r\n    setRatedResults(rated);\r\n    setNormalResults(normal);\r\n  }, [searchResults, activeTab]);\r\n\r\n  const GetCategoryIcon = (category) => {\r\n    switch (category) {\r\n      case \"Free\":\r\n        return <EmojiPeopleIcon fontSize=\"Large\" />;\r\n      case \"Student\":\r\n        return <BusinessCenterIcon fontSize=\"Large\" />;\r\n      case \"Freelance\":\r\n        return <EngineeringIcon fontSize=\"Large\" />;\r\n      case \"Entrepreneur\":\r\n        return <ApartmentIcon fontSize=\"Large\" />;\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  const handleTabChange = (event, newValue) => {\r\n    setActiveTab(newValue);\r\n    setDisplayCount(5); // Reset display count when switching tabs\r\n  };\r\n\r\n  const sortResultsByTab = (tabValue) => {\r\n    const ratedAccounts = searchResults\r\n      .filter((result) => result.user.rate > 0)\r\n      .sort((a, b) => b.user.rate - a.user.rate);\r\n\r\n    const normalAccounts = searchResults\r\n      .filter((result) => !result.user.rate || result.user.rate === 0)\r\n      .sort((a, b) => a.userName.localeCompare(b.userName));\r\n\r\n    switch (tabValue) {\r\n      case 0: // \"All\" tab - show both sections\r\n        return {\r\n          sorted: [...ratedAccounts, ...normalAccounts],\r\n          rated: ratedAccounts,\r\n          normal: normalAccounts,\r\n        };\r\n      case 1: // \"Rated\" tab - show only rated accounts\r\n        return {\r\n          sorted: ratedAccounts,\r\n          rated: ratedAccounts,\r\n          normal: [],\r\n        };\r\n      default:\r\n        return {\r\n          sorted: searchResults,\r\n          rated: ratedAccounts,\r\n          normal: normalAccounts,\r\n        };\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Container>\r\n      <Typography variant=\"h5\" component=\"h1\" gutterBottom>\r\n        Search\r\n      </Typography>\r\n      <Typography component=\"h2\" color=\"textSecondary\">\r\n        Results for {searchParams.get(\"q\")}\r\n      </Typography>\r\n      {loading ? (\r\n        <Box\r\n          sx={{\r\n            display: \"flex\",\r\n            justifyContent: \"center\",\r\n            alignItems: \"center\",\r\n            minHeight: \"300px\",\r\n            flexDirection: \"column\",\r\n            gap: 2,\r\n          }}\r\n        >\r\n          <CircularProgress size={60} />\r\n          <Typography variant=\"h6\" color=\"textSecondary\">\r\n            Searching...\r\n          </Typography>\r\n        </Box>\r\n      ) : searchResults.length > 0 ? (\r\n        <Box sx={{ mt: 5 }}>\r\n          <Box\r\n            sx={{\r\n              display: \"flex\",\r\n              justifyContent: \"center\",\r\n              gap: \"10px\",\r\n              padding: \"10px\",\r\n              marginBottom: \"30px\",\r\n            }}\r\n          >\r\n            <Tabs\r\n              value={activeTab}\r\n              onChange={handleTabChange}\r\n              aria-label=\"Account tabs\"\r\n            >\r\n              <Tab label=\"All\" />\r\n              <Tab label=\"Rated\" />\r\n            </Tabs>\r\n          </Box>\r\n          {activeTab === 0 ? (\r\n            // \"All\" tab - show organized sections\r\n            <Box>\r\n              {ratedResults.length > 0 && (\r\n                <Box sx={{ mb: 4 }}>\r\n                  <Typography\r\n                    variant=\"h5\"\r\n                    component=\"h2\"\r\n                    sx={{\r\n                      mb: 1,\r\n                      fontWeight: 600,\r\n                      color: \"primary.main\",\r\n                    }}\r\n                  >\r\n                    Top Rated Accounts\r\n                  </Typography>\r\n                  <Typography\r\n                    variant=\"body2\"\r\n                    color=\"text.secondary\"\r\n                    sx={{ mb: 3 }}\r\n                  >\r\n                    Accounts sorted by their ratings from highest to lowest\r\n                  </Typography>\r\n                  <Grid container spacing={3}>\r\n                    {ratedResults\r\n                      .slice(0, Math.min(displayCount, ratedResults.length))\r\n                      .map((result) => (\r\n                        <ResultCard\r\n                          key={result.id}\r\n                          result={result}\r\n                          GetCategoryIcon={GetCategoryIcon}\r\n                        />\r\n                      ))}\r\n                  </Grid>\r\n                </Box>\r\n              )}\r\n\r\n              {normalResults.length > 0 &&\r\n                displayCount > ratedResults.length && (\r\n                  <Box sx={{ mb: 4 }}>\r\n                    <Typography\r\n                      variant=\"h5\"\r\n                      component=\"h2\"\r\n                      sx={{\r\n                        mb: 1,\r\n                        fontWeight: 600,\r\n                        color: \"primary.main\",\r\n                      }}\r\n                    >\r\n                      Other Accounts\r\n                    </Typography>\r\n                    <Typography\r\n                      variant=\"body2\"\r\n                      color=\"text.secondary\"\r\n                      sx={{ mb: 3 }}\r\n                    >\r\n                      Accounts sorted alphabetically by username\r\n                    </Typography>\r\n                    <Grid container spacing={3}>\r\n                      {normalResults\r\n                        .slice(\r\n                          0,\r\n                          Math.max(0, displayCount - ratedResults.length)\r\n                        )\r\n                        .map((result) => (\r\n                          <ResultCard\r\n                            key={result.id}\r\n                            result={result}\r\n                            GetCategoryIcon={GetCategoryIcon}\r\n                          />\r\n                        ))}\r\n                    </Grid>\r\n                  </Box>\r\n                )}\r\n            </Box>\r\n          ) : (\r\n            // \"Rated\" tab - show only rated accounts\r\n            <Grid container spacing={3}>\r\n              {sortedResults.slice(0, displayCount).map((result) => (\r\n                <ResultCard\r\n                  key={result.id}\r\n                  result={result}\r\n                  GetCategoryIcon={GetCategoryIcon}\r\n                />\r\n              ))}\r\n            </Grid>\r\n          )}\r\n          {displayCount < sortedResults.length && (\r\n            <Box\r\n              sx={{\r\n                display: \"flex\",\r\n                justifyContent: \"center\",\r\n                mt: 3,\r\n              }}\r\n            >\r\n              <Button\r\n                variant=\"outlined\"\r\n                onClick={() => setDisplayCount(displayCount + 5)}\r\n              >\r\n                View More\r\n              </Button>\r\n            </Box>\r\n          )}\r\n        </Box>\r\n      ) : (\r\n        <SearchNotFound searchQuery={searchParams.get(\"q\")} />\r\n      )}\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default SearchResults;\r\n"], "mappings": "AAAA,OAASA,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAC3C,OACEC,IAAI,CACJC,MAAM,CACNC,IAAI,CACJC,GAAG,CACHC,UAAU,CACVC,IAAI,CACJC,GAAG,CACHC,SAAS,CACTC,MAAM,CACNC,KAAK,KACA,eAAe,CACtB,MAAO,CAAAC,cAAc,KAAM,gCAAgC,CAC3D,MAAO,CAAAC,eAAe,KAAM,iCAAiC,CAC7D,MAAO,CAAAC,cAAc,KAAM,gCAAgC,CAC3D,MAAO,CAAAC,MAAM,KAAM,sBAAsB,CACzC,OAASC,IAAI,KAAQ,kBAAkB,CACvC,OAASC,SAAS,KAAQ,0BAA0B,CACpD,MAAO,CAAAC,cAAc,KAAM,kBAAkB,CAC7C,OAASC,MAAM,KAAQ,eAAe,CACtC,MAAO,CAAAC,eAAe,KAAM,iCAAiC,CAC7D,MAAO,CAAAC,eAAe,KAAM,iCAAiC,CAC7D,MAAO,CAAAC,aAAa,KAAM,+BAA+B,CACzD,MAAO,CAAAC,kBAAkB,KAAM,oCAAoC,CACnE,OAASC,eAAe,KAAQ,kBAAkB,CAClD,OAASC,gBAAgB,KAAQ,eAAe,CAEhD;AAAA,OAAAC,GAAA,IAAAC,IAAA,gCAAAC,IAAA,IAAAC,KAAA,gCAAAC,QAAA,IAAAC,SAAA,yBACA,KAAM,CAAAC,UAAU,CAAGC,IAAA,MAAC,CAAEC,MAAM,CAAEC,eAAgB,CAAC,CAAAF,IAAA,oBAC7CN,IAAA,CAACzB,IAAI,EAACkC,IAAI,MAACC,EAAE,CAAE,EAAG,CAAAC,QAAA,cAChBX,IAAA,CAACR,MAAM,CAACoB,GAAG,EACTC,SAAS,CAAC,mBAAmB,CAC7BC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CACVC,QAAQ,CAAE,GAAG,CACbC,IAAI,CAAE,SACR,CAAE,CAAAT,QAAA,cAEFX,IAAA,CAACX,IAAI,EACHgC,EAAE,CAAE,YAAYd,MAAM,CAACe,QAAQ,EAAG,CAClCC,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,YAAY,CAChBC,KAAK,CAAE,CACLC,cAAc,CAAE,MAAM,CACtBC,KAAK,CAAE,SACT,CAAE,CAAAhB,QAAA,cAEFX,IAAA,CAACvB,IAAI,EACHmD,EAAE,CAAE,CACFC,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,KAAK,CACpBC,UAAU,CAAE,QAAQ,CACpBC,CAAC,CAAE,CAAC,CACJC,GAAG,CAAE,MAAM,CACXC,SAAS,CAAE,iCAAiC,CAC5ChB,UAAU,CAAE,sBAAsB,CAClC,SAAS,CAAE,CACTgB,SAAS,CAAE,iCACb,CACF,CAAE,CAAAvB,QAAA,cAEFT,KAAA,CAAClB,KAAK,EACJmD,SAAS,CAAC,KAAK,CACfJ,UAAU,CAAC,QAAQ,CACnBK,OAAO,CAAE,CAAE,CACXR,EAAE,CAAE,CAAES,IAAI,CAAE,CAAE,CAAE,CAAA1B,QAAA,eAEhBX,IAAA,CAACZ,MAAM,EACLkD,GAAG,CAAE/B,MAAM,CAACgC,IAAI,CAACC,SAAS,CAAG,GAAG,CAAGjC,MAAM,CAACgC,IAAI,CAACE,QAAS,CACxDC,GAAG,CAAEnC,MAAM,CAACoC,cAAe,CAC3Bf,EAAE,CAAE,CACFgB,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,EACV,CAAE,CACH,CAAC,cACF3C,KAAA,CAAClB,KAAK,EAACmD,SAAS,CAAC,QAAQ,CAACC,OAAO,CAAE,CAAE,CAAAzB,QAAA,eACnCT,KAAA,CAACvB,UAAU,EAACmE,OAAO,CAAC,IAAI,CAAAnC,QAAA,EACrBJ,MAAM,CAACgC,IAAI,CAACC,SAAS,CAAC,GAAC,CAACjC,MAAM,CAACgC,IAAI,CAACE,QAAQ,CAAE,GAAG,cAClDvC,KAAA,CAAClB,KAAK,EACJmD,SAAS,CAAC,KAAK,CACfJ,UAAU,CAAC,UAAU,CACrBK,OAAO,CAAE,CAAE,CACXR,EAAE,CAAE,CACFC,OAAO,CAAE,aAAa,CACtBkB,UAAU,CAAE,CACd,CAAE,CAAApC,QAAA,eAEFT,KAAA,CAACvB,UAAU,EACTmE,OAAO,CAAC,SAAS,CACjBlB,EAAE,CAAE,CACFD,KAAK,CAAE,uBACT,CAAE,CAAAhB,QAAA,EACH,GACE,CAACJ,MAAM,CAACe,QAAQ,EACP,CAAC,cACbtB,IAAA,CAACrB,UAAU,EACTmE,OAAO,CAAC,OAAO,CACflB,EAAE,CAAE,CACFD,KAAK,CAAE,uBACT,CAAE,CAAAhB,QAAA,CAEDH,eAAe,CAACD,MAAM,CAACgC,IAAI,CAACS,QAAQ,CAAC,CAC5B,CAAC,EACR,CAAC,EACE,CAAC,CACZzC,MAAM,CAACgC,IAAI,CAACU,IAAI,CAAG,CAAC,eACnBjD,IAAA,CAACjB,MAAM,EACLmE,IAAI,CAAC,WAAW,CAChBC,KAAK,CAAE5C,MAAM,CAACgC,IAAI,CAACU,IAAK,CACxBG,QAAQ,MACRC,SAAS,cAAErD,IAAA,CAACb,cAAc,EAACmE,QAAQ,CAAC,SAAS,CAAE,CAAE,CACjD1B,EAAE,CAAE,CACFmB,UAAU,CAAE,MAAM,CAClBO,QAAQ,CAAE,MACZ,CAAE,CACH,CACF,cACDtD,IAAA,CAACtB,GAAG,EACFkD,EAAE,CAAE,CACFC,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBE,GAAG,CAAE,MACP,CAAE,CAAAtB,QAAA,CAEDJ,MAAM,CAACgD,OAAO,eACbrD,KAAA,CAAAE,SAAA,EAAAO,QAAA,eACEX,IAAA,CAACf,cAAc,EACb2C,EAAE,CAAE,CACF0B,QAAQ,CAAE,MACZ,CAAE,CACH,CAAC,cACFtD,IAAA,CAACrB,UAAU,EACTmE,OAAO,CAAC,SAAS,CACjBlB,EAAE,CAAE,CACFD,KAAK,CAAE,uBACT,CAAE,CAAAhB,QAAA,CAEDJ,MAAM,CAACgD,OAAO,CACL,CAAC,EACb,CACH,CACE,CAAC,cACNvD,IAAA,CAACtB,GAAG,EACFkD,EAAE,CAAE,CACFC,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBE,GAAG,CAAE,MACP,CAAE,CAAAtB,QAAA,CAEDJ,MAAM,CAACiD,UAAU,eAChBtD,KAAA,CAAAE,SAAA,EAAAO,QAAA,eACEX,IAAA,CAACd,eAAe,EACd0C,EAAE,CAAE,CACF0B,QAAQ,CAAE,MACZ,CAAE,CACH,CAAC,cACFtD,IAAA,CAACrB,UAAU,EACTmE,OAAO,CAAC,SAAS,CACjBlB,EAAE,CAAE,CACFD,KAAK,CAAE,uBACT,CAAE,CAAAhB,QAAA,CAEDJ,MAAM,CAACiD,UAAU,CACR,CAAC,EACb,CACH,CACE,CAAC,EACD,CAAC,EACH,CAAC,CACJ,CAAC,CACH,CAAC,CACG,CAAC,CACT,CAAC,EACR,CAED,KAAM,CAAAC,aAAa,CAAGA,CAAA,GAAM,CAC1B,KAAM,CAAEC,aAAa,CAAEC,kBAAkB,CAAEC,OAAQ,CAAC,CAAGtE,SAAS,CAAC,CAAC,CAClE,KAAM,CAACuE,SAAS,CAAEC,YAAY,CAAC,CAAGxF,QAAQ,CAAC,CAAC,CAAC,CAAE;AAC/C,KAAM,CAACyF,YAAY,CAAEC,eAAe,CAAC,CAAG1F,QAAQ,CAAC,CAAC,CAAC,CACnD,KAAM,CAAC2F,aAAa,CAAEC,gBAAgB,CAAC,CAAG5F,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAC6F,YAAY,CAAEC,eAAe,CAAC,CAAG9F,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAAC+F,aAAa,CAAEC,gBAAgB,CAAC,CAAGhG,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACiG,YAAY,CAAC,CAAG1E,eAAe,CAAC,CAAC,CAExC;AACAxB,SAAS,CAAC,IAAM,CACd,KAAM,CAAAmG,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAID,YAAY,EAAIA,YAAY,CAACE,QAAQ,CAAC,CAAC,GAAK,EAAE,CAAE,CAClD,KAAM,CAAAd,kBAAkB,CAACY,YAAY,CAACG,GAAG,CAAC,GAAG,CAAC,CAAC,CACjD,CACF,CAAC,CACDF,YAAY,CAAC,CAAC,CAAE;AAClB,CAAC,CAAE,CAACD,YAAY,CAAC,CAAC,CAElB;AACAlG,SAAS,CAAC,IAAM,CACd,KAAM,CAAEsG,MAAM,CAAEC,KAAK,CAAEC,MAAO,CAAC,CAAGC,gBAAgB,CAACjB,SAAS,CAAC,CAC7DK,gBAAgB,CAACS,MAAM,CAAC,CACxBP,eAAe,CAACQ,KAAK,CAAC,CACtBN,gBAAgB,CAACO,MAAM,CAAC,CAC1B,CAAC,CAAE,CAACnB,aAAa,CAAEG,SAAS,CAAC,CAAC,CAE9B,KAAM,CAAArD,eAAe,CAAIwC,QAAQ,EAAK,CACpC,OAAQA,QAAQ,EACd,IAAK,MAAM,CACT,mBAAOhD,IAAA,CAACP,eAAe,EAAC6D,QAAQ,CAAC,OAAO,CAAE,CAAC,CAC7C,IAAK,SAAS,CACZ,mBAAOtD,IAAA,CAACJ,kBAAkB,EAAC0D,QAAQ,CAAC,OAAO,CAAE,CAAC,CAChD,IAAK,WAAW,CACd,mBAAOtD,IAAA,CAACN,eAAe,EAAC4D,QAAQ,CAAC,OAAO,CAAE,CAAC,CAC7C,IAAK,cAAc,CACjB,mBAAOtD,IAAA,CAACL,aAAa,EAAC2D,QAAQ,CAAC,OAAO,CAAE,CAAC,CAC3C,QACE,MAAO,KAAI,CACf,CACF,CAAC,CAED,KAAM,CAAAyB,eAAe,CAAGA,CAACC,KAAK,CAAEC,QAAQ,GAAK,CAC3CnB,YAAY,CAACmB,QAAQ,CAAC,CACtBjB,eAAe,CAAC,CAAC,CAAC,CAAE;AACtB,CAAC,CAED,KAAM,CAAAc,gBAAgB,CAAII,QAAQ,EAAK,CACrC,KAAM,CAAAC,aAAa,CAAGzB,aAAa,CAChC0B,MAAM,CAAE7E,MAAM,EAAKA,MAAM,CAACgC,IAAI,CAACU,IAAI,CAAG,CAAC,CAAC,CACxCoC,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKA,CAAC,CAAChD,IAAI,CAACU,IAAI,CAAGqC,CAAC,CAAC/C,IAAI,CAACU,IAAI,CAAC,CAE5C,KAAM,CAAAuC,cAAc,CAAG9B,aAAa,CACjC0B,MAAM,CAAE7E,MAAM,EAAK,CAACA,MAAM,CAACgC,IAAI,CAACU,IAAI,EAAI1C,MAAM,CAACgC,IAAI,CAACU,IAAI,GAAK,CAAC,CAAC,CAC/DoC,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKD,CAAC,CAAChE,QAAQ,CAACmE,aAAa,CAACF,CAAC,CAACjE,QAAQ,CAAC,CAAC,CAEvD,OAAQ4D,QAAQ,EACd,IAAK,EAAC,CAAE;AACN,MAAO,CACLP,MAAM,CAAE,CAAC,GAAGQ,aAAa,CAAE,GAAGK,cAAc,CAAC,CAC7CZ,KAAK,CAAEO,aAAa,CACpBN,MAAM,CAAEW,cACV,CAAC,CACH,IAAK,EAAC,CAAE;AACN,MAAO,CACLb,MAAM,CAAEQ,aAAa,CACrBP,KAAK,CAAEO,aAAa,CACpBN,MAAM,CAAE,EACV,CAAC,CACH,QACE,MAAO,CACLF,MAAM,CAAEjB,aAAa,CACrBkB,KAAK,CAAEO,aAAa,CACpBN,MAAM,CAAEW,cACV,CAAC,CACL,CACF,CAAC,CAED,mBACEtF,KAAA,CAACpB,SAAS,EAAA6B,QAAA,eACRX,IAAA,CAACrB,UAAU,EAACmE,OAAO,CAAC,IAAI,CAAC4C,SAAS,CAAC,IAAI,CAACC,YAAY,MAAAhF,QAAA,CAAC,QAErD,CAAY,CAAC,cACbT,KAAA,CAACvB,UAAU,EAAC+G,SAAS,CAAC,IAAI,CAAC/D,KAAK,CAAC,eAAe,CAAAhB,QAAA,EAAC,cACnC,CAAC4D,YAAY,CAACG,GAAG,CAAC,GAAG,CAAC,EACxB,CAAC,CACZd,OAAO,cACN1D,KAAA,CAACxB,GAAG,EACFkD,EAAE,CAAE,CACFC,OAAO,CAAE,MAAM,CACf+D,cAAc,CAAE,QAAQ,CACxB7D,UAAU,CAAE,QAAQ,CACpB8D,SAAS,CAAE,OAAO,CAClB/D,aAAa,CAAE,QAAQ,CACvBG,GAAG,CAAE,CACP,CAAE,CAAAtB,QAAA,eAEFX,IAAA,CAACF,gBAAgB,EAACgG,IAAI,CAAE,EAAG,CAAE,CAAC,cAC9B9F,IAAA,CAACrB,UAAU,EAACmE,OAAO,CAAC,IAAI,CAACnB,KAAK,CAAC,eAAe,CAAAhB,QAAA,CAAC,cAE/C,CAAY,CAAC,EACV,CAAC,CACJ+C,aAAa,CAACqC,MAAM,CAAG,CAAC,cAC1B7F,KAAA,CAACxB,GAAG,EAACkD,EAAE,CAAE,CAAEoE,EAAE,CAAE,CAAE,CAAE,CAAArF,QAAA,eACjBX,IAAA,CAACtB,GAAG,EACFkD,EAAE,CAAE,CACFC,OAAO,CAAE,MAAM,CACf+D,cAAc,CAAE,QAAQ,CACxB3D,GAAG,CAAE,MAAM,CACXgE,OAAO,CAAE,MAAM,CACfC,YAAY,CAAE,MAChB,CAAE,CAAAvF,QAAA,cAEFT,KAAA,CAACtB,IAAI,EACHuE,KAAK,CAAEU,SAAU,CACjBsC,QAAQ,CAAEpB,eAAgB,CAC1B,aAAW,cAAc,CAAApE,QAAA,eAEzBX,IAAA,CAACnB,GAAG,EAACuH,KAAK,CAAC,KAAK,CAAE,CAAC,cACnBpG,IAAA,CAACnB,GAAG,EAACuH,KAAK,CAAC,OAAO,CAAE,CAAC,EACjB,CAAC,CACJ,CAAC,CACLvC,SAAS,GAAK,CAAC,eACd;AACA3D,KAAA,CAACxB,GAAG,EAAAiC,QAAA,EACDwD,YAAY,CAAC4B,MAAM,CAAG,CAAC,eACtB7F,KAAA,CAACxB,GAAG,EAACkD,EAAE,CAAE,CAAEyE,EAAE,CAAE,CAAE,CAAE,CAAA1F,QAAA,eACjBX,IAAA,CAACrB,UAAU,EACTmE,OAAO,CAAC,IAAI,CACZ4C,SAAS,CAAC,IAAI,CACd9D,EAAE,CAAE,CACFyE,EAAE,CAAE,CAAC,CACLC,UAAU,CAAE,GAAG,CACf3E,KAAK,CAAE,cACT,CAAE,CAAAhB,QAAA,CACH,oBAED,CAAY,CAAC,cACbX,IAAA,CAACrB,UAAU,EACTmE,OAAO,CAAC,OAAO,CACfnB,KAAK,CAAC,gBAAgB,CACtBC,EAAE,CAAE,CAAEyE,EAAE,CAAE,CAAE,CAAE,CAAA1F,QAAA,CACf,yDAED,CAAY,CAAC,cACbX,IAAA,CAACzB,IAAI,EAACgI,SAAS,MAACnE,OAAO,CAAE,CAAE,CAAAzB,QAAA,CACxBwD,YAAY,CACVqC,KAAK,CAAC,CAAC,CAAEC,IAAI,CAACC,GAAG,CAAC3C,YAAY,CAAEI,YAAY,CAAC4B,MAAM,CAAC,CAAC,CACrDY,GAAG,CAAEpG,MAAM,eACVP,IAAA,CAACK,UAAU,EAETE,MAAM,CAAEA,MAAO,CACfC,eAAe,CAAEA,eAAgB,EAF5BD,MAAM,CAACqG,EAGb,CACF,CAAC,CACA,CAAC,EACJ,CACN,CAEAvC,aAAa,CAAC0B,MAAM,CAAG,CAAC,EACvBhC,YAAY,CAAGI,YAAY,CAAC4B,MAAM,eAChC7F,KAAA,CAACxB,GAAG,EAACkD,EAAE,CAAE,CAAEyE,EAAE,CAAE,CAAE,CAAE,CAAA1F,QAAA,eACjBX,IAAA,CAACrB,UAAU,EACTmE,OAAO,CAAC,IAAI,CACZ4C,SAAS,CAAC,IAAI,CACd9D,EAAE,CAAE,CACFyE,EAAE,CAAE,CAAC,CACLC,UAAU,CAAE,GAAG,CACf3E,KAAK,CAAE,cACT,CAAE,CAAAhB,QAAA,CACH,gBAED,CAAY,CAAC,cACbX,IAAA,CAACrB,UAAU,EACTmE,OAAO,CAAC,OAAO,CACfnB,KAAK,CAAC,gBAAgB,CACtBC,EAAE,CAAE,CAAEyE,EAAE,CAAE,CAAE,CAAE,CAAA1F,QAAA,CACf,4CAED,CAAY,CAAC,cACbX,IAAA,CAACzB,IAAI,EAACgI,SAAS,MAACnE,OAAO,CAAE,CAAE,CAAAzB,QAAA,CACxB0D,aAAa,CACXmC,KAAK,CACJ,CAAC,CACDC,IAAI,CAACI,GAAG,CAAC,CAAC,CAAE9C,YAAY,CAAGI,YAAY,CAAC4B,MAAM,CAChD,CAAC,CACAY,GAAG,CAAEpG,MAAM,eACVP,IAAA,CAACK,UAAU,EAETE,MAAM,CAAEA,MAAO,CACfC,eAAe,CAAEA,eAAgB,EAF5BD,MAAM,CAACqG,EAGb,CACF,CAAC,CACA,CAAC,EACJ,CACN,EACA,CAAC,eAEN;AACA5G,IAAA,CAACzB,IAAI,EAACgI,SAAS,MAACnE,OAAO,CAAE,CAAE,CAAAzB,QAAA,CACxBsD,aAAa,CAACuC,KAAK,CAAC,CAAC,CAAEzC,YAAY,CAAC,CAAC4C,GAAG,CAAEpG,MAAM,eAC/CP,IAAA,CAACK,UAAU,EAETE,MAAM,CAAEA,MAAO,CACfC,eAAe,CAAEA,eAAgB,EAF5BD,MAAM,CAACqG,EAGb,CACF,CAAC,CACE,CACP,CACA7C,YAAY,CAAGE,aAAa,CAAC8B,MAAM,eAClC/F,IAAA,CAACtB,GAAG,EACFkD,EAAE,CAAE,CACFC,OAAO,CAAE,MAAM,CACf+D,cAAc,CAAE,QAAQ,CACxBI,EAAE,CAAE,CACN,CAAE,CAAArF,QAAA,cAEFX,IAAA,CAACxB,MAAM,EACLsE,OAAO,CAAC,UAAU,CAClBgE,OAAO,CAAEA,CAAA,GAAM9C,eAAe,CAACD,YAAY,CAAG,CAAC,CAAE,CAAApD,QAAA,CAClD,WAED,CAAQ,CAAC,CACN,CACN,EACE,CAAC,cAENX,IAAA,CAACT,cAAc,EAACwH,WAAW,CAAExC,YAAY,CAACG,GAAG,CAAC,GAAG,CAAE,CAAE,CACtD,EACQ,CAAC,CAEhB,CAAC,CAED,cAAe,CAAAjB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module"}