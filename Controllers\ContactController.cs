using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using idigix.Models;
using idigix.Services;
using idigix.Services.interfaces;


namespace idigix.Controllers;
[Route("api/[controller]")]
[ApiController]
public class ContactController : ControllerBase
{
    private readonly IConfiguration _configuration;
    private readonly ITokenService _tokenService;
    private readonly dbContext _cnx;

    public ContactController(IConfiguration configuration, dbContext cnx, ITokenService tokenService)
    {
        this._cnx = cnx;
        this._configuration = configuration;
        this._tokenService = tokenService;
    }

    [HttpPost("CreateContact")]
    [Authorize]
    public async Task<IActionResult> CreateContact([FromBody] postContact contact)
    {
        if (contact == null)
        {
            return BadRequest("Contact data is required");
        }

        if (string.IsNullOrEmpty(contact.ContactInfo))
        {
            return BadRequest("ContactInfo is required");
        }

        Contact newContact = new Contact
        {
            Category = contact.Category,
            ContactInfo = contact.ContactInfo,
            Title = contact.Title,
            isPublic = contact.isPublic,
            UserId = contact.UserId,
        };

        try
        {
            await _cnx.Contact.AddAsync(newContact);
            await _cnx.SaveChangesAsync();
            return Ok();
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"An error occurred while saving the new contact: {ex.Message}");
        }
    }

    [HttpPut("EditContact")]
    [Authorize]
    public async Task<IActionResult> EditContact([FromBody] putContact newContact)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var userId = _tokenService.GetUserIdFromToken(Request.Headers["Authorization"].ToString().Replace("Bearer ", ""));

        if (userId == 0)
            return BadRequest("userId is null");

        var contact = await _cnx.Contact.FirstOrDefaultAsync(w => w.Id == newContact.Id && w.UserId == userId);

        if (contact is null)
            return NotFound();
        contact.ContactInfo = newContact.ContactInfo;
        contact.Title = newContact.Title;
        contact.isPublic = newContact.isPublic;

        await _cnx.SaveChangesAsync();

        return Ok();
    }

    [HttpDelete("DeleteContact/{Id}")]
    [Authorize]
    public async Task<IActionResult> DeleteContact(int Id)
    {
        if (Id <= 0)
            return BadRequest("Invalid Id");

        var userId = _tokenService.GetUserIdFromToken(Request.Headers["Authorization"].ToString().Replace("Bearer ", ""));

        if (userId == 0)
            return BadRequest("userId is null");

        var contact = await _cnx.Contact.FirstOrDefaultAsync(w => w.Id == Id && w.UserId == userId);
        if (contact == null)
            return NotFound("Contact not found or you don't have permission to delete it");

        try
        {
            _cnx.Contact.Remove(contact);
            await _cnx.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Internal server error: {ex.Message}");
        }

        return Ok("Contact deleted");
    }
}

