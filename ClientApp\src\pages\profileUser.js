import { useState } from "react";
import {
  TextField,
  Grid,
  Avatar,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tab,
  Tabs,
  Card,
  CardContent,
  IconButton,
  Box,
  CardHeader,
  Container,
  CircularProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
} from "@mui/material";

import Tooltip, { tooltipClasses } from "@mui/material/Tooltip";
import { useNavigate } from "react-router-dom";
import { Helmet } from "react-helmet-async";
import { styled } from "@mui/material/styles";
import CreateIcon from "@mui/icons-material/Create";
import CloseIcon from "@mui/icons-material/Close";
import SaveIcon from "@mui/icons-material/Save";

import InstagramIcon from "@mui/icons-material/Instagram";
import FacebookIcon from "@mui/icons-material/Facebook";
import GitHubIcon from "@mui/icons-material/GitHub";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import LinkedInIcon from "@mui/icons-material/LinkedIn";
import PhoneIcon from "@mui/icons-material/Phone";
import PhoneIphoneIcon from "@mui/icons-material/PhoneIphone";
import XIcon from "@mui/icons-material/X";
import EmailIcon from "@mui/icons-material/Email";
import WhatsAppIcon from "@mui/icons-material/WhatsApp";
import YouTubeIcon from "@mui/icons-material/YouTube";
import PinterestIcon from "@mui/icons-material/Pinterest";
import RedditIcon from "@mui/icons-material/Reddit";
import TelegramIcon from "@mui/icons-material/Telegram";
import SpotifyIcon from "@mui/icons-material/MusicNote";
import LanguageIcon from "@mui/icons-material/Language";
import Typography from "@mui/material/Typography";
import AppLinksByProfile from "../sections/@dashboard/app/AppLinksByProfile";
import { AppProfileCard } from "../sections/@dashboard/app";
import { EditProfile } from "../ProfileData.ts";
import { DeleteContact } from "../ContactData.ts";
import {
  GetSocialLinks,
  EditCustomLink,
  EditSocialLink,
  CreateSocialLink,
  CreateCustomLink,
  DeleteSocialLink,
  GetCustomLinks,
  DeleteCustomLink,
} from "../LinkData.ts";
import { useEffect } from "react";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import PhotoSelector from "../sections/auth/signup/PhotoSelector";
import { useProfile } from "../Context/ProfileContext";
import PhoneLinkDialog from "../sections/@dashboard/Link/PhoneLinkDialog";
import EmailLinkDialog from "../sections/@dashboard/Link/EmailLinkDialog";
import WhatsAppLinkDialog from "../sections/@dashboard/Link/WhatsAppLinkDialog";
import { motion } from "framer-motion";
import EmojiPeopleIcon from "@mui/icons-material/EmojiPeople";
import BusinessCenterIcon from "@mui/icons-material/BusinessCenter";
import EngineeringIcon from "@mui/icons-material/Engineering";
import ApartmentIcon from "@mui/icons-material/Apartment";
import Iconify from "../components/iconify";
import Appearance from "./Appearance";
import { SvgIcon } from "@mui/material";

function TikTokIcon(props) {
  return (
    <SvgIcon {...props}>
      <path d="M12.95 2c.52 1.97 2.08 3.38 4.05 3.59v3.27a7.49 7.49 0 0 1-3.64-.98v5.77c0 3.58-3.2 6.54-7.15 5.38A5.55 5.55 0 0 1 4 13.3c0-2.74 2.05-5.06 4.83-5.35v3.28c-.89.18-1.55.97-1.55 1.93 0 1.08.9 1.96 2 1.96s2-.88 2-1.96V2h1.67z" />
    </SvgIcon>
  );
}

// import About from "./About";

const BootstrapTooltip = styled(({ className, ...props }) => (
  <Tooltip {...props} arrow classes={{ popper: className }} />
))(({ theme }) => ({
  [`& .${tooltipClasses.arrow}`]: {
    color: "#ee705e",
  },
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: "#ee705e",
  },
}));

const ProfileUser = () => {
  const { profile, fetchProfile } = useProfile();
  const [activeTab, setActiveTab] = useState(0);
  const [isMobile, setIsMobile] = useState(false);
  const [isProfileCardVisible, setIsProfileCardVisible] = useState(false);
  const [isVisible, setIsVisible] = useState(true);
  const [isSaveButtonActive, setIsSaveButtonActive] = useState(false);
  const [isProfileSaving, setIsProfileSaving] = useState(false);
  const [isCustomLinkSaving, setIsCustomLinkSaving] = useState(false);
  const [isSocialLinkSaving, setIsSocialLinkSaving] = useState(false);
  const navigate = useNavigate();

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Function to get bundle icon based on category
  const getBundleIcon = (category) => {
    switch (category) {
      case "Free":
        return <EmojiPeopleIcon fontSize="large" sx={{ color: "#ff715b" }} />;
      case "Student":
        return (
          <BusinessCenterIcon fontSize="large" sx={{ color: "#ff715b" }} />
        );
      case "Freelance":
        return <EngineeringIcon fontSize="large" sx={{ color: "#ff715b" }} />;
      case "Enterprise":
        return <ApartmentIcon fontSize="large" sx={{ color: "#ff715b" }} />;
      default:
        return <EmojiPeopleIcon fontSize="large" sx={{ color: "#ff715b" }} />;
    }
  };

  const [User, setUser] = useState({
    id: 0,
    email: "",

    firstName: "",
    lastName: "",
    category: "",
    budget: 0.0,
  });

  const [Profile, setProfile] = useState({
    id: 0,
    userId: 0,
    userName: "",
    birthDate: "",
    gender: "",
    profilePicture: "",
    profileCoverPicture: "",
    profilePictureFrame: 0,
    occupation: "",
    isPremium: false,
    user: null,
    socialLinks: null,
    customLinks: null,
    premium: null,
    isSearch: null,
    country: null,
  });

  const [isLoading, setIsLoading] = useState(true);
  const [SocialLinks, setSocialLinks] = useState([]);

  const [CustomLinks, setCustomLinks] = useState([]);

  const [newSocialLink, setNewSocialLink] = useState({
    ProfileId: 0,
    LinkUrl: "",
    Category: "",
    Title: "",
    Color: "",
  });

  const [newCustomLink, setNewCustomLink] = useState({
    ProfileId: 0,
    LinkUrl: "",
    Title: "",
    Color: "Gray",
    Icon: null,
  });

  const [editCustomSectionVisible, setEditCustomSectionVisible] =
    useState(false);

  const [editedCustomLink, setEditedCustomLink] = useState({
    Id: 0,
    ProfileId: 0,
    LinkUrl: "",
    Title: "",
    Icon: null,
  });

  const [editSocialSectionVisible, setEditSocialSectionVisible] =
    useState(false);

  const [editedSocialLink, setEditedSocialLink] = useState({
    Id: 0,
    ProfileId: 0,
    Title: "",
    LinkUrl: "",
    Category: "",
    Color: "",
  });

  const [open, setOpen] = useState(false);
  const [CategoryChosen, setCategoryChosen] = useState(false);

  const handleClickOpen = (title, text, color) => {
    setOpen(true);
    setNewSocialLink((prevNewSocialLink) => ({
      ...prevNewSocialLink,
      Category: text,
      Color: color,
    }));
  };

  const socialLinks = [
    {
      platform: "Twitter",
      icon: "X",
      label: "Can you provide your Twitter account link?",
      dialogTitle: "Twitter link",
      color: "linear-gradient(to bottom,#0d90e0, #1DA1F2)",
    },
    {
      platform: "GitHub",
      icon: "GitHub",
      label: "Can you provide your GitHub account link?",
      dialogTitle: "GitHub link",
      color:
        "linear-gradient(112.1deg, rgb(63, 76, 119) 11.4%, rgb(32, 38, 57) 70.2%)",
    },
    {
      platform: "Instagram",
      icon: "Instagram",
      label: "Can you provide your Insta account link?",
      dialogTitle: "insta link",
      color: "linear-gradient(90deg, #f46f30, #c32aa3)",
    },
    {
      platform: "Facebook",
      icon: "Facebook",
      label: "Can you provide your Facebook account link?",
      dialogTitle: "facebook link",
      color: "linear-gradient(180deg, #1877f2, #3b5998)",
    },
    {
      platform: "TikTok",
      icon: "TikTok",
      label: "Can you provide your TikTok account link?",
      dialogTitle: "TikTok link",
      color: "linear-gradient(180deg, #000000, #ff0050)",
    },
    {
      platform: "LinkedIn",
      icon: "LinkedIn",
      label: "Can you provide your LinkedIn account link?",
      dialogTitle: "LinkedIn link",
      color: "linear-gradient(135deg, #0077B5, #00A0DC)",
    },
  ];

  const PhoneLinks = [
    {
      platform: "Phone",
      icon: "Phone",
      label: "Can you provide your Phone number?",
      dialogTitle: "Phone link",
      color: "linear-gradient(to bottom,#0d90e0, #1DA1F2)",
    },
    {
      platform: "Email",
      icon: "Email",
      label: "Can you provide your Email address?",
      dialogTitle: "Email link",
      color: "linear-gradient(to bottom,#EA4335, #FBBC05)",
    },
    {
      platform: "WhatsApp",
      icon: "WhatsApp",
      label: "Can you provide your WhatsApp number?",
      dialogTitle: "WhatsApp link",
      color: "linear-gradient(to bottom,#25D366, #128C7E)",
    },
  ];

  const handleUserChange = (event) => {
    const { name, value } = event.target;

    const hasNonSpaceCharacter = value.trim() !== "";

    const isValidInput = /^[a-zA-Z\s]+$/.test(value);

    if (name === "firstName" || name === "lastName") {
      if (isValidInput && hasNonSpaceCharacter) {
        setUser((prevUser) => ({
          ...prevUser,
          [name]: value,
        }));
        setIsSaveButtonActive(true);
      }
    }
  };

  const handleProfileChange = (event) => {
    const { name, value } = event.target;

    setProfile((prevProfile) => ({
      ...prevProfile,
      [name]: value,
    }));
    setIsSaveButtonActive(true);
  };

  const handleSearchChange = (value) => {
    setProfile((prevProfile) => ({
      ...prevProfile,
      isSearch: value,
    }));
    setIsSaveButtonActive(true);
  };

  const handleNewSocialLinkChange = (event) => {
    const { name, value } = event.target;
    setNewSocialLink((prevNewSocialLink) => ({
      ...prevNewSocialLink,
      [name]: value,
    }));
  };

  const handleNewCustomLinkChange = (event) => {
    const { name, value } = event.target;
    setNewCustomLink((prevNewCustomLink) => ({
      ...prevNewCustomLink,
      [name]: value,
    }));

    setValidationStatus({
      ...validationStatus,
      [name]: value !== "",
    });
  };

  const handlePhotoSelect = (photoDataUrl) => {
    setProfile((prevData) => ({
      ...prevData,
      profilePicture: photoDataUrl,
    }));
    setIsSaveButtonActive(true);
  };

  const handleCoverPhotoSelect = (photoDataUrl) => {
    setProfile((prevData) => ({
      ...prevData,
      profileCoverPicture: photoDataUrl,
    }));
    setIsSaveButtonActive(true);
  };

  const handleSave = async () => {
    setIsProfileSaving(true);
    try {
      const response = await EditProfile(User, Profile);

      if (response && response.status === 200) {
        await fetchProfile();
        toast.success("Profile saved successfully", {
          position: "top-center",
          autoClose: 1000,
        });
        setIsSaveButtonActive(false);
      } else {
        throw new Error("Failed to save profile");
      }
    } catch (error) {
      console.error("Error saving profile:", error);
      toast.error(`Error saving profile: ${error.message || "Unknown error"}`, {
        position: "top-center",
        autoClose: 3000,
      });
    } finally {
      setIsProfileSaving(false);
    }
  };

  // edit Custom links
  const handleBringEditedCustomLink = async (link) => {
    setEditedCustomLink(link);
    setEditCustomSectionVisible(true);
    // Switch to links tab when editing from mobile
    if (isMobile) {
      setActiveTab(1);
    }
  };

  const handleDeleteContact = async (contactId) => {
    if (!window.confirm("Are you sure you want to delete this contact?")) {
      return;
    }

    console.log("Attempting to delete contact with ID:", contactId);
    try {
      const response = await DeleteContact(contactId);
      console.log("Delete response:", response);
      if (response) {
        toast.success("Contact deleted successfully", {
          position: "top-center",
          autoClose: 1000,
        });
        fetchProfile();
      } else {
        toast.error("No response from server", {
          position: "top-center",
          autoClose: 1000,
        });
      }
    } catch (error) {
      console.error("Error deleting contact:", error);
      toast.error(`Error deleting contact: ${error.message || error}`, {
        position: "top-center",
        autoClose: 3000,
      });
    }
  };

  const handleEditContact = (contact) => {
    // Set the contact being edited
    setEditingContact(contact);

    // Open the appropriate dialog based on contact category
    if (contact.Category === "Phone" || contact.Category === "PhoneNumber") {
      setOpenPhoneDialog(true);
    } else if (contact.Category === "Gmail" || contact.Category === "Email") {
      setOpenEmailDialog(true);
    } else if (contact.Category === "WhatsApp") {
      setOpenWhatsAppDialog(true);
    }

    // Switch to links tab when editing from mobile
    if (isMobile) {
      setActiveTab(1);
    }
  };

  const handleEditedCustomLinkChange = async (event) => {
    const { name, value } = event.target;
    setEditedCustomLink((prevLink) => ({
      ...prevLink,
      [name]: value,
    }));
  };

  const handleCustomLinkPhotoSelectEdit = (photoDataUrl) => {
    setEditedCustomLink((prevLink) => ({
      ...prevLink,
      Icon: photoDataUrl,
    }));
    setValidationStatus({
      ...validationStatus,
      photo: photoDataUrl !== null,
    });
  };

  const handleCustomLinkEdit = async () => {
    setIsCustomLinkSaving(true);
    try {
      if (!isValidCustomURL(editedCustomLink.LinkUrl)) {
        toast.error("Invalid URL format", {
          position: "top-center",
          autoClose: 2000,
        });
        return;
      }

      if (editedCustomLink.Title === "") {
        toast.error("Title can't be empty", {
          position: "top-center",
          autoClose: 2000,
        });
        return;
      }

      await EditCustomLink(editedCustomLink);

      setEditedCustomLink({
        Id: 0,
        ProfileId: 0,
        LinkUrl: "",
        Title: "",
      });

      setEditCustomSectionVisible(false);

      toast.success("Link edited", {
        position: "top-center",
        autoClose: 1000,
      });

      fetchCustomLinksData();
    } catch (error) {
      toast.error(error, {
        position: "top-center",
        autoClose: 1000,
      });
      console.error("Error saving Custom link data:", error.message);
    } finally {
      setIsCustomLinkSaving(false);
    }
  };

  // edit Social link
  const handleBringEditedSocialLink = async (link) => {
    setEditedSocialLink(link);
    setEditSocialSectionVisible(true);
    // Switch to links tab when editing from mobile
    if (isMobile) {
      setActiveTab(1);
    }
  };

  const handleEditedSocialLinkChange = async (event) => {
    const { name, value } = event.target;
    setEditedSocialLink((prevLink) => ({
      ...prevLink,
      [name]: value,
    }));
  };

  const handleSocialLinkEdit = async () => {
    setIsSocialLinkSaving(true);
    try {
      if (!isValidURL(editedSocialLink.LinkUrl)) {
        toast.error("Invalid URL format", {
          position: "top-center",
          autoClose: 2000,
        });
        return;
      }

      if (editedSocialLink.Title === "") {
        toast.error("Title can't be empty", {
          position: "top-center",
          autoClose: 2000,
        });
        return;
      }

      const regex = /^(?:https?:\/\/)?(?:www\.)?([^\/.]+)/i;
      const matches = editedSocialLink.LinkUrl.match(regex);
      const domain = matches ? matches[1] : null;

      if (domain !== editedSocialLink.Category.toLowerCase()) {
        toast.error("You cant change the category of the link", {
          position: "top-center",
          autoClose: 2000,
        });
        return;
      }

      await EditSocialLink(editedSocialLink);

      setEditedSocialLink({
        Id: 0,
        ProfileId: 0,
        Title: "",
        LinkUrl: "",
        Category: "",
        Color: "",
      });

      setEditSocialSectionVisible(false);
      setCategoryChosen(false);

      toast.success("Link edited", {
        position: "top-center",
        autoClose: 1000,
      });

      fetchSocialLinksData();
    } catch (error) {
      toast.error(error, {
        position: "top-center",
        autoClose: 1000,
      });
      console.error("Error saving Social link data:", error.message);
    } finally {
      setIsSocialLinkSaving(false);
    }
  };

  const handleDeleteCustomLink = async (Id) => {
    // Add confirmation dialog
    if (!window.confirm("Are you sure you want to delete this custom link?")) {
      return;
    }

    try {
      const response = await DeleteCustomLink(Id);
      if (response != null) {
        toast.success("Custom link deleted successfully", {
          position: "top-center",
          autoClose: 1000,
        });
        fetchCustomLinksData();
      }
    } catch (error) {
      toast.error("Failed to delete custom link", {
        position: "top-center",
        autoClose: 1000,
      });
      console.error("Error deleting custom link:", error);
    }
  };

  const handleDeleteSocialLink = async (Id) => {
    // Add confirmation dialog
    if (!window.confirm("Are you sure you want to delete this social link?")) {
      return;
    }

    try {
      const response = await DeleteSocialLink(Id);
      if (response != null) {
        toast.success("Social link deleted successfully", {
          position: "top-center",
          autoClose: 1000,
        });
        fetchSocialLinksData();
      }
    } catch (error) {
      toast.error("Failed to delete social link", {
        position: "top-center",
        autoClose: 1000,
      });
      console.error("Error deleting social link:", error);
    }
  };

  const fetchUserData = async () => {
    try {
      setUser({
        id: profile.id,
        email: profile.email,
        firstName: profile.firstName,
        lastName: profile.lastName,
        category: profile.category,
      });
      setProfile(profile.profile);

      setNewSocialLink((prevNewSocialLink) => ({
        ...prevNewSocialLink,
        ProfileId: profile.profile.id,
      }));

      setNewCustomLink((prevNewCustomLink) => ({
        ...prevNewCustomLink,
        ProfileId: profile.profile.id,
      }));
    } catch (error) {
      if (error.redirectToLogin) {
        navigate("/Login");
      }
    }
  };

  // Update local state when profile context changes
  useEffect(() => {
    if (profile && profile.profile) {
      fetchUserData();
    }
  }, [profile]);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    window.addEventListener("resize", handleResize);

    // Call handleResize immediately to set the initial state
    handleResize();

    // Fetch data when component mounts
    const fetchData = async () => {
      if (profile && profile.profile) {
        setIsLoading(true);
        try {
          await Promise.all([
            fetchUserData(),
            fetchSocialLinksData(),
            fetchCustomLinksData(),
          ]);
        } catch (error) {
          console.error("Error fetching profile data:", error);
        } finally {
          setIsLoading(false);
        }
      }
    };

    fetchData();

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  const fetchSocialLinksData = async () => {
    try {
      const response = await GetSocialLinks();
      setSocialLinks(response.data);
    } catch (error) {
      console.error("Error fetching social links data:", error);
    }
  };

  const fetchCustomLinksData = async () => {
    try {
      const response = await GetCustomLinks();
      setCustomLinks(response.data);
    } catch (error) {
      console.error("Error fetching social links data:", error);
    }
  };

  const handleClose = () => {
    setOpen(false);
    setNewSocialLink((prevNewSocialLink) => ({
      ...prevNewSocialLink,
      LinkUrl: "",
      Category: "",
      Title: "",
      Color: "",
    }));
  };

  const handleDone = async () => {
    if (!isValidURL(newSocialLink.LinkUrl)) {
      toast.error("Invalid URL format", {
        position: "top-center",
        autoClose: 2000,
      });
      return;
    }

    const response = await CreateSocialLink(newSocialLink);
    localStorage.setItem("isLinksCardVisible", "true");
    handleClose();
    if (response) {
      fetchSocialLinksData();
      toast.success("Social link created", {
        position: "top-center",
        autoClose: 1000,
      });
    } else {
      toast.error("Error while creating social link", {
        position: "top-center",
        autoClose: 1000,
      });
    }
  };

  const handleCustomLinkDone = async () => {
    if (!isValidCustomURL(newCustomLink.LinkUrl)) {
      toast.error("Invalid URL format", {
        position: "top-center",
        autoClose: 2000,
      });
      return;
    }

    const response = await CreateCustomLink(newCustomLink);

    if (response) {
      fetchCustomLinksData();
      toast.success("Custom link created", {
        position: "top-center",
        autoClose: 1000,
      });
      handleCustomLinkClose();
    } else {
      toast.error("Error while creating custom link", {
        position: "top-center",
        autoClose: 1000,
      });
    }
  };

  const handleCustomLinkPhotoSelect = (photoDataUrl) => {
    setNewCustomLink((prevData) => ({
      ...prevData,
      Icon: photoDataUrl,
    }));
    setValidationStatus({
      ...validationStatus,
      photo: photoDataUrl !== null,
    });
  };

  // Second Dialog
  const [openSecondDialog, setOpenSecondDialog] = useState(false);
  const [openPhoneDialog, setOpenPhoneDialog] = useState(false);
  const [openEmailDialog, setOpenEmailDialog] = useState(false);
  const [openWhatsAppDialog, setOpenWhatsAppDialog] = useState(false);
  const [editingContact, setEditingContact] = useState(null);

  const [openCategoryChooseDialog, setOpenCategoryChooseDialog] =
    useState(false);

  // Second Dialog Handlers
  const handleClickOpenSecond = () => {
    setOpenSecondDialog(true);
  };

  const handleCustomLinkClose = () => {
    setNewCustomLink((prevData) => ({
      ...prevData,
      Title: "",
      LinkUrl: "",
      Icon: "",
    }));
    setOpenSecondDialog(false);
  };

  //lazem create
  const [validationStatus, setValidationStatus] = useState({
    title: false,
    linkUrl: false,
    photo: false,
  });

  const isFormValid =
    newCustomLink.Title.trim() !== "" && newCustomLink.LinkUrl.trim() !== "";

  const isValidURL = (input) => {
    // Regular expression pattern to match URLs that start with http or https
    const urlPattern =
      /^(https?:\/\/)([\da-z.-]+)\.([a-z.]{2,6})(\/[^?#]*)?(\?[^#]*)?(#.*)?$/i;

    // Regular expression pattern to match phone numbers with 8 digits
    const phonePattern = /^\d{8}$/;

    // Check if the input matches URL pattern
    const isURL = urlPattern.test(input);

    // Check if the input matches phone number pattern
    const isPhoneNumber = phonePattern.test(input);

    // List of social media domains
    const socialMediaDomains = [
      "facebook.com",
      "twitter.com",
      "instagram.com",
      "linkedin.com",
      "instagram.com",
      "github.com",
      "tiktok.com",
    ];

    // Check if the input matches any social media domain and starts with http or https
    const isSocialMedia = socialMediaDomains.some((domain) =>
      new RegExp(`^https?:\/\/(?:www\.)?${domain}`, "i").test(input)
    );

    const isCategoryLike = new RegExp(
      `^https?:\/\/(?:www\.)?${newSocialLink.Category}`,
      "i"
    ).test(input);

    // Return true if it's a valid URL with http/https, a valid social media URL with http/https, OR a valid phone number
    return (isURL && isSocialMedia && isCategoryLike) || isPhoneNumber;
  };

  const isValidCustomURL = (input) => {
    // Regular expression pattern to match URLs that start with http or https
    const urlPattern =
      /^(https?:\/\/)([\da-z.-]+)\.([a-z.]{2,6})(\/[^?#]*)?(\?[^#]*)?(#.*)?$/i;

    // Check if the input matches URL pattern
    const isURL = urlPattern.test(input);

    return isURL;
  };

  const IconFromPlatform = (platform) => {
    switch (platform) {
      case "Twitter":
        return (
          <XIcon
            sx={{
              fontSize: "36px",
              cursor: "pointer",
              transition: "color 0.3s",
              "&:hover": {
                color: "#212121",
              },
            }}
          />
        );
      case "GitHub":
        return (
          <GitHubIcon
            sx={{
              fontSize: "36px",
              cursor: "pointer",
              transition: "color 0.3s",
              "&:hover": {
                color: "#212121",
              },
            }}
          />
        );
      case "Instagram":
        return (
          <InstagramIcon
            sx={{
              fontSize: "35px",
              cursor: "pointer",
              transition: "color 0.3s",
              "&:hover": {
                color: "#D81B60",
              },
            }}
          />
        );
      case "Facebook":
        return (
          <FacebookIcon
            sx={{
              fontSize: "35px",
              cursor: "pointer",
              transition: "color 0.3s",
              "&:hover": {
                color: "#5892d0",
              },
            }}
          />
        );
      case "LinkedIn":
        return (
          <LinkedInIcon
            sx={{
              fontSize: "35px",
              cursor: "pointer",
              transition: "color 0.3s",
              "&:hover": {
                color: "#00b9f1",
              },
            }}
          />
        );
      case "TikTok":
        return (
          <TikTokIcon
            icon="fab:tiktok"
            sx={{
              fontSize: "35px",
              cursor: "pointer",
              transition: "color 0.3s",
              "&:hover": {
                color: "#000",
              },
            }}
          />
        );
      case "Phone":
        return (
          <PhoneIcon
            sx={{
              fontSize: "35px",
              cursor: "pointer",
              transition: "color 0.3s",
              "&:hover": {
                color: "#4CAF50",
              },
            }}
          />
        );

      case "Gmail":
      case "Email":
        return (
          <EmailIcon
            sx={{
              fontSize: "35px",
              cursor: "pointer",
              transition: "color 0.3s",
              "&:hover": {
                color: "#EA4335",
              },
            }}
          />
        );
      case "WhatsApp":
        return (
          <WhatsAppIcon
            sx={{
              fontSize: "35px",
              cursor: "pointer",
              transition: "color 0.3s",
              "&:hover": {
                color: "#25D366",
              },
            }}
          />
        );
      default:
        return null;
    }
  };

  return (
    <Container
      sx={{
        "@media (min-width: 900px)": {
          paddingRight: "0", // Remove default padding to prevent overlap with sidebar
        },
      }}
    >
      <Helmet>
        <title>IDigics | Profile</title>
        <meta
          name="description"
          content={`Manage your IDigics profile${
            User.firstName && User.lastName
              ? ` - ${User.firstName} ${User.lastName}`
              : ""
          }${
            Profile.occupation ? `, ${Profile.occupation}` : ""
          }. Update your social links, contact information, and professional details.`}
        />

        {/* Open Graph meta tags */}
        <meta
          property="og:title"
          content={`${
            User.firstName && User.lastName
              ? `${User.firstName} ${User.lastName} | `
              : ""
          }IDigics Profile`}
        />
        <meta
          property="og:description"
          content={`Manage your IDigics profile${
            User.firstName && User.lastName
              ? ` - ${User.firstName} ${User.lastName}`
              : ""
          }${
            Profile.occupation ? `, ${Profile.occupation}` : ""
          }. Update your social links, contact information, and professional details.`}
        />
        <meta property="og:type" content="profile" />
        <meta property="og:url" content={window.location.href} />
        {Profile.profilePicture && (
          <meta property="og:image" content={Profile.profilePicture} />
        )}
        <meta property="og:site_name" content="IDigics" />

        {/* Twitter Card meta tags */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta
          name="twitter:title"
          content={`${
            User.firstName && User.lastName
              ? `${User.firstName} ${User.lastName} | `
              : ""
          }IDigics Profile`}
        />
        <meta
          name="twitter:description"
          content={`Manage your IDigics profile${
            User.firstName && User.lastName
              ? ` - ${User.firstName} ${User.lastName}`
              : ""
          }${
            Profile.occupation ? `, ${Profile.occupation}` : ""
          }. Update your social links, contact information, and professional details.`}
        />
        {Profile.profilePicture && (
          <meta name="twitter:image" content={Profile.profilePicture} />
        )}

        {/* Additional profile-specific meta tags */}
        {User.firstName && (
          <meta property="profile:first_name" content={User.firstName} />
        )}
        {User.lastName && (
          <meta property="profile:last_name" content={User.lastName} />
        )}
        {Profile.userName && (
          <meta property="profile:username" content={Profile.userName} />
        )}
      </Helmet>
      {isLoading ? (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{
            duration: 0.5,
            ease: "easeOut",
          }}
        >
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              minHeight: "400px",
              flexDirection: "column",
              gap: 2,
            }}
          >
            <CircularProgress size={60} />
            <Typography variant="h6" color="textSecondary">
              Loading profile data...
            </Typography>
          </Box>
        </motion.div>
      ) : (
        <>
          {/* Tabs at the top */}
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              marginBottom: "45px",
            }}
          >
            <Tabs
              value={activeTab}
              onChange={handleTabChange}
              aria-label="Account tabs"
            >
              <Tab label="Appearance" />
              {/* <Tab label="About (Beta)" /> */}
              <Tab label="Links" />
            </Tabs>
          </Box>
          <Grid container spacing={2} sx={{ marginTop: "-50px" }}>
            <Grid
              item
              xs={12}
              md={9}
              sx={{
                "@media (min-width: 900px) and (max-width: 1200px)": {
                  maxWidth: "calc(100vw - 320px)", // Account for sidebar + margins
                  paddingRight: "20px",
                },
                "@media (min-width: 1201px)": {
                  maxWidth: "calc(75vw - 40px)",
                  paddingRight: "20px",
                },
                "@media (max-width: 899px)": {
                  maxWidth: "100%",
                  paddingRight: "0",
                },
              }}
            >
              {User.category && (
                <Card sx={{ p: 3, marginBottom: "30px", position: "relative" }}>
                  <Typography
                    variant="overline"
                    sx={{
                      mb: 3,
                      display: "block",
                      color: "text.secondary",
                    }}
                  >
                    Your Plan
                  </Typography>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      gap: 2,
                      mb: 2,
                    }}
                  >
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        width: 60,
                        height: 60,
                        borderRadius: "12px",
                        background:
                          "linear-gradient(135deg, #ff715b20 0%, #e65d4710 100%)",
                        border: "2px solid #ff715b30",
                        boxShadow: "0 8px 32px #ff715b20",
                      }}
                    >
                      {getBundleIcon(User.category)}
                    </Box>
                    <Typography variant="h4">{User.category}</Typography>
                  </Box>
                  {User.category !== "Freelance" &&
                    User.category !== "Enterprise" && (
                      <Box
                        sx={{
                          mt: { xs: 2, sm: 0 },
                          position: { sm: "absolute" },
                          top: { sm: 24 },
                          right: { sm: 24 },
                        }}
                      >
                        <Button
                          size="small"
                          variant="outlined"
                          onClick={() => {
                            navigate("/admin/bundles");
                          }}
                        >
                          Upgrade plan
                        </Button>
                      </Box>
                    )}
                </Card>
              )}
              {activeTab === 0 && (
                <Appearance
                  Profile={Profile}
                  User={User}
                  isSaveButtonActive={isSaveButtonActive}
                  setIsSaveButtonActive={setIsSaveButtonActive}
                  isProfileSaving={isProfileSaving}
                  handlePhotoSelect={handlePhotoSelect}
                  handleProfileChange={handleProfileChange}
                  handleUserChange={handleUserChange}
                  handleSave={handleSave}
                  handleIsSearchChange={handleSearchChange}
                  handleCoverPhotoSelect={handleCoverPhotoSelect}
                />
              )}
              {/* {activeTab === 1 && <About />} */}
              {activeTab === 1 && (
                <Grid container spacing={2}>
                  {/* Create Links */}
                  <Grid item xs={12} md={12}>
                    {/* edit Social */}
                    {editSocialSectionVisible && (
                      <Grid item xs={12} md={12} sx={{ marginBottom: "10px" }}>
                        <Card>
                          <CardHeader
                            title="Edit your links"
                            subheader="This is where you can edit your entire profile! Here, you can manage and edit your 'About' section."
                          />
                          <CardContent>
                            <Grid container spacing={2}>
                              <IconButton
                                sx={{
                                  position: "absolute",
                                  right: 8,
                                  top: 8,
                                }}
                                aria-label="close"
                                onClick={() => {
                                  setEditSocialSectionVisible(false);
                                  setCategoryChosen(false);
                                }}
                              >
                                <CloseIcon />
                              </IconButton>
                              <Grid item xs={9} md={7}>
                                <TextField
                                  name="Title"
                                  label="Type your link title"
                                  focused
                                  value={editedSocialLink.Title}
                                  sx={{ width: "100%" }}
                                  onChange={handleEditedSocialLinkChange}
                                />
                              </Grid>
                              <Grid item xs={12} md={12}>
                                <TextField
                                  name="LinkUrl"
                                  label="Type your link url"
                                  value={editedSocialLink.LinkUrl}
                                  focused
                                  sx={{ width: "100%" }}
                                  onChange={handleEditedSocialLinkChange}
                                />
                              </Grid>
                            </Grid>
                          </CardContent>
                          <Button
                            onClick={handleSocialLinkEdit}
                            color="primary"
                            variant="outlined"
                            disabled={isSocialLinkSaving}
                            sx={{
                              margin: "25px",
                              backgroundColor: "#ee705e",
                              color: "white",
                              "&:hover": {
                                color: "#ee705e",
                              },
                            }}
                          >
                            <span
                              style={{
                                marginRight: "10px",
                              }}
                            >
                              {isSocialLinkSaving
                                ? "Saving..."
                                : "Save your link"}
                            </span>
                            {isSocialLinkSaving ? (
                              <CircularProgress size={20} color="inherit" />
                            ) : (
                              <SaveIcon />
                            )}
                          </Button>

                          <Dialog
                            open={openCategoryChooseDialog}
                            onClose={() => {
                              setOpenCategoryChooseDialog(false);
                            }}
                          >
                            <DialogTitle color="primary">
                              Choose a website
                            </DialogTitle>

                            <DialogContent>
                              <Grid
                                sx={{
                                  display: "flex",
                                }}
                                container
                                spacing={2}
                              >
                                {socialLinks.map(({ platform }) => {
                                  let icon = IconFromPlatform(platform);
                                  return (
                                    <Grid
                                      item
                                      xs={3}
                                      sm={6}
                                      md={2}
                                      lg={2}
                                      sx={{
                                        display: "flex",
                                        justifyContent: "center",
                                        marginTop: "5px",
                                      }}
                                      key={platform}
                                    >
                                      <BootstrapTooltip
                                        title={platform}
                                        sx={{
                                          "& .MuiTooltip-tooltip": {
                                            fontSize: "13px",
                                          },
                                        }}
                                      >
                                        <Button
                                          variant="outlined"
                                          sx={{
                                            color: "rgba(20, 43, 58, 0.5)",
                                            borderColor:
                                              "rgba(20, 43, 58, 0.3)",
                                            height: "100%",
                                            padding: "15px 20px",
                                          }}
                                          onClick={() => {
                                            setEditedSocialLink((prevLink) => ({
                                              ...prevLink,
                                              Category: platform,
                                            }));
                                            setOpenCategoryChooseDialog(false);
                                            setCategoryChosen(true);
                                          }}
                                        >
                                          {icon}
                                        </Button>
                                      </BootstrapTooltip>
                                    </Grid>
                                  );
                                })}
                              </Grid>
                            </DialogContent>
                            <DialogActions></DialogActions>
                          </Dialog>
                        </Card>
                      </Grid>
                    )}
                    {/* edit Custom */}
                    {editCustomSectionVisible && (
                      <Grid
                        item
                        xs={12}
                        md={12}
                        sx={{ marginTop: "10px", marginBottom: "10px" }}
                      >
                        <Card>
                          <CardHeader
                            title="Edit your links"
                            subheader="Analyze the daily views to understand the trends and patterns in the number of views your content receives.Gain valuable insights into the most active days and make informed decisions based on this data."
                          />
                          <CardContent>
                            <Grid container spacing={2}>
                              <IconButton
                                sx={{
                                  position: "absolute",
                                  right: 8,
                                  top: 8,
                                }}
                                aria-label="close"
                                onClick={() => {
                                  setEditCustomSectionVisible(false);
                                }}
                              >
                                <CloseIcon />
                              </IconButton>
                              <Grid item xs={10} md={11}>
                                <TextField
                                  name="Title"
                                  label="Type your link title"
                                  value={editedCustomLink.Title}
                                  focused
                                  sx={{ width: "100%" }}
                                  onChange={handleEditedCustomLinkChange}
                                />
                              </Grid>
                              <Grid item xs={1} md={1}>
                                <Avatar
                                  style={{
                                    width: "3rem",
                                    height: "3rem",
                                  }}
                                  focused
                                  src={editedCustomLink.Icon}
                                />
                                <PhotoSelector
                                  onSelect={handleCustomLinkPhotoSelectEdit}
                                />
                              </Grid>
                              <Grid item xs={12} md={12}>
                                <TextField
                                  name="LinkUrl"
                                  label="Type your link url"
                                  value={editedCustomLink.LinkUrl}
                                  focused
                                  sx={{ width: "100%" }}
                                  onChange={handleEditedCustomLinkChange}
                                />
                              </Grid>
                            </Grid>
                          </CardContent>
                          <Button
                            onClick={handleCustomLinkEdit}
                            color="primary"
                            variant="outlined"
                            disabled={isCustomLinkSaving}
                            sx={{
                              margin: "25px",
                              backgroundColor: "#ee705e",
                              color: "white",
                              "&:hover": {
                                color: "#ee705e",
                              },
                            }}
                          >
                            <span
                              style={{
                                marginRight: "10px",
                              }}
                            >
                              {isCustomLinkSaving
                                ? "Saving..."
                                : "Save your link"}
                            </span>
                            {isCustomLinkSaving ? (
                              <CircularProgress size={20} color="inherit" />
                            ) : (
                              <SaveIcon />
                            )}
                          </Button>
                        </Card>
                      </Grid>
                    )}
                    {/* Custom Links */}
                    <Card
                      sx={{
                        background:
                          "linear-gradient(135deg, #ff715b08, #ff715b03)",
                        border: "1px solid #ff715b20",
                        boxShadow: "0 8px 32px rgba(255, 113, 91, 0.12)",
                        borderRadius: "16px",
                        overflow: "hidden",
                        position: "relative",
                        "&::before": {
                          content: '""',
                          position: "absolute",
                          top: 0,
                          left: 0,
                          right: 0,
                          height: "4px",
                          background:
                            "linear-gradient(90deg, #ff715b, #e65d47)",
                        },
                      }}
                    >
                      <CardHeader
                        title={
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              gap: 1,
                            }}
                          >
                            <Box
                              sx={{
                                width: 40,
                                height: 40,
                                borderRadius: "12px",
                                background:
                                  "linear-gradient(135deg, #ff715b, #e65d47)",
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                                boxShadow: "0 4px 16px rgba(255, 113, 91, 0.3)",
                              }}
                            >
                              <CreateIcon
                                sx={{ color: "white", fontSize: 20 }}
                              />
                            </Box>
                            <Typography
                              variant="h6"
                              sx={{ fontWeight: 700, color: "#212B36" }}
                            >
                              Create Custom Links
                            </Typography>
                          </Box>
                        }
                        subheader={
                          <Typography
                            variant="body2"
                            sx={{
                              color: "text.secondary",
                              mt: 1,
                              lineHeight: 1.6,
                              fontSize: "14px",
                              marginBottom: "14px",
                            }}
                          >
                            Design personalized links with custom icons and
                            titles. Perfect for showcasing your portfolio,
                            business, or any important links you want to share.
                          </Typography>
                        }
                        sx={{ pb: 1 }}
                      />
                      <CardContent sx={{ pt: 0 }}>
                        <Grid container spacing={1}>
                          <Grid item xs={12} md={12}>
                            <Button
                              size="large"
                              fullWidth
                              variant="outlined"
                              sx={{
                                minHeight: "80px",
                                background:
                                  "linear-gradient(135deg, #ff715b08, #ff715b03)",
                                border: "2px dashed #ff715b40",
                                borderRadius: "20px",
                                color: "#ff715b",
                                fontSize: "16px",
                                fontWeight: "600",
                                textTransform: "none",
                                position: "relative",
                                overflow: "hidden",
                                transition:
                                  "all 0.4s cubic-bezier(0.4, 0, 0.2, 1)",
                                "&::before": {
                                  content: '""',
                                  position: "absolute",
                                  top: 0,
                                  left: "-100%",
                                  width: "100%",
                                  height: "100%",
                                  background:
                                    "linear-gradient(90deg, transparent, rgba(255, 113, 91, 0.1), transparent)",
                                  transition: "left 0.6s ease",
                                },
                                "&:hover": {
                                  background:
                                    "linear-gradient(135deg, #ff715b15, #ff715b08)",
                                  border: "2px solid #ff715b",
                                  transform: "translateY(-3px) scale(1.02)",
                                  boxShadow:
                                    "0 12px 35px rgba(255, 113, 91, 0.3)",
                                  "&::before": {
                                    left: "100%",
                                  },
                                },
                                "&:active": {
                                  transform: "translateY(-1px) scale(1.01)",
                                },
                              }}
                              onClick={() => handleClickOpenSecond()}
                            >
                              <Box
                                sx={{
                                  display: "flex",
                                  alignItems: "center",
                                  gap: 2.5,
                                  position: "relative",
                                  zIndex: 1,
                                }}
                              >
                                <Box
                                  sx={{
                                    width: 48,
                                    height: 48,
                                    borderRadius: "12px",
                                    background:
                                      "linear-gradient(135deg, #ff715b, #e65d47)",
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "center",
                                    boxShadow:
                                      "0 4px 16px rgba(255, 113, 91, 0.4)",
                                    transition: "all 0.3s ease",
                                  }}
                                >
                                  <CreateIcon
                                    sx={{ fontSize: 24, color: "white" }}
                                  />
                                </Box>
                                <Box sx={{ textAlign: "left", flex: 1 }}>
                                  <Typography
                                    variant="h7"
                                    sx={{
                                      fontWeight: 700,
                                      color: "#ff715b",
                                      fontSize: "18px",
                                      mb: 0.5,
                                    }}
                                  >
                                    Create Custom Link
                                  </Typography>
                                  <Typography
                                    variant="body2"
                                    sx={{
                                      color: "text.secondary",
                                      fontSize: "14px",
                                      lineHeight: 1.4,
                                    }}
                                  >
                                    Design personalized links
                                  </Typography>
                                </Box>
                              </Box>
                            </Button>
                          </Grid>
                        </Grid>
                      </CardContent>
                    </Card>
                    {/* Social Links */}
                    <Card
                      sx={{
                        marginTop: "20px",
                        background:
                          "linear-gradient(135deg, #667eea08, #764ba203)",
                        border: "1px solid #667eea20",
                        boxShadow: "0 8px 32px rgba(102, 126, 234, 0.12)",
                        borderRadius: "16px",
                        overflow: "hidden",
                        position: "relative",
                        "&::before": {
                          content: '""',
                          position: "absolute",
                          top: 0,
                          left: 0,
                          right: 0,
                          height: "4px",
                          background:
                            "linear-gradient(90deg, #667eea, #764ba2)",
                        },
                      }}
                    >
                      <CardHeader
                        title={
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              gap: 1,
                            }}
                          >
                            <Box
                              sx={{
                                width: 40,
                                height: 40,
                                borderRadius: "12px",
                                background:
                                  "linear-gradient(135deg, #667eea, #764ba2)",
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                                boxShadow:
                                  "0 4px 16px rgba(102, 126, 234, 0.3)",
                              }}
                            >
                              <LanguageIcon
                                sx={{ color: "white", fontSize: 20 }}
                              />
                            </Box>
                            <Typography
                              variant="h6"
                              sx={{ fontWeight: 700, color: "#212B36" }}
                            >
                              Social Platforms
                            </Typography>
                          </Box>
                        }
                        subheader={
                          <Typography
                            variant="body2"
                            sx={{
                              color: "text.secondary",
                              mt: 1,
                              lineHeight: 1.6,
                              fontSize: "14px",
                              marginBottom: "14px",
                            }}
                          >
                            Connect your social media accounts and professional
                            profiles. Choose from popular platforms and add your
                            custom titles and URLs to build your online
                            presence.
                          </Typography>
                        }
                        sx={{ pb: 1 }}
                      />
                      <CardContent sx={{ pt: 0 }}>
                        <Grid container spacing={2}>
                          {socialLinks.map(({ platform, color }) => {
                            let icon = IconFromPlatform(platform);
                            return (
                              <Grid
                                item
                                xs={6}
                                sm={4}
                                md={2.4}
                                lg={2.4}
                                sx={{
                                  display: "flex",
                                  justifyContent: "center",
                                }}
                                key={platform}
                              >
                                <BootstrapTooltip
                                  title={platform}
                                  sx={{
                                    "& .MuiTooltip-tooltip": {
                                      fontSize: "13px",
                                    },
                                  }}
                                >
                                  <Button
                                    variant="outlined"
                                    sx={{
                                      color: "rgba(20, 43, 58, 0.5)",
                                      borderColor: "rgba(20, 43, 58, 0.3)",
                                      height: "100%",
                                      padding: "15px 28px",
                                      minHeight: "100px",
                                      minWidth: "100px",
                                      boxShadow: `0 6px 24px ${color}18`,
                                      transition:
                                        "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
                                      display: "flex",
                                      flexDirection: "column",
                                      gap: 1,
                                      position: "relative",
                                      overflow: "hidden",
                                      "&::before": {
                                        content: '""',
                                        position: "absolute",
                                        top: 0,
                                        left: 0,
                                        right: 0,
                                        height: "3px",
                                        background: `linear-gradient(90deg, ${color}, ${color}80)`,
                                        opacity: 0,
                                        transition: "opacity 0.3s ease",
                                      },
                                      "&:hover": {
                                        background: `linear-gradient(135deg, ${color}20, ${color}08)`,
                                        border: `2px solid ${color}50`,
                                        transform:
                                          "translateY(-6px) scale(1.02)",
                                        boxShadow: `0 12px 40px ${color}30`,
                                        "&::before": {
                                          opacity: 1,
                                        },
                                      },
                                    }}
                                    onClick={() =>
                                      handleClickOpen(
                                        "Customize your link",
                                        platform,
                                        color
                                      )
                                    }
                                  >
                                    <Box sx={{ fontSize: 32, mb: 0.5 }}>
                                      {icon}
                                    </Box>
                                    <Typography
                                      variant="caption"
                                      sx={{
                                        fontWeight: 600,
                                        fontSize: "11px",
                                        textTransform: "none",
                                        opacity: 0.8,
                                      }}
                                    >
                                      {platform}
                                    </Typography>
                                  </Button>
                                </BootstrapTooltip>
                              </Grid>
                            );
                          })}
                        </Grid>
                        {/* Contact Links Section Header */}
                        <Box sx={{ my: 4 }}>
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              gap: 2,
                              mb: 2,
                            }}
                          >
                            <Box
                              sx={{
                                width: 32,
                                height: 32,
                                borderRadius: "8px",
                                background:
                                  "linear-gradient(135deg, #25D366, #128C7E)",
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                                boxShadow: "0 4px 12px rgba(37, 211, 102, 0.3)",
                              }}
                            >
                              <PhoneIcon
                                sx={{ color: "white", fontSize: 16 }}
                              />
                            </Box>
                            <Typography
                              variant="h6"
                              sx={{ fontWeight: 600, color: "#212B36" }}
                            >
                              Contact Information
                            </Typography>
                          </Box>
                          <Typography
                            variant="body2"
                            sx={{
                              color: "text.secondary",
                              mb: 3,
                              lineHeight: 1.6,
                              fontSize: "13px",
                            }}
                          >
                            Add your contact details to make it easy for people
                            to reach you directly.
                          </Typography>
                          <Divider
                            sx={{
                              borderColor: "rgba(102, 126, 234, 0.2)",
                              borderWidth: "1px",
                              borderStyle: "dashed",
                            }}
                          />
                        </Box>
                        <Grid container spacing={2}>
                          {PhoneLinks.map(({ platform, color }) => {
                            let icon = IconFromPlatform(platform);
                            return (
                              <Grid
                                item
                                xs={6}
                                sm={4}
                                md={2.4}
                                lg={2.4}
                                sx={{
                                  display: "flex",
                                  justifyContent: "center",
                                }}
                                key={platform}
                              >
                                <BootstrapTooltip
                                  title={platform}
                                  sx={{
                                    "& .MuiTooltip-tooltip": {
                                      fontSize: "13px",
                                    },
                                  }}
                                >
                                  <Button
                                    variant="outlined"
                                    sx={{
                                      color: "rgba(20, 43, 58, 0.5)",
                                      borderColor: "rgba(20, 43, 58, 0.3)",
                                      height: "100%",
                                      padding: "15px 20px",
                                      minHeight: "100px",
                                      minWidth: "100px",
                                      boxShadow: `0 6px 24px ${color}18`,
                                      transition:
                                        "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
                                      display: "flex",
                                      flexDirection: "column",
                                      gap: 1,
                                      position: "relative",
                                      overflow: "hidden",
                                      "&::before": {
                                        content: '""',
                                        position: "absolute",
                                        top: 0,
                                        left: 0,
                                        right: 0,
                                        height: "3px",
                                        background: `linear-gradient(90deg, ${color}, ${color}80)`,
                                        opacity: 0,
                                        transition: "opacity 0.3s ease",
                                      },
                                      "&:hover": {
                                        background: `linear-gradient(135deg, ${color}20, ${color}08)`,
                                        border: `2px solid ${color}50`,
                                        transform:
                                          "translateY(-6px) scale(1.02)",
                                        boxShadow: `0 12px 40px ${color}30`,
                                        "&::before": {
                                          opacity: 1,
                                        },
                                      },
                                    }}
                                    onClick={() => {
                                      setEditingContact(null);
                                      if (platform === "Phone") {
                                        setOpenPhoneDialog(true);
                                      } else if (platform === "Email") {
                                        setOpenEmailDialog(true);
                                      } else if (platform === "WhatsApp") {
                                        setOpenWhatsAppDialog(true);
                                      }
                                    }}
                                  >
                                    <Box sx={{ fontSize: 32, mb: 0.5 }}>
                                      {icon}
                                    </Box>
                                    <Typography
                                      variant="caption"
                                      sx={{
                                        fontWeight: 600,
                                        fontSize: "11px",
                                        textTransform: "none",
                                        opacity: 0.8,
                                      }}
                                    >
                                      {platform}
                                    </Typography>
                                  </Button>
                                </BootstrapTooltip>
                              </Grid>
                            );
                          })}
                        </Grid>
                      </CardContent>
                    </Card>
                  </Grid>

                  <Dialog
                    open={openSecondDialog}
                    onClose={handleCustomLinkClose}
                  >
                    <DialogTitle color="primary">
                      Create your custom link
                    </DialogTitle>

                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                    >
                      <div>
                        <Avatar
                          style={{
                            width: "5rem",
                            height: "5rem",
                          }}
                          src={newCustomLink.Icon}
                          alt="User Profile Photo"
                        />
                        <PhotoSelector
                          onSelect={handleCustomLinkPhotoSelect}
                          error={
                            newCustomLink.Icon === null &&
                            validationStatus.photo
                          }
                          helperText={
                            newCustomLink.Icon === null
                              ? "Photo is required"
                              : ""
                          }
                        />
                      </div>
                    </div>

                    <DialogContent>
                      <TextField
                        autoFocus
                        margin="dense"
                        name="Title"
                        label="Title"
                        type="text"
                        fullWidth
                        required
                        color="primary"
                        value={newCustomLink.Title}
                        onChange={handleNewCustomLinkChange}
                        error={
                          newCustomLink.Title.trim() === "" &&
                          validationStatus.title
                        }
                        helperText={
                          newCustomLink.Title.trim() === ""
                            ? "Title is required"
                            : ""
                        }
                      />

                      <TextField
                        name="LinkUrl"
                        margin="dense"
                        label="Your link"
                        type="url"
                        fullWidth
                        required
                        value={newCustomLink.LinkUrl}
                        onChange={handleNewCustomLinkChange}
                        error={
                          newCustomLink.LinkUrl.trim() === "" &&
                          validationStatus.linkUrl
                        }
                        helperText={
                          newCustomLink.LinkUrl.trim() === ""
                            ? "URL is required"
                            : ""
                        }
                      />
                      {/* Hints and Tips Section */}
                      <Box
                        mt={2}
                        p={2}
                        sx={{
                          backgroundColor: "#f0f0f0",
                          borderRadius: "5px",
                        }}
                      >
                        <Typography variant="subtitle1" color="textPrimary">
                          Tips for Creating Your Custom Link
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          - Ensure your link is correctly formatted, e.g.,
                          "https://www.facebook.com/yourprofile"
                        </Typography>
                      </Box>
                    </DialogContent>
                    <DialogActions>
                      <Button onClick={handleCustomLinkClose}>Cancel</Button>
                      <Button
                        onClick={handleCustomLinkDone}
                        disabled={!isFormValid}
                      >
                        Done
                      </Button>
                    </DialogActions>
                  </Dialog>
                  <Dialog open={open} onClose={handleClose}>
                    <DialogTitle>Create your social link</DialogTitle>
                    <DialogContent>
                      <TextField
                        name="Title"
                        autoFocus
                        margin="dense"
                        label="Title"
                        type="url"
                        fullWidth
                        required
                        value={newSocialLink.Title}
                        onChange={handleNewSocialLinkChange}
                        helperText={
                          newSocialLink.Title === "" ? "Title is required" : ""
                        }
                      />

                      <TextField
                        name="LinkUrl"
                        margin="dense"
                        id="linkUrl"
                        label="Url"
                        type="url"
                        fullWidth
                        required
                        value={newSocialLink.LinkUrl}
                        onChange={handleNewSocialLinkChange}
                        helperText={
                          newSocialLink.LinkUrl === "" ? "URL is required" : ""
                        }
                      />
                      {/* Hints and Tips Section */}
                      <Box
                        mt={2}
                        p={2}
                        sx={{
                          backgroundColor: "#f0f0f0",
                          borderRadius: "5px",
                        }}
                      >
                        <Typography variant="subtitle1" color="textPrimary">
                          Tips for Creating Predefined Link
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          - Ensure your link is correctly formatted, e.g.,
                          https://www.facebook.com/yourprofile
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          - Only links from social media platforms like
                          Facebook, Twitter, Instagram, LinkedIn, GitHub, and
                          TikTok are accepted.
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          - For phone numbers, simply enter an 8-digit number
                          without spaces or symbols.
                        </Typography>
                      </Box>
                    </DialogContent>
                    <DialogActions>
                      <Button onClick={handleClose}>Cancel</Button>
                      <Button
                        onClick={handleDone}
                        disabled={
                          newSocialLink.Title === "" ||
                          newSocialLink.LinkUrl === ""
                        }
                      >
                        Done
                      </Button>
                    </DialogActions>
                  </Dialog>
                  <PhoneLinkDialog
                    setOpenPhoneDialog={setOpenPhoneDialog}
                    openPhoneDialog={openPhoneDialog}
                    Id={profile.id}
                    editingContact={editingContact}
                    fetchProfile={fetchProfile}
                    clearEditingContact={() => setEditingContact(null)}
                  />
                  <EmailLinkDialog
                    setOpenEmailDialog={setOpenEmailDialog}
                    openEmailDialog={openEmailDialog}
                    Id={profile.id}
                    editingContact={editingContact}
                    fetchProfile={fetchProfile}
                    clearEditingContact={() => setEditingContact(null)}
                  />
                  <WhatsAppLinkDialog
                    setOpenWhatsAppDialog={setOpenWhatsAppDialog}
                    openWhatsAppDialog={openWhatsAppDialog}
                    Id={profile.id}
                    editingContact={editingContact}
                    fetchProfile={fetchProfile}
                    clearEditingContact={() => setEditingContact(null)}
                  />
                  <Grid item xs={12} md={12}>
                    <Card
                      sx={{
                        display: isVisible ? "flex" : "none",
                        marginTop: "20px",
                      }}
                    >
                      <CardContent>
                        <IconButton
                          sx={{
                            position: "absolute",
                            right: 8,
                            top: 8,
                          }}
                          aria-label="close"
                          onClick={() => {
                            setIsVisible(false);
                            localStorage.setItem("isLinksCardVisible", "false");
                          }}
                        >
                          <CloseIcon />
                        </IconButton>
                        <Typography gutterBottom variant="h6" component="div">
                          Create Your Custom Link!
                        </Typography>
                        <List>
                          <ListItem>
                            <ListItemIcon>
                              <CheckCircleOutlineIcon
                                sx={{
                                  fontSize: "20px",
                                }}
                              />
                            </ListItemIcon>
                            <ListItemText>
                              <Typography
                                sx={{
                                  fontSize: "16px",
                                }}
                              >
                                Automate content with dynamic feeds and images.
                              </Typography>
                            </ListItemText>
                          </ListItem>
                          <ListItem>
                            <ListItemIcon>
                              <CheckCircleOutlineIcon
                                sx={{
                                  fontSize: "20px",
                                }}
                              />
                            </ListItemIcon>
                            <ListItemText>
                              <Typography
                                sx={{
                                  fontSize: "16px",
                                }}
                              >
                                Use your own domain to boost branding.
                              </Typography>
                            </ListItemText>
                          </ListItem>
                          <ListItem>
                            <ListItemIcon>
                              <CheckCircleOutlineIcon
                                sx={{
                                  fontSize: "20px",
                                }}
                              />
                            </ListItemIcon>
                            <ListItemText>
                              <Typography
                                sx={{
                                  fontSize: "16px",
                                }}
                              >
                                Access analytics to improve your strategy.
                              </Typography>
                            </ListItemText>
                          </ListItem>
                          <ListItem>
                            <ListItemIcon>
                              <CheckCircleOutlineIcon
                                sx={{
                                  fontSize: "20px",
                                }}
                              />
                            </ListItemIcon>
                            <ListItemText>
                              <Typography
                                sx={{
                                  fontSize: "16px",
                                }}
                              >
                                Unlock premium features for more engagement.
                              </Typography>
                            </ListItemText>
                          </ListItem>
                        </List>
                        <Button
                          variant="contained"
                          onClick={() => handleClickOpenSecond()}
                        >
                          Get Started
                        </Button>
                      </CardContent>
                      <Box
                        sx={{
                          objectFit: "cover",
                          width: "50%",
                          display: {
                            xs: "none",
                            sm: "block",
                          },
                        }}
                      >
                        <img src="https://linktr.ee/_gatsby/image/1f7e31106ab8fd6cf4d62970cef6fec5/5dd82ff0dad9ac8464af794a9dc6bbeb/lsp_16x9-linktree-edm-release-may-2020-hero-socials-1_260520-024625.png?u=https%3A%2F%2Fapi.blog.production.linktr.ee%2Fwp-content%2Fuploads%2F2020%2F05%2Flsp_16x9-linktree-edm-release-may-2020-hero-socials-1_260520-024625.png&a=w%3D364%26h%3D449%26fit%3Dcrop%26crop%3Dcenter%26fm%3Dpng%26q%3D75&cd=4f02ff65d6cf6e346076e548f1a232da" />
                      </Box>
                    </Card>
                  </Grid>
                </Grid>
              )}
            </Grid>
            {!isMobile && (
              <Grid item xs={12} md={3}>
                {/* Profile */}
                {activeTab === 0 && (
                  <Box
                    sx={{
                      "@media (min-width: 900px) and (max-width: 1200px)": {
                        maxHeight: "100vh",
                        width: "280px",
                        overflowY: "auto",
                        position: "fixed",
                        right: "10px",
                        top: "100px",
                        zIndex: 1000,
                      },
                      "@media (max-width: 899px)": {
                        display: "none",
                      },
                    }}
                  >
                    <AppProfileCard
                      title="Profile"
                      subheader="Here is your profile"
                      SocialLinks={SocialLinks}
                      CustomLinks={CustomLinks}
                      User={User}
                      Profile={Profile}
                    />
                  </Box>
                )}
                {activeTab === 1 && (
                  <Box
                    sx={{
                      "@media (min-width: 900px) and (max-width: 1200px)": {
                        maxHeight: "550px",
                        width: "280px",
                        overflowY: "auto",
                        position: "fixed",
                        right: "10px",
                        top: "100px",
                        zIndex: 1000,
                      },
                      "@media (max-width: 899px)": {
                        display: "none", // Hide on smaller screens to prevent overlap
                      },
                    }}
                  >
                    <Box>
                      {/* social links */}
                      <AppLinksByProfile
                        title="Social links"
                        subheader="Here are your social links"
                        type="socialLinks"
                        list={SocialLinks.map(
                          ({ title, linkUrl, category, id, profileId }) => {
                            let iconn;
                            let color;
                            switch (category) {
                              case "Twitter":
                                iconn = <XIcon />;
                                color = "#43aff1";
                                break;
                              case "GitHub":
                                iconn = <GitHubIcon />;
                                color = "#212121";
                                break;
                              case "Instagram":
                                iconn = <InstagramIcon />;
                                color = "#c32aa3";
                                break;
                              case "Facebook":
                                iconn = <FacebookIcon />;
                                color = "#5892d0";
                                break;
                              case "LinkedIn":
                                iconn = <LinkedInIcon />;
                                color = "#00b9f1";
                                break;
                              case "TikTok":
                                iconn = <TikTokIcon icon="fab:tiktok" />;
                                color = "#000000";
                                break;
                              case "PhoneNumber":
                                iconn = <PhoneIcon />;
                                color = "#212121";
                                break;
                              default:
                                iconn = null;
                                color = "#ffffff";
                            }
                            return {
                              Id: id,
                              Title: title,
                              LinkUrl: linkUrl,
                              Color: color,
                              Icon: iconn,
                              ProfileId: profileId,
                              Category: category,
                            };
                          }
                        )}
                        onDelete={handleDeleteSocialLink}
                        onEdit={handleBringEditedSocialLink}
                      />
                      {/* custom links */}
                      <AppLinksByProfile
                        title="Custom links"
                        subheader="Here are your custom links"
                        type="customLinks"
                        list={CustomLinks.map((link) => {
                          return {
                            Id: link.id,
                            ProfileId: link.profileId,
                            Title: link.title,
                            LinkUrl: link.linkUrl,
                            Icon: link.icon,
                          };
                        })}
                        onDelete={handleDeleteCustomLink}
                        onEdit={handleBringEditedCustomLink}
                      />
                      {/* Contact links */}
                      <AppLinksByProfile
                        title="Contact links"
                        subheader="Here are your contact links"
                        type="contactLinks"
                        list={
                          profile.contacts
                            ?.filter(
                              (contact) =>
                                contact.category === "Phone" ||
                                contact.category === "PhoneNumber" ||
                                contact.category === "Gmail" ||
                                contact.category === "Email" ||
                                contact.category === "WhatsApp"
                            )
                            .map((contact) => {
                              let iconClass;
                              let color;
                              switch (contact.category) {
                                case "Phone":
                                case "PhoneNumber":
                                  iconClass = "fas fa-phone";
                                  color = "#0d90e0";
                                  break;
                                case "Gmail":
                                case "Email":
                                  iconClass = "fab fa-google";
                                  color = "#EA4335";
                                  break;
                                case "WhatsApp":
                                  iconClass = "fab fa-whatsapp";
                                  color = "#25D366";
                                  break;
                                default:
                                  iconClass = "fas fa-phone";
                                  color = "#0d90e0";
                              }
                              return {
                                Id: contact.id,
                                Title: contact.title || contact.category,
                                LinkUrl: contact.contactInfo,
                                Color: color,
                                Icon: iconClass,
                                Category: contact.category,
                              };
                            }) || []
                        }
                        onDelete={handleDeleteContact}
                        onEdit={handleEditContact}
                      />
                    </Box>
                  </Box>
                )}
              </Grid>
            )}

            {isMobile && !isProfileCardVisible && (
              <Button
                disableRipple
                color="primary"
                onClick={() => setIsProfileCardVisible((prev) => !prev)}
                variant="contained"
                sx={{
                  margin: "10px 21px 20px ",
                  zIndex: 1000,
                  position: "fixed",
                  right: "1rem",
                  bottom: "1rem",
                  borderRadius: "50%",
                  height: "55px",
                  minWidth: "5px",
                }}
              >
                <PhoneIphoneIcon />
              </Button>
            )}

            <Dialog open={isProfileCardVisible} fullScreen>
              <DialogContent>
                {/* X button to close the dialog */}
                <IconButton
                  sx={{
                    position: "fixed",
                    top: 16,
                    right: 16,
                    zIndex: 9999,
                    backgroundColor: "rgba(255, 255, 255, 0.9)",
                    "&:hover": {
                      backgroundColor: "rgba(255, 255, 255, 1)",
                    },
                  }}
                  onClick={() => setIsProfileCardVisible(false)}
                  aria-label="close"
                >
                  <CloseIcon />
                </IconButton>
                <AppProfileCard
                  title="Profile"
                  subheader="Here is your profile"
                  SocialLinks={SocialLinks}
                  CustomLinks={CustomLinks}
                  User={User}
                  Profile={Profile}
                />
                <Box>
                  {/* social links */}
                  <AppLinksByProfile
                    title="Social links"
                    subheader="Here are your social links"
                    type="socialLinks"
                    list={SocialLinks.map(
                      ({ title, linkUrl, category, id, profileId }) => {
                        let iconn;
                        let color;
                        switch (category) {
                          case "Twitter":
                            iconn = <XIcon />;
                            color = "#43aff1";
                            break;
                          case "GitHub":
                          case "Phone":
                            iconn = <GitHubIcon />;
                            color = "#212121";
                            break;
                          case "Instagram":
                            iconn = <InstagramIcon />;
                            color = "#c32aa3";
                            break;
                          case "Facebook":
                            iconn = <FacebookIcon />;
                            color = "#5892d0";
                            break;
                          case "LinkedIn":
                            iconn = <LinkedInIcon />;
                            color = "#00b9f1";
                            break;
                          case "TikTok":
                            iconn = <Iconify icon="fab:tiktok" />;
                            color = "#000000";
                            break;
                          default:
                            iconn = null;
                            color = "#ffffff";
                        }
                        return {
                          Id: id,
                          Title: title,
                          LinkUrl: linkUrl,
                          Color: color,
                          Icon: iconn,
                          ProfileId: profileId,
                          Category: category,
                        };
                      }
                    )}
                    onDelete={handleDeleteSocialLink}
                    onEdit={handleBringEditedSocialLink}
                    ProfileCardVisible={setIsProfileCardVisible}
                  />
                  {/* custom links */}
                  <AppLinksByProfile
                    title="Custom links"
                    subheader="Here are your custom links"
                    type="customLinks"
                    list={CustomLinks.map((link) => {
                      return {
                        Id: link.id,
                        ProfileId: link.profileId,
                        Title: link.title,
                        LinkUrl: link.linkUrl,
                        Icon: link.icon,
                      };
                    })}
                    onDelete={handleDeleteCustomLink}
                    onEdit={handleBringEditedCustomLink}
                    ProfileCardVisible={setIsProfileCardVisible}
                  />
                  {/* Contact links */}
                  <AppLinksByProfile
                    title="Contact links"
                    subheader="Here are your contact links"
                    type="contactLinks"
                    list={
                      profile.contacts
                        ?.filter(
                          (contact) =>
                            contact.category === "Phone" ||
                            contact.category === "PhoneNumber" ||
                            contact.category === "Gmail" ||
                            contact.category === "Email" ||
                            contact.category === "WhatsApp"
                        )
                        .map((contact) => {
                          let iconClass;
                          let color;
                          switch (contact.category) {
                            case "Phone":
                            case "PhoneNumber":
                              iconClass = "fas fa-phone";
                              color = "#0d90e0";
                              break;
                            case "Gmail":
                            case "Email":
                              iconClass = "fab fa-google";
                              color = "#EA4335";
                              break;
                            case "WhatsApp":
                              iconClass = "fab fa-whatsapp";
                              color = "#25D366";
                              break;
                            default:
                              iconClass = "fas fa-phone";
                              color = "#0d90e0";
                          }
                          return {
                            Id: contact.id,
                            Title: contact.title || contact.category,
                            LinkUrl: contact.contactInfo,
                            Color: color,
                            Icon: iconClass,
                            Category: contact.category,
                            ContactInfo: contact.contactInfo,
                          };
                        }) || []
                    }
                    onDelete={handleDeleteContact}
                    onEdit={handleEditContact}
                    ProfileCardVisible={setIsProfileCardVisible}
                  />
                </Box>
              </DialogContent>
            </Dialog>
          </Grid>

          {/* Mobile Sidebar - Shows below main content on small screens */}
          {activeTab === 1 && (
            <Box
              sx={{
                "@media (max-width: 899px)": {
                  display: "block",
                  mt: 3,
                  mb: 2,
                },
                "@media (min-width: 900px)": {
                  display: "none",
                },
              }}
            >
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Box>
                    {/* Social links for mobile */}
                    <AppLinksByProfile
                      title="Social links"
                      subheader="Here are your social links"
                      type="socialLinks"
                      list={SocialLinks.map(
                        ({ title, linkUrl, category, id, profileId }) => {
                          let iconn;
                          let color;
                          switch (category) {
                            case "Twitter":
                              iconn = <XIcon />;
                              color = "#43aff1";
                              break;
                            case "Instagram":
                              iconn = <InstagramIcon />;
                              color = "#e4405f";
                              break;
                            case "Facebook":
                              iconn = <FacebookIcon />;
                              color = "#3b5998";
                              break;
                            case "LinkedIn":
                              iconn = <LinkedInIcon />;
                              color = "#0077b5";
                              break;
                            case "TikTok":
                              iconn = <Iconify icon="fab:tiktok" />;
                              color = "#000000";
                              break;
                            case "YouTube":
                              iconn = <YouTubeIcon />;
                              color = "#ff0000";
                              break;
                            case "TikTok":
                              iconn = <Iconify icon="fab:tiktok" />;
                              color = "#000000";
                              break;
                            case "Snapchat":
                              iconn = <LanguageIcon />;
                              color = "#fffc00";
                              break;
                            case "Pinterest":
                              iconn = <PinterestIcon />;
                              color = "#bd081c";
                              break;
                            case "Reddit":
                              iconn = <RedditIcon />;
                              color = "#ff4500";
                              break;
                            case "Twitch":
                              iconn = <LanguageIcon />;
                              color = "#9146ff";
                              break;
                            case "Discord":
                              iconn = <LanguageIcon />;
                              color = "#7289da";
                              break;
                            case "Telegram":
                              iconn = <TelegramIcon />;
                              color = "#0088cc";
                              break;
                            case "WhatsApp":
                              iconn = <WhatsAppIcon />;
                              color = "#25d366";
                              break;
                            case "Spotify":
                              iconn = <SpotifyIcon />;
                              color = "#1db954";
                              break;
                            case "SoundCloud":
                              iconn = <SpotifyIcon />;
                              color = "#ff5500";
                              break;
                            case "Behance":
                              iconn = <LanguageIcon />;
                              color = "#1769ff";
                              break;
                            case "Dribbble":
                              iconn = <LanguageIcon />;
                              color = "#ea4c89";
                              break;
                            case "GitHub":
                              iconn = <GitHubIcon />;
                              color = "#333";
                              break;
                            case "Website":
                              iconn = <LanguageIcon />;
                              color = "#007bff";
                              break;
                            default:
                              iconn = <LanguageIcon />;
                              color = "#007bff";
                          }
                          return {
                            Id: id,
                            Title: title,
                            LinkUrl: linkUrl,
                            Color: color,
                            Icon: iconn,
                            Category: category,
                          };
                        }
                      )}
                      onDelete={handleDeleteSocialLink}
                      onEdit={handleBringEditedSocialLink}
                      ProfileCardVisible={setIsProfileCardVisible}
                    />

                    {/* Contact links for mobile */}
                    <AppLinksByProfile
                      title="Contact links"
                      subheader="Here are your contact links"
                      type="contactLinks"
                      list={
                        profile.contacts
                          ?.filter((contact) =>
                            [
                              "Gmail",
                              "Email",
                              "WhatsApp",
                              "PhoneNumber",
                            ].includes(contact.category)
                          )
                          .map((contact) => {
                            let iconClass;
                            let color;
                            switch (contact.category) {
                              case "Gmail":
                              case "Email":
                                iconClass = <EmailIcon />;
                                color = "#ea4335";
                                break;
                              case "WhatsApp":
                                iconClass = <WhatsAppIcon />;
                                color = "#25d366";
                                break;
                              case "PhoneNumber":
                                iconClass = <PhoneIcon />;
                                color = "#007bff";
                                break;
                              default:
                                iconClass = <PhoneIcon />;
                                color = "#007bff";
                            }
                            return {
                              Id: contact.id,
                              Title: contact.title || contact.category,
                              LinkUrl: contact.contactInfo,
                              Color: color,
                              Icon: iconClass,
                              Category: contact.category,
                            };
                          }) || []
                      }
                      onDelete={handleDeleteContact}
                      onEdit={handleEditContact}
                      ProfileCardVisible={setIsProfileCardVisible}
                    />
                  </Box>
                </Grid>
              </Grid>
            </Box>
          )}
        </>
      )}
    </Container>
  );
};

export default ProfileUser;
